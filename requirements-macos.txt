# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --output-file=requirements-macos.txt requirements.in
#
-e .[all]
    # via -[all]
absl-py==2.3.1
    # via
    #   dm-control
    #   dm-env
    #   dm-tree
    #   labmaze
    #   mujoco
accelerate==1.9.0
    # via lerobot
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.15
    # via fsspec
aiosignal==1.4.0
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
asttokens==3.0.0
    # via stack-data
async-timeout==5.0.1
    # via aiohttp
attrs==25.3.0
    # via
    #   aiohttp
    #   dm-tree
    #   jsonlines
    #   rerun-sdk
av==15.0.0
    # via lerobot
blinker==1.9.0
    # via flask
certifi==2025.7.14
    # via
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via pymunk
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   flask
    #   wandb
cloudpickle==3.1.1
    # via gymnasium
cmake==4.0.3
    # via lerobot
cmeel==0.57.3
    # via
    #   cmeel-assimp
    #   cmeel-boost
    #   cmeel-console-bridge
    #   cmeel-octomap
    #   cmeel-qhull
    #   cmeel-tinyxml2
    #   cmeel-urdfdom
    #   cmeel-zlib
    #   coal-library
    #   eigenpy
    #   eiquadprog
    #   pin
    #   placo
    #   rhoban-cmeel-jsoncpp
cmeel-assimp==5.4.3.1
    # via coal-library
cmeel-boost==1.87.0.1
    # via
    #   coal-library
    #   eigenpy
    #   eiquadprog
    #   pin
cmeel-console-bridge==*******
    # via cmeel-urdfdom
cmeel-octomap==1.10.0
    # via coal-library
cmeel-qhull==*******
    # via coal-library
cmeel-tinyxml2==10.0.0
    # via cmeel-urdfdom
cmeel-urdfdom==4.0.1
    # via pin
cmeel-zlib==1.3.1
    # via cmeel-assimp
coal-library==3.0.1
    # via pin
contourpy==1.3.2
    # via matplotlib
coverage[toml]==7.10.1
    # via pytest-cov
cycler==0.12.1
    # via matplotlib
datasets==3.6.0
    # via lerobot
debugpy==1.8.15
    # via lerobot
decorator==5.2.1
    # via ipython
deepdiff==8.5.0
    # via lerobot
diffusers==0.34.0
    # via lerobot
dill==0.3.8
    # via
    #   datasets
    #   multiprocess
distlib==0.4.0
    # via virtualenv
dm-control==1.0.14
    # via gym-aloha
dm-env==1.6
    # via dm-control
dm-tree==0.1.9
    # via
    #   dm-control
    #   dm-env
docopt==0.6.2
    # via num2words
draccus==0.10.0
    # via lerobot
dynamixel-sdk==3.7.31
    # via lerobot
eigenpy==3.10.3
    # via coal-library
einops==0.8.1
    # via lerobot
eiquadprog==1.2.9
    # via placo
exceptiongroup==1.3.0
    # via
    #   ipython
    #   pytest
executing==2.2.0
    # via stack-data
farama-notifications==0.0.4
    # via gymnasium
feetech-servo-sdk==1.0.0
    # via lerobot
filelock==3.18.0
    # via
    #   datasets
    #   diffusers
    #   huggingface-hub
    #   torch
    #   transformers
    #   virtualenv
flask==3.1.1
    # via lerobot
fonttools==4.59.0
    # via matplotlib
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec[http]==2025.3.0
    # via
    #   datasets
    #   huggingface-hub
    #   torch
gitdb==4.0.12
    # via gitpython
gitpython==3.1.45
    # via wandb
glfw==2.9.0
    # via
    #   dm-control
    #   mujoco
grpcio==1.73.1
    # via
    #   grpcio-tools
    #   lerobot
grpcio-tools==1.73.1
    # via lerobot
gym-aloha==0.1.1
    # via lerobot
gym-hil==0.1.10
    # via lerobot
gym-pusht==0.1.5
    # via lerobot
gym-xarm==0.1.1
    # via lerobot
gymnasium==0.29.1
    # via
    #   gym-aloha
    #   gym-hil
    #   gym-pusht
    #   gym-xarm
    #   gymnasium-robotics
    #   lerobot
    #   pettingzoo
gymnasium-robotics==1.2.4
    # via gym-xarm
hf-transfer==0.1.9
    # via huggingface-hub
hf-xet==1.1.5
    # via huggingface-hub
hidapi==0.14.0.post4
    # via
    #   gym-hil
    #   lerobot
huggingface-hub[cli,hf-transfer]==0.34.3
    # via
    #   accelerate
    #   datasets
    #   diffusers
    #   lerobot
    #   tokenizers
    #   transformers
identify==2.6.12
    # via pre-commit
idna==3.10
    # via
    #   requests
    #   yarl
imageio[ffmpeg]==2.37.0
    # via
    #   gym-aloha
    #   gym-hil
    #   gymnasium-robotics
    #   lerobot
    #   scikit-image
imageio-ffmpeg==0.6.0
    # via imageio
importlib-metadata==8.7.0
    # via diffusers
iniconfig==2.1.0
    # via pytest
inquirerpy==0.3.4
    # via huggingface-hub
ipython==8.37.0
    # via meshcat
ischedule==1.2.7
    # via placo
itsdangerous==2.2.0
    # via flask
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   flask
    #   gymnasium-robotics
    #   torch
jsonlines==4.0.0
    # via lerobot
kiwisolver==1.4.8
    # via matplotlib
labmaze==1.0.6
    # via dm-control
lazy-loader==0.4
    # via scikit-image
lxml==6.0.0
    # via dm-control
markupsafe==3.0.2
    # via
    #   flask
    #   jinja2
    #   werkzeug
matplotlib==3.10.5
    # via lerobot
matplotlib-inline==0.1.7
    # via ipython
mergedeep==1.3.4
    # via draccus
meshcat==0.3.2
    # via placo
mock-serial==0.0.1
    # via lerobot
mpmath==1.3.0
    # via sympy
mujoco==2.3.7
    # via
    #   dm-control
    #   gym-aloha
    #   gym-hil
    #   gym-xarm
    #   gymnasium-robotics
multidict==6.6.3
    # via
    #   aiohttp
    #   yarl
multiprocess==0.70.16
    # via datasets
mypy-extensions==1.1.0
    # via typing-inspect
networkx==3.4.2
    # via
    #   scikit-image
    #   torch
nodeenv==1.9.1
    # via pre-commit
num2words==0.5.14
    # via lerobot
numpy==2.2.6
    # via
    #   accelerate
    #   cmeel-boost
    #   contourpy
    #   datasets
    #   diffusers
    #   dm-control
    #   dm-env
    #   dm-tree
    #   gymnasium
    #   gymnasium-robotics
    #   imageio
    #   labmaze
    #   matplotlib
    #   meshcat
    #   mujoco
    #   opencv-python
    #   opencv-python-headless
    #   pandas
    #   pettingzoo
    #   rerun-sdk
    #   scikit-image
    #   scipy
    #   shapely
    #   tifffile
    #   torchvision
    #   transformers
opencv-python==*********
    # via gym-pusht
opencv-python-headless==*********
    # via lerobot
orderly-set==5.5.0
    # via deepdiff
packaging==25.0
    # via
    #   accelerate
    #   datasets
    #   huggingface-hub
    #   lazy-loader
    #   lerobot
    #   matplotlib
    #   pytest
    #   scikit-image
    #   transformers
    #   wandb
pandas==2.3.1
    # via
    #   datasets
    #   lerobot
parso==0.8.4
    # via jedi
pettingzoo==1.24.3
    # via gymnasium-robotics
pexpect==4.9.0
    # via ipython
pfzy==0.3.4
    # via inquirerpy
pillow==11.3.0
    # via
    #   diffusers
    #   imageio
    #   matplotlib
    #   meshcat
    #   rerun-sdk
    #   scikit-image
    #   torchvision
pin==3.4.0
    # via placo
placo==0.9.14
    # via lerobot
platformdirs==4.3.8
    # via
    #   virtualenv
    #   wandb
pluggy==1.6.0
    # via
    #   pytest
    #   pytest-cov
pre-commit==4.2.0
    # via lerobot
prompt-toolkit==3.0.51
    # via
    #   inquirerpy
    #   ipython
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
protobuf==6.31.0
    # via
    #   dm-control
    #   grpcio-tools
    #   lerobot
    #   wandb
psutil==7.0.0
    # via
    #   accelerate
    #   imageio
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pyarrow==21.0.0
    # via
    #   datasets
    #   rerun-sdk
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via wandb
pydantic-core==2.33.2
    # via pydantic
pygame==2.6.1
    # via
    #   gym-hil
    #   gym-pusht
    #   lerobot
pygments==2.19.2
    # via
    #   ipython
    #   pytest
pymunk==6.11.1
    # via
    #   gym-pusht
    #   lerobot
pyngrok==7.2.12
    # via meshcat
pynput==1.8.1
    # via
    #   gym-hil
    #   lerobot
pyobjc-core==11.1
    # via
    #   pyobjc-framework-applicationservices
    #   pyobjc-framework-cocoa
    #   pyobjc-framework-coretext
    #   pyobjc-framework-quartz
pyobjc-framework-applicationservices==11.1
    # via pynput
pyobjc-framework-cocoa==11.1
    # via
    #   pyobjc-framework-applicationservices
    #   pyobjc-framework-coretext
    #   pyobjc-framework-quartz
pyobjc-framework-coretext==11.1
    # via pyobjc-framework-applicationservices
pyobjc-framework-quartz==11.1
    # via
    #   pynput
    #   pyobjc-framework-applicationservices
    #   pyobjc-framework-coretext
pyopengl==3.1.9
    # via
    #   dm-control
    #   mujoco
pyparsing==3.2.3
    # via
    #   dm-control
    #   matplotlib
pyrealsense2-macosx==2.54.2
    # via lerobot
pyserial==3.5
    # via
    #   dynamixel-sdk
    #   feetech-servo-sdk
    #   lerobot
pytest==8.4.1
    # via
    #   lerobot
    #   pytest-cov
    #   pytest-timeout
pytest-cov==6.2.1
    # via lerobot
pytest-timeout==2.4.0
    # via lerobot
python-dateutil==2.9.0.post0
    # via
    #   matplotlib
    #   pandas
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   accelerate
    #   datasets
    #   draccus
    #   huggingface-hub
    #   pre-commit
    #   pyngrok
    #   pyyaml-include
    #   transformers
    #   wandb
pyyaml-include==1.4.1
    # via draccus
pyzmq==27.0.0
    # via
    #   lerobot
    #   meshcat
regex==2025.7.34
    # via
    #   diffusers
    #   transformers
requests==2.32.4
    # via
    #   datasets
    #   diffusers
    #   dm-control
    #   huggingface-hub
    #   transformers
    #   wandb
rerun-sdk==0.22.1
    # via lerobot
rhoban-cmeel-jsoncpp==*******
    # via placo
safetensors==0.5.3
    # via
    #   accelerate
    #   diffusers
    #   lerobot
    #   transformers
scikit-image==0.25.2
    # via
    #   gym-pusht
    #   lerobot
scipy==1.15.3
    # via
    #   dm-control
    #   scikit-image
sentry-sdk==2.34.1
    # via wandb
shapely==2.1.1
    # via gym-pusht
six==1.17.0
    # via
    #   pynput
    #   python-dateutil
smmap==5.0.2
    # via gitdb
stack-data==0.6.3
    # via ipython
sympy==1.14.0
    # via torch
termcolor==3.1.0
    # via lerobot
tifffile==2025.5.10
    # via scikit-image
tokenizers==0.21.4
    # via transformers
toml==0.10.2
    # via draccus
tomli==2.2.1
    # via
    #   cmeel
    #   coverage
    #   pytest
torch==2.7.1
    # via
    #   accelerate
    #   lerobot
    #   torchvision
torchcodec==0.5
    # via lerobot
torchvision==0.22.1
    # via lerobot
tornado==6.5.1
    # via meshcat
tqdm==4.67.1
    # via
    #   datasets
    #   dm-control
    #   huggingface-hub
    #   transformers
traitlets==5.14.3
    # via
    #   ipython
    #   matplotlib-inline
transformers==4.51.3
    # via lerobot
typing-extensions==4.14.1
    # via
    #   aiosignal
    #   exceptiongroup
    #   gymnasium
    #   huggingface-hub
    #   ipython
    #   multidict
    #   pydantic
    #   pydantic-core
    #   rerun-sdk
    #   torch
    #   typing-inspect
    #   typing-inspection
    #   wandb
typing-inspect==0.9.0
    # via draccus
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via pandas
u-msgpack-python==2.8.0
    # via meshcat
urllib3==2.5.0
    # via
    #   requests
    #   sentry-sdk
virtualenv==20.32.0
    # via pre-commit
wandb==0.21.0
    # via lerobot
wcwidth==0.2.13
    # via prompt-toolkit
werkzeug==3.1.3
    # via flask
wrapt==1.17.2
    # via dm-tree
xxhash==3.5.0
    # via datasets
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
