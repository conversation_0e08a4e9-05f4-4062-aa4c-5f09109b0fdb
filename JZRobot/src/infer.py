import time
import torch
import torchvision.transforms.v2 as v2
import sys
import os

# Add current directory to Python path (we're already in JZRobot/src)
sys.path.append(os.path.dirname(__file__))

# from lerobot.common.envs.utils import preprocess_observation
from lerobot.policies.act.modeling_act import ACTPolicy
from jzrobot.robots.YMbot_D import YMbotD, YMbotDConfig


def busy_wait(duration):
    """Busy wait for a specific duration."""
    if duration > 0:
        time.sleep(duration)


def preprocess_observation(observation, device):
    obs = {}
    image_transforms = v2.Compose([
                    v2.Resize((16 * 14, 22 * 14)),
                ])
    for name in observation:
        if "image" in name:
            observation[name] = observation[name].permute(2, 0, 1).contiguous()
            observation[name] = image_transforms(observation[name])
            observation[name] = observation[name].type(torch.float32) / 255
        if "depth" in name:
            continue
        obs[name] = observation[name].unsqueeze(0)
        obs[name] = obs[name].to(device)
    return obs

inference_time_s = 10000
fps = 30
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Create YMbot-D robot configuration
config = YMbotDConfig(
    id="ymbot_d",
    max_joint_change_deg=30.0,
    default_trajectory_time=0.2
)
robot = YMbotD(config)

if not robot.is_connected:
    robot.connect()
    robot.configure()

ckpt_path = "/home/<USER>/lerobot/outputs/train/turn_on_the_tap/checkpoints/0080000/pretrained_model"
policy = ACTPolicy.from_pretrained(ckpt_path, strict=False)
policy.to(device)
# robot.reset()
time.sleep(3)

for _ in range(inference_time_s * fps):
    start_time = time.perf_counter()

    # Read the follower state and access the frames from the cameras
    observation = robot.get_observation()
    print("observation", observation)
    observation = preprocess_observation(observation, device)
    observation = {
            key: observation[key].to(device, non_blocking=device.type == "cuda") for key in observation
        }

    # Compute the next action with the policy
    # based on the current observation
    # observation = get_observation(observation)
    with torch.inference_mode():
        action = policy.select_action(observation)
        
    # Remove batch dimension
    action = action.squeeze(0).to("cpu").numpy()
    if action[14] >= 0.35: action[14] = 1.0
    if action[15] >= 0.3: action[15] = 1.0
    # Move to cpu, if not already the case

    # Convert action array to robot action format
    action_dict = {
        "action.joint.position": action[:14].tolist(),  # First 14 elements for arm joints
        "action.gripper.position": action[14:16].tolist()  # Next 2 elements for grippers
    }

    # Order the robot to move
    print("action", action.shape)
    robot.send_action(action_dict)

    dt_s = time.perf_counter() - start_time
    busy_wait(1 / fps - dt_s)
# robot.reset()