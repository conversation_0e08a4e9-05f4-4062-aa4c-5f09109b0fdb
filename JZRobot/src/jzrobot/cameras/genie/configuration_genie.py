# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from dataclasses import dataclass, field

from ..configs import CameraConfig, ColorMode, Cv2Rotation


@CameraConfig.register_subclass("genie")
@dataclass
class GenieCameraConfig(CameraConfig):
    """Configuration class for Intel RealSense cameras.

    This class provides specialized configuration options for Intel RealSense cameras,
    including support for depth sensing and device identification via serial number or name.

    Attributes:
        fps: Requested frames per second for the color stream.
        width: Requested frame width in pixels for the color stream.
        height: Requested frame height in pixels for the color stream.
        color_mode: Color mode for image output (RGB or BGR). Defaults to RGB.
        use_depth: Whether to enable depth stream. Defaults to False.
        rotation: Image rotation setting (0°, 90°, 180°, or 270°). Defaults to no rotation.
        warmup_s: Time reading frames before returning from connect (in seconds)

    Note:
        - Either name or serial_number must be specified.
        - Depth stream configuration (if enabled) will use the same FPS as the color stream.
        - The actual resolution and FPS may be adjusted by the camera to the nearest supported mode.
        - For `fps`, `width` and `height`, either all of them need to be set, or none of them.
    """

    color_mode: ColorMode = ColorMode.RGB
    use_depth: bool = False
    rotation: Cv2Rotation = Cv2Rotation.NO_ROTATION
    warmup_s: int = 1
    camera_group: list[str] = field(default_factory=list)

    def __post_init__(self):
        if self.color_mode not in (ColorMode.RGB, ColorMode.BGR):
            raise ValueError(
                f"`color_mode` is expected to be {ColorMode.RGB.value} or {ColorMode.BGR.value}, but {self.color_mode} is provided."
            )

        if self.rotation not in (
            Cv2Rotation.NO_ROTATION,
            Cv2Rotation.ROTATE_90,
            Cv2Rotation.ROTATE_180,
            Cv2Rotation.ROTATE_270,
        ):
            raise ValueError(
                f"`rotation` is expected to be in {(Cv2Rotation.NO_ROTATION, Cv2Rotation.ROTATE_90, Cv2Rotation.ROTATE_180, Cv2Rotation.ROTATE_270)}, but {self.rotation} is provided."
            )

        values = (self.fps, self.width, self.height)
        if any(v is not None for v in values) and any(v is None for v in values):
            raise ValueError(
                "For `fps`, `width` and `height`, either all of them need to be set, or none of them."
            )
        
        for camera_name in self.camera_group:
            if camera_name not in ["head", "head_depth",  # head
                                   "hand_left", "hand_left_depth",  # hand_left
                                   "hand_right", "/camera/hand_right_depth",  # hand_right
                                   "/camera/head_center_fisheye", "/camera/head_left_fisheye", "/camera/head_right_fisheye",  # fisheye
                                   ]:
                raise ValueError(f"Invalid camera name: {camera_name}")

    def estimate_bandwidth_mbps(self) -> float:
        """Estimate total bandwidth requirement in Mbps."""
        # Assume 3 bytes per pixel for color cameras, 2 for depth
        total_bandwidth = 0

        for camera_name in self.camera_group:
            bytes_per_pixel = 2 if "depth" in camera_name else 3
            pixels_per_second = self.width * self.height * self.fps
            bytes_per_second = pixels_per_second * bytes_per_pixel
            bits_per_second = bytes_per_second * 8
            mbps = bits_per_second / (1024 * 1024)
            total_bandwidth += mbps

        return total_bandwidth

    def requires_separate_usb_hubs(self) -> bool:
        """Check if configuration requires separate USB hubs."""
        estimated_bandwidth = self.estimate_bandwidth_mbps()

        # USB 3.0 practical limit is around 400-500 Mbps per controller
        usb3_practical_limit = 400

        return (estimated_bandwidth > usb3_practical_limit or
                len(self.camera_group) > 2)  # More than 2 cameras typically need separate hubs