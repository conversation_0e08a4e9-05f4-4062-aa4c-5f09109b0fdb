import logging

import numpy as np

from a2d_sdk.robot import CosineCamera

from jzrobot.errors import DeviceAlreadyConnectedError, DeviceNotConnectedError

from ..camera import Camera
from ..configs import ColorMode
from ..utils import get_cv2_rotation
from .configuration_genie import GenieCameraConfig


class GenieCamera(Camera):
    def __init__(self, config: GenieCameraConfig):
        super().__init__(config)
        self.config = config
        self._is_connected = False
        self.camera = None

    @property
    def is_connected(self) -> bool:
        return self._is_connected
    
    def connect(self, warmup: bool = True) -> None:
        if self.is_connected:
            raise DeviceAlreadyConnectedError(f"{self} already connected")

        self.camera = CosineCamera(self.config.camera_group)
        if warmup:
            for _ in range(self.config.warmup_s):
                for camera_name in self.config.camera_group:
                    self.camera.get_latest_image(camera_name)

        logging.info(f"{self} connected.")
        self._is_connected = True
    
    def read(self, color_mode: ColorMode | None = None) -> np.ndarray:
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")
        
        pass
    
    def async_read(self, camera_name: str, timestamp_ns: int = 0, timeout_ms: float = 200) -> np.ndarray:
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")
        
        if timestamp_ns == 0:
            image, timestamp = self.camera.get_latest_image(camera_name)
        else:
            image = self.camera.get_image_nearest(camera_name, timestamp_ns)
            
        return image
    
    def disconnect(self) -> None:
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")

        if self.camera:
            self.camera.close()
        self.camera = None
        self._is_connected = False
    