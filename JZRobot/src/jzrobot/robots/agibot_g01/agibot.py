# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import logging
import time
from functools import cached_property
from typing import Any, Dict, List, Optional

import numpy as np

from jzrobot.errors import DeviceAlreadyConnectedError, DeviceNotConnectedError
from jzrobot.cameras.utils import make_cameras_from_configs
from jzrobot.robots.robot import Robot
from jzrobot.robots.agibot_g01.config_agibot import Genie01Config

logger = logging.getLogger(__name__)

try:
    from a2d_sdk.robot import RobotDds, RobotController
except ImportError:
    logger.warning("a2d_sdk not available - some functionality will be limited")
    RobotDds = None
    RobotController = None


class Genie01(Robot):
    """
    [Genie01]
    """

    config_class = Genie01Config
    name = "genie01"

    def __init__(
        self,
        config: Genie01Config,
    ):
        super().__init__(config)
        self.config = config

        # Initialize cameras if configured
        self.cameras = None
        if hasattr(config, 'cameras') and config.cameras:
            try:
                # Handle new camera configuration structure
                if isinstance(config.cameras, dict) and "cameras" in config.cameras:
                    # New structure: {"cameras": GenieCameraConfig(...)}
                    camera_config = config.cameras["cameras"]
                    from jzrobot.cameras.genie.camera_genie import GenieCamera
                    self.cameras = GenieCamera(camera_config)
                else:
                    # Old structure: dict of camera configs
                    cameras_dict = make_cameras_from_configs(config.cameras)
                    if "camera" in cameras_dict:
                        self.cameras = cameras_dict["camera"]
            except Exception as e:
                logger.warning(f"Failed to initialize cameras: {e}")
                self.cameras = None

        self.robot = None
        self._connected = False
        self._motion_controller = None

    @property
    def _motors_ft(self) -> dict[str, type]:
        arm_joints_names = [
            "left_shoulder_roll", "left_shoulder_pitch", "left_shoulder_yaw", 
            "left_elbow_pitch", "left_wrist_roll", "left_wrist_pitch", "left_wrist_yaw",
            "right_shoulder_roll", "right_shoulder_pitch", "right_shoulder_yaw", 
            "right_elbow_pitch", "right_wrist_roll", "right_wrist_pitch", "right_wrist_yaw",
        ]
        effector_names = ["left_gripper", "right_gripper"]

        effector_force_names = ["left_force_x", "left_force_y", "left_force_z", "left_torque_x", "left_torque_y", "left_torque_z",
                          "right_force_x", "right_force_y", "right_force_z", "right_torque_x", "right_torque_y", "right_torque_z"]

        end_flag_names =  ["left_ee_x", "left_ee_y", "left_ee_z", "left_ee_qx", "left_ee_qy", "left_ee_qz", "left_ee_qw",
                          "right_ee_x", "right_ee_y", "right_ee_z", "right_ee_qx", "right_ee_qy", "right_ee_qz", "right_ee_qw"],
        
        head_names = ["head_yaw", "head_pitch"]

        waist_names = ["waist_pitch", "waist_lift"]

        return {
            "state.joint.position": {
                "dtype": "float32",
                "shape": (len(arm_joints_names),),
                "names": arm_joints_names,
            },
            "state.effector.position": {
                "dtype": "float32",
                "shape": (len(effector_names),),
                "names": effector_names,
            },
            "state.effector.force": {
                "dtype": "float32",
                "shape": (len(effector_force_names),),
                "names": effector_force_names,
            },
            "state.end.position": {
                "dtype": "float32",
                "shape": (len(end_flag_names),),
                "names": end_flag_names,
            },
            "state.head.position": {
                "dtype": "float32",
                "shape": (len(head_names),),
                "names": head_names,
            },
            "state.waist.position": {
                "dtype": "float32",
                "shape": (len(waist_names),),
                "names": waist_names,
            },
        }

    @property
    def _cameras_ft(self) -> dict[str, tuple]:
        if not self.cameras or not hasattr(self.cameras.config, 'camera_group'):
            return {}

        # Get camera configuration
        if isinstance(self.config.cameras, dict) and "cameras" in self.config.cameras:
            camera_config = self.config.cameras["cameras"]
            height = camera_config.height
            width = camera_config.width
        else:
            # Fallback to default values
            height = 480
            width = 640

        return {
            f"observation.images.{cam}": (height, width, 1) if "depth" in cam else (height, width, 3)
            for cam in self.cameras.config.camera_group
        }

    @cached_property
    def observation_features(self) -> dict[str, type | tuple]:
        return {**self._motors_ft, **self._cameras_ft}

    @cached_property
    def action_features(self) -> dict[str, type]:
        # Convert motor features from "state.*" to "action.*" for actions
        action_ft = {}
        for key, value in self._motors_ft.items():
            if key.startswith("state."):
                # Replace "state." with "action."
                action_key = key.replace("state.", "action.", 1)
                action_ft[action_key] = value
            else:
                action_ft[key] = value
        return action_ft

    @property
    def is_connected(self) -> bool:
        return self._connected

    def connect(self, calibrate: bool = True) -> None:
        """
        We assume that at connection time, arm is in a rest position,
        and torque can be safely disabled to run calibration.
        """
        if self.is_connected:
            raise DeviceAlreadyConnectedError(f"{self} already connected")

        if RobotDds is None:
            raise ImportError("a2d_sdk not available - cannot connect to robot")

        self.robot = RobotDds()
        self._motion_controller = RobotController()

        # Connect cameras if available
        if self.cameras:
            try:
                self.cameras.connect()
                logger.info("Cameras connected successfully")
            except Exception as e:
                logger.warning(f"Failed to connect cameras: {e}")

        time.sleep(1)
        self._connected = True
        logger.info(f"{self} connected.")

    def configure(self) -> None:
        pass
    
    def _get_images(self) -> dict[str, Any]:
        """
        Get the images from the cameras.
        """
        images_dict = {}
        if self.cameras and hasattr(self.cameras.config, 'camera_group'):
            logger.debug(f"Camera group: {self.cameras.config.camera_group}")
            for cam_key in self.cameras.config.camera_group:
                try:
                    images_dict[f"observation.images.{cam_key}"] = self.cameras.async_read(cam_key)
                except Exception as e:
                    logger.warning(f"Failed to read camera {cam_key}: {e}")
                    images_dict[f"observation.images.{cam_key}"] = None
        else:
            logger.debug("No cameras available or camera_group not found")
        
        return images_dict
    
    def _get_ee_pose(self) -> np.ndarray:
        """
        Get the end-effector pose of the robot.
        """
        if self._motion_controller:
            try:
                status = self._motion_controller.get_motion_status()
                left_ee_pose = np.zeros(7)
                right_ee_pose = np.zeros(7)
                # left arm ee position
                if 'arm_left_link7' in status['frames']:
                    frame = status['frames']['arm_left_link7']
                    pos = frame['position']
                    quat = frame['orientation']['quaternion']

                    left_ee_pose = np.array([
                        pos['x'], pos['y'], pos['z'],
                        quat['x'], quat['y'], quat['z'], quat['w']
                    ])

                # right arm ee position
                if 'arm_right_link7' in status['frames']:
                    frame = status['frames']['arm_right_link7']
                    pos = frame['position']
                    quat = frame['orientation']['quaternion']

                    right_ee_pose = np.array([
                        pos['x'], pos['y'], pos['z'],
                        quat['x'], quat['y'], quat['z'], quat['w']
                    ])

                ee_pose = np.concatenate([left_ee_pose, right_ee_pose])
                return ee_pose
            except Exception as e:
                logger.warning(f"Failed to read end-effector pose: {e}")
                return np.zeros(14)  # Return zeros instead of None
        else:
            return np.zeros(14)  # Return zeros when no motion controller

    def _get_proprioceptive(self) -> dict[str, Any]:
        """
        Get the proprioceptive state of the robot.
        """
        proprioceptive_dict = {}
        if self.robot:
            try:
                proprioceptive_dict["observation.state.joint.position"], _ = self.robot.arm_joint_states()
                proprioceptive_dict["observation.state.effector.position"], _ = self.robot.gripper_states()
                proprioceptive_dict["observation.state.effector.force"] = self.robot.hand_force_states()
                proprioceptive_dict["observation.state.end.position"] = self._get_ee_pose()
                proprioceptive_dict["observation.state.head.position"], _ = self.robot.head_joint_states()
                proprioceptive_dict["observation.state.waist.position"], _ = self.robot.waist_joint_states()
            except Exception as e:
                logger.warning(f"Failed to read robot state: {e}")
                # Return empty dict instead of None
                proprioceptive_dict = {
                    "observation.state.joint.position": np.zeros(14),
                    "observation.state.effector.position": np.zeros(2),
                    "observation.state.effector.force": np.zeros(12),
                    "observation.state.end.position": np.zeros(14),
                    "observation.state.head.position": np.zeros(2),
                    "observation.state.waist.position": np.zeros(2),
                }
        else:
            # Return empty dict when robot is not available
            proprioceptive_dict = {
                "observation.state.joint.position": np.zeros(14),
                "observation.state.effector.position": np.zeros(2),
                "observation.state.effector.force": np.zeros(12),
                "observation.state.end.position": np.zeros(14),
                "observation.state.head.position": np.zeros(2),
                "observation.state.waist.position": np.zeros(2),
            }

        return proprioceptive_dict

    def get_observation(self) -> dict[str, Any]:
        """The returned observations do not have a batch dimension."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")

        obs_dict = {}

        # Read robot state (placeholder implementation)
        start = time.perf_counter()
        obs_dict.update(self._get_proprioceptive())
        obs_dict.update(self._get_images())
        dt_ms = (time.perf_counter() - start) * 1e3
        logger.debug(f"{self} read state: {dt_ms:.1f}ms")

        return obs_dict

    def send_action(self, action: dict[str, float]) -> dict[str, float]:
        """Command arm to move to a target joint configuration.

        The relative action magnitude may be clipped depending on the configuration parameter
        `max_relative_target`. In this case, the action sent differs from original action.
        Thus, this function always returns the action actually sent.

        Args:
            action (dict[str, float]): The goal positions for the motors.

        Returns:
            dict[str, float]: The action sent to the motors, potentially clipped.
        """
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")

        # NOTE: Action sending is intentionally not implemented for safety reasons
        # move arms
        for key, value in action.items():
            if "joint.position" in key:
                self.robot.move_arm(value)
            elif "effector.position" in key:
                self.robot.move_gripper(value)
            elif "head.position" in key:
                self.robot.move_head(value)
            elif "waist.position" in key:
                self.robot.move_waist(value)
            elif "hand.position" in key:
                self.robot.move_hand(value)

        return action
    
    def reset(self) -> None:
        pass

    def disconnect(self):
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")

        if self.cameras:
            try:
                self.cameras.disconnect()
            except Exception as e:
                logger.error(f"Error disconnecting cameras: {e}")

        if self.robot:
            try:
                self.robot.shutdown()
            except Exception as e:
                logger.error(f"Error shutting down robot: {e}")

        self.robot = None
        self._connected = False

        logger.info(f"{self} disconnected.")