# Genie01 (Agibot G01) Robot Control

This directory contains the implementation for the Genie01 (Agibot G01) robot control system, providing comprehensive control over the dual-arm humanoid robot with advanced camera integration and real-time feedback.

## 🤖 Robot Overview

The Genie01 is a sophisticated dual-arm humanoid robot featuring:

- **🦾 Dual 7-DOF Arms**: Full articulation with force feedback
- **🤏 Dual Grippers**: Precise manipulation capabilities
- **👀 Multi-Camera System**: Head and hand-mounted cameras with depth sensing
- **🏃 Mobile Base**: 2-DOF waist for body positioning
- **🎯 Head System**: 2-DOF pan-tilt for camera positioning

## 📁 Directory Structure

```
src/jzrobot/robots/agibot_g01/
├── README.md              # This file
├── agibot.py              # Main robot implementation
├── config_agibot.py       # Robot configuration classes
└── examples/              # Usage examples (optional)
```

## 🚀 Environment Setup

### 1. Set up LeRobot Environment

```bash
# Create a conda environment
conda create -n a2d python=3.10

# Activate the environment
conda activate a2d

# Install JZRobot with <PERSON>oh<PERSON> (Genie01) dependencies
pip install -e ".[aloha]"
```

### 2. Set up A2D Environment

```bash
# Install system dependencies
sudo apt install iproute2

# Install GUI requirements
pip install -r requirements_gui.txt
```

**requirements_gui.txt** contains:
```txt
numpy
protobuf==3.12.4
ruckig==0.14.0
opencv-python==*********
scipy
zmq==0.0.0
pyzmq==26.2.0
matplotlib
```

### 3. Deploy A2D SDK Environment

Confirm PC network connection with A2-D robot, then execute:

```bash
# Download and install A2D SDK
curl -sSL http://***********:8849/install.sh | bash

# Navigate to SDK directory and source environment
cd a2d_sdk
source env.sh
```

### 4. Switch to Hybrid Deployment Mode

Run the following command to enter SDK hybrid deployment mode:

```bash
python3 robot_service.py -s -c ./conf/hybrid_deploy_depth53.pbtxt
```

## 🔧 Quick Start

### Basic Usage

```python
from jzrobot.robots.agibot_g01 import Genie01, Genie01Config
from jzrobot.cameras.genie.configuration_genie import GenieCameraConfig

# Create camera configuration
camera_config = {"cameras": GenieCameraConfig(
    camera_group=["head", "head_depth", "hand_left", "hand_right"],
    width=640,
    height=480,
    fps=30
)}

# Create robot configuration
config = Genie01Config(
    id="genie01_robot",
    port="/dev/ttyUSB0",
    max_relative_target=5,  # Safety limit for movements
    cameras=camera_config
)

# Initialize and connect to robot
robot = Genie01(config)
robot.connect()

# Get current observation
observation = robot.get_observation()
print(f"Joint positions: {observation['state.joint.position']}")
print(f"Camera data available: {[k for k in observation.keys() if 'images' in k]}")

# Send action commands
action = {
    "action.joint.position": [0.01] * 14,    # Small joint movements
    "action.effector.position": [0.1, 0.1],  # Gripper positions
    "action.head.position": [0.0, 0.0],      # Head pan/tilt
    "action.waist.position": [0.0, 0.0],     # Waist pitch/lift
}
robot.send_action(action)

# Disconnect when done
robot.disconnect()
```

## 📊 Robot Specifications

### Degrees of Freedom
- **Arms**: 2 × 7 DOF (14 total)
- **Grippers**: 2 × 1 DOF (2 total)  
- **Head**: 2 DOF (pan, tilt)
- **Waist**: 2 DOF (pitch, lift)
- **Total**: 20 DOF

### Camera System
- **Head Color Camera**: 640×480 @ 30fps
- **Head Depth Camera**: 640×480 @ 30fps
- **Left Hand Camera**: 640×480 @ 30fps
- **Right Hand Camera**: 640×480 @ 30fps

### Force Sensing
- **6-axis Force/Torque**: Per arm (12 channels total)
- **Sampling Rate**: 1kHz
- **Force Range**: ±100N
- **Torque Range**: ±10Nm

## 🎛️ Configuration Options

### Robot Configuration

```python
config = Genie01Config(
    id="my_genie01",                    # Robot identifier
    port="/dev/ttyUSB0",               # Communication port
    max_relative_target=5,             # Max movement per step (safety)
    disable_torque_on_disconnect=True, # Auto-disable on disconnect
    cameras=camera_config              # Camera configuration
)
```

### Camera Configuration

```python
# Standard configuration
camera_config = {"cameras": GenieCameraConfig(
    camera_group=["head", "head_depth", "hand_left", "hand_right"],
    width=640,
    height=480,
    fps=30
)}

# High-resolution configuration (requires separate USB hubs)
high_res_config = {"cameras": GenieCameraConfig(
    camera_group=["head", "head_depth"],
    width=1920,
    height=1080,
    fps=60,
    use_separate_usb_hubs=True
)}
```

## 🧪 Testing

### Unit Tests
```bash
# Test robot implementation
python -m pytest test/robot/test_agibot_units.py -v

# Test specific functionality
python -c "
from jzrobot.robots.agibot_g01 import Genie01, Genie01Config
config = Genie01Config(id='test', port='/dev/ttyUSB0')
robot = Genie01(config)
print('✓ Robot initialization successful')
"
```

### Integration Tests (Requires Hardware)
```bash
# Source A2D environment first
source ~/workspace/a2d_sdk/env.sh

# Run integration tests
python test/robot/test_agibot.py

# Run performance benchmarks
python test/robot/benchmark_agibot.py
```

## 🛡️ Safety Features

### Movement Safety
- **Incremental Limits**: `max_relative_target` prevents large sudden movements
- **Joint Limits**: Hardware and software joint limit enforcement
- **Emergency Stop**: Immediate torque disable on errors
- **Connection Monitoring**: Continuous connection state validation

### Error Handling
- **Graceful Degradation**: System continues operation when possible
- **Comprehensive Logging**: Detailed error reporting and debugging
- **Automatic Recovery**: Self-recovery from temporary issues
- **Input Validation**: All commands validated before execution

## 📈 Performance Characteristics

### Latency (Typical)
- **Observation**: 10-20ms
- **Action**: 2-5ms
- **Camera Frame**: 33ms (30fps)
- **Force Feedback**: 1ms

### Throughput
- **Control Loop**: 30+ Hz
- **Joint Data**: 1kHz sampling
- **Camera Streams**: 4 × 30fps
- **Force Data**: 1kHz per arm

## 🔍 Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Check A2D SDK environment
echo $A2D_SDK_ROOT
source path/to/a2d_sdk/env.sh

# Verify robot service is running
ps aux | grep robot_service

# Check network connectivity
ping ***********
```

#### Camera Issues
```bash
# Check USB bandwidth
lsusb -t

# Test camera configuration
python -c "
from jzrobot.cameras.genie.configuration_genie import GenieCameraConfig
config = GenieCameraConfig()
print(f'Bandwidth: {config.estimate_bandwidth_mbps():.1f} Mbps')
print(f'USB hubs needed: {config.requires_separate_usb_hubs()}')
"
```

#### Performance Issues
- **High Latency**: Check system load and USB bandwidth
- **Dropped Frames**: Reduce camera resolution or use separate USB hubs
- **Connection Timeouts**: Verify network stability and robot service status

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# All operations will show detailed logs
robot = Genie01(config)
robot.connect()  # Shows detailed connection process
```

## 📚 API Reference

### Key Classes
- **`Genie01`**: Main robot control class
- **`Genie01Config`**: Robot configuration
- **`GenieCameraConfig`**: Camera system configuration

### Key Methods
- **`connect()`**: Establish robot connection
- **`disconnect()`**: Close robot connection
- **`get_observation()`**: Get current robot state
- **`send_action(action)`**: Send movement commands
- **`reset()`**: Reset robot to default state

### Data Formats
- **Observations**: Use `state.*` prefix
- **Actions**: Use `action.*` prefix
- **Images**: Numpy arrays (H, W, C)
- **Positions**: Radians for joints, 0-1 for grippers

## 🤝 Contributing

When contributing to Genie01 robot support:

1. **Test with Hardware**: Always test changes with actual robot
2. **Safety First**: Ensure all changes maintain safety features
3. **Performance**: Monitor latency and throughput impacts
4. **Documentation**: Update this README for any API changes

## 📞 Support

For Genie01-specific issues:
- **Hardware Issues**: Check A2D SDK documentation
- **Software Issues**: Create GitHub issue with detailed logs
- **Performance**: Use benchmark tools to identify bottlenecks
- **Safety**: Report any safety-related issues immediately

---

**⚠️ Important**: Always ensure the A2D SDK environment is properly sourced before running any robot operations.
