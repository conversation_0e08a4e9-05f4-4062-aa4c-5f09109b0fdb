#!/usr/bin/env python3
"""
YMbot-D robot usage example using JZRobot framework.
"""

import time
import numpy as np
from jzrobot.robots.YMbot_D import YMbotD, YMbotDConfig

def main():
    # Create robot configuration
    config = YMbotDConfig(
        id="ymbot_d_robot",
        max_joint_change_deg=25.0,  # Conservative safety setting
        default_trajectory_time=0.5
    )
    
    # Initialize robot
    robot = YMbotD(config)
    
    try:
        # Connect to robot
        print("Connecting to YMbot-D...")
        robot.connect()
        robot.configure()
        
        # Get initial observation
        obs = robot.get_observation()
        print(f"Initial joint positions: {obs.get('observation.state.joint.position', 'N/A')}")
        print(f"Available observations: {list(obs.keys())}")
        
        # Reset to initial pose
        print("Resetting robot...")
        robot.reset()
        time.sleep(2.0)
        
        # Send small movement action
        action = {
            "action.joint.position": [0.01] * 14,  # Small movements for all arm joints
            "action.gripper.position": [0.5, 0.5]  # Half-open grippers
        }
        
        print("Sending action...")
        sent_action = robot.send_action(action)
        print(f"Action sent: {sent_action}")
        
        # Wait and get new observation
        time.sleep(1.0)
        new_obs = robot.get_observation()
        print(f"New joint positions: {new_obs.get('observation.state.joint.position', 'N/A')}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        # Disconnect
        if robot.is_connected:
            robot.disconnect()
        print("Robot disconnected")

if __name__ == "__main__":
    main()