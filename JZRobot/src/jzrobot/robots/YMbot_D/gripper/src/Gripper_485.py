import serial
import threading
import time
import os
import sys
from typing import Optional, Dict, Any

# 添加父目录到Python路径以导入logger_config
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from common.logger_config import get_logger
from .gripper_base import GripperBase, GripperType, GripperState, GripperFault


class GripperRS485(GripperBase):
    def __init__(self, port, node_id=1, baudrate=115200):
        """
        初始化RS485夹爪控制器
        :param port: 串口名称 (如 'COM3' 或 '/dev/ttyUSB0')
        :param node_id: 夹爪节点ID (默认为1)
        :param baudrate: 通信波特率 (默认为115200)
        """
        # 调用父类构造函数
        super().__init__(node_id)

        # 初始化串口连接，配置为8位数据位、无校验位、1位停止位
        self.ser = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=0.1  # 设置读取超时为100ms
        )
        self.frame_head = bytes([0x41, 0x41])  # 帧头固定为0x4141

        # 初始化日志系统
        self.logger = get_logger(
            name=f'gripper_485_{node_id}',
            log_dir=os.path.join(os.getcwd(), "logs/gripper_485"),
            log_level=10,  # DEBUG
            console_level=20,  # INFO
            file_level=10   # DEBUG
        )

        # 重置父类中的默认值为RS485夹爪的默认值
        self._target_position = 0x80  # 默认目标位置
        self._target_force = 0xFF     # 默认目标力矩
        self._target_velocity = 0xFF  # 默认目标速度
        self._target_acceleration = 0xFF  # 默认目标加速度
        self._target_deceleration = 0xFF  # 默认目标减速度
        self._query_interval = 0.01    # 默认查询间隔10ms

        self.logger.info(f"初始化RS485夹爪 - 端口: {port}, 节点ID: {node_id}, 波特率: {baudrate}")
        self._start_recv_thread()

    def connect(self) -> bool:
        """
        连接到RS485夹爪设备
        :return: 连接成功返回True，失败返回False
        """
        try:
            if not self.ser.is_open:
                self.ser.open()
            self.logger.info("RS485夹爪连接成功")
            return True
        except Exception as e:
            self.logger.error(f"RS485夹爪连接失败: {e}")
            return False

    def disconnect(self) -> bool:
        """
        断开RS485夹爪连接
        :return: 断开成功返回True，失败返回False
        """
        try:
            if self.ser.is_open:
                self.ser.close()
            self.logger.info("RS485夹爪断开连接成功")
            return True
        except Exception as e:
            self.logger.error(f"RS485夹爪断开连接失败: {e}")
            return False

    def get_gripper_type(self) -> GripperType:
        """
        获取夹爪类型
        :return: 夹爪类型枚举
        """
        return GripperType.RS485

    def _start_recv_thread(self):
        self.logger.debug("启动RS485消息接收线程")
        self._recv_thread_running = True
        self._recv_thread = threading.Thread(target=self._recv_loop, daemon=True)
        self._recv_thread.start()
        self.logger.info("RS485消息接收线程已启动")

    def _recv_loop(self):
        last_query_time = time.time()

        while self._recv_thread_running:
            # 高频发送目标位置指令来获取状态回调
            current_time = time.time()
            if current_time - last_query_time >= self._query_interval:
                self._send_target_position_query()
                last_query_time = current_time

            # 接收消息
            response = self.ser.read(12)
            if len(response) == 12:
                status = self.parse_status(response)
                if status:  # 只有解析成功才更新状态
                    self._last_status = status
                    # self.logger.debug(f'状态更新: 故障码={status["fault_code"]}, 状态={status["state"]}, 位置=0x{status["position"]:02X}')
                    if self._status_callback:
                        try:
                            self._status_callback(status)
                        except Exception as e:
                            self.logger.error(f'状态回调错误: {e}')
            time.sleep(0.01)

    def checksum(self, buf):
        """
        计算消息校验和
        :param buf: 需要计算校验和的字节(D2-D10)
        :return: 校验和字节
        """
        ret = 0
        for i in buf:
            ret += i  # 累加所有字节
        return (~ret) & 0xff  # 取反后保留最低8位

    def _send_target_position_query(self):
        """
        发送目标位置指令 - 高频发送目标位置来实现持续的状态回调
        这样可以确保夹爪持续向目标移动，同时获得频繁的状态反馈
        """
        try:
            # 构建命令数据帧(D2-D10)
            cmd_data = bytes([
                self.node_id,  # D2: 节点ID
                0x00,  # D3: 保留位(填0)
                self._target_position & 0xFF,  # D4: 目标位置
                self._target_force & 0xFF,  # D5: 目标力矩
                self._target_velocity & 0xFF,  # D6: 目标速度
                self._target_acceleration & 0xFF,  # D7: 目标加速度
                self._target_deceleration & 0xFF,  # D8: 目标减速度
                0x00,  # D9: 保留位
                0x00  # D10: 保留位
            ])

            # 计算校验和
            chk = self.checksum(cmd_data)

            # 构建完整帧(帧头 + 数据 + 校验和)
            frame = self.frame_head + cmd_data + bytes([chk])

            # 发送命令帧
            self.ser.write(frame)
        except Exception as e:
            self.logger.error(f'目标位置查询异常: {e}')

    def send_command(self, pos_cmd: int, force_cmd: int = 0xFF, vel_cmd: int = 0xFF,
                    acc_cmd: int = 0xFF, dec_cmd: int = 0xFF, timeout: float = 5.0,
                    wait_for_completion: bool = False, position_tolerance: int = 10) -> Optional[Dict[str, Any]]:
        """
        发送控制指令到夹爪
        :param pos_cmd: 目标位置(0-0xFF, 0=完全闭合, 0xFF=完全张开)
        :param force_cmd: 目标力矩(0-0xFF, 0xFF=最大力矩)
        :param vel_cmd: 目标速度(0-0xFF, 0xFF=最大速度)
        :param acc_cmd: 目标加速度(0-0xFF, 0xFF=最大加速度)
        :param dec_cmd: 目标减速度(0-0xFF, 0xFF=最大减速度)
        :param timeout: 超时时间（秒）
        :param wait_for_completion: 是否等待夹爪到达目标位置
        :param position_tolerance: 位置容差（允许的位置误差）
        :return: 返回解析后的状态字典，超时则返回None
        """
        self.logger.info(f'发送指令: 节点ID={self.node_id}, 位置=0x{pos_cmd:02X}, 力矩=0x{force_cmd:02X}')

        # 更新目标参数，用于高频查询
        self._target_position = pos_cmd & 0xFF
        self._target_force = force_cmd & 0xFF
        self._target_velocity = vel_cmd & 0xFF
        self._target_acceleration = acc_cmd & 0xFF
        self._target_deceleration = dec_cmd & 0xFF

        # 构建命令数据帧(D2-D10)
        cmd_data = bytes([
            self.node_id,  # D2: 节点ID
            0x00,  # D3: 保留位(填0)
            pos_cmd & 0xFF,  # D4: 位置指令(确保在0-255范围内)
            force_cmd & 0xFF,  # D5: 力矩指令
            vel_cmd & 0xFF,  # D6: 速度指令
            acc_cmd & 0xFF,  # D7: 加速度指令
            dec_cmd & 0xFF,  # D8: 减速度指令
            0x00,  # D9: 保留位
            0x00  # D10: 保留位
        ])

        # 计算校验和
        chk = self.checksum(cmd_data)

        # 构建完整帧(帧头 + 数据 + 校验和)
        frame = self.frame_head + cmd_data + bytes([chk])

        # 发送命令帧
        self.ser.write(frame)

        # self.logger.debug(f'指令已发送，等待回复...')

        # 等待接收反馈
        start_time = time.time()
        last_status = None
        stable_count = 0  # 稳定计数器

        while time.time() - start_time < timeout:
            # 检查接收线程是否已经更新了状态
            if self._last_status:
                current_status = self._last_status.copy()

                # 如果不需要等待完成，收到第一个状态就返回
                if not wait_for_completion:
                    # self.logger.info(f'收到状态回复: {current_status}')
                    return current_status

                # 检查故障
                if current_status['fault_code'] != 0x00:  # 有故障
                    # self.logger.warning(f'夹爪故障: 故障码={current_status["fault_code"]}')
                    return current_status

                # 检查是否到达目标位置（考虑容差）
                current_pos = current_status['position']
                pos_diff = abs(current_pos - pos_cmd)

                if pos_diff <= position_tolerance:
                    stable_count += 1
                    if stable_count >= 2:  # 连续2次检查都在容差范围内
                        # self.logger.info(f'夹爪已到达目标位置: 目标=0x{pos_cmd:02X}, 实际=0x{current_pos:02X}, 误差={pos_diff}')
                        return current_status
                else:
                    stable_count = 0  # 重置稳定计数器
                    # 显示移动进度
                    # if time.time() - start_time > 1.0:  # 1秒后开始显示进度
                    #     self.logger.debug(f'移动中: 目标=0x{pos_cmd:02X}, 当前=0x{current_pos:02X}, 误差={pos_diff}')

                # 检查其他状态
                if current_status['state'] == 0x02:  # 夹爪堵转
                    self.logger.info(f'夹爪堵转: {current_status}')
                    return current_status

                last_status = current_status

            time.sleep(0.01)  # 100ms检查间隔

        # self.logger.warning(f'等待回复超时，节点ID: {self.node_id}')
        # if last_status:
            # self.logger.warning(f'最后状态: 目标=0x{pos_cmd:02X}, 实际=0x{last_status["position"]:02X}')
        return last_status  # 返回最后收到的状态

    def get_status(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        获取夹爪当前状态
        :param timeout: 超时时间（秒）
        :return: 状态字典或None
        """
        # 发送状态查询指令
        self._send_target_position_query()

        # 等待状态更新
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self._last_status:
                return self._last_status.copy()
            time.sleep(0.01)

        self.logger.warning(f"获取状态超时 ({timeout}s)")
        return None

    def parse_status(self, status_frame: bytes) -> Optional[Dict[str, Any]]:
        """
        解析接收到的状态帧
        :param status_frame: 12字节的状态帧
        :return: 解析后的状态字典，无效帧则返回None
        """
        # 检查帧长度和帧头是否正确
        if len(status_frame) != 12 or status_frame[0] != 0x41 or status_frame[1] != 0x41:
            return None

        # 验证校验和
        received_checksum = status_frame[11]  # 接收到的校验和(D11)
        calculated_checksum = self.checksum(status_frame[2:11])  # 计算的校验和(D2-D10)
        if received_checksum != calculated_checksum:
            return None  # 校验失败

        # 返回解析后的状态字典
        status = {
            'id': status_frame[2],  # 节点ID
            'fault_code': status_frame[3],  # 错误代码(0=无故障)
            'state': status_frame[4],  # 当前状态(0=到达目标,1=移动中等)
            'position': status_frame[5],  # 当前位置(0-255)
            'velocity': status_frame[6],  # 当前速度(0-255)
            'force': status_frame[7],  # 当前力矩(0-255)
            'reserved1': status_frame[8],  # 保留位1
            'reserved2': status_frame[9],  # 保留位2
            'reserved3': status_frame[10]  # 保留位3
        }

        # 添加可读的状态描述
        status['fault_description'] = self._get_fault_description(status_frame[3])
        status['state_description'] = self._get_state_description(status_frame[4])

        return status

    def _get_fault_description(self, fault_code):
        """获取故障代码描述"""
        fault_descriptions = {
            0x00: "无故障",
            0x01: "过温警报",
            0x02: "超速警报",
            0x03: "初始化故障警报",
            0x04: "超限检测警报"
        }
        return fault_descriptions.get(fault_code, f"未知故障代码: 0x{fault_code:02X}")

    def _get_state_description(self, state):
        """获取状态描述"""
        state_descriptions = {
            0x00: "已达到目标位置",
            0x01: "夹爪移动中",
            0x02: "夹爪堵转",
            0x03: "物体掉落"
        }
        return state_descriptions.get(state, f"未知状态: 0x{state:02X}")

    def set_status_callback(self, callback):
        """
        设置状态回调函数，每收到一帧夹爪状态就会自动调用
        :param callback: function(status_dict)
        """
        self._status_callback = callback

    def get_last_status(self):
        """
        获取最近一次收到的夹爪状态
        :return: 状态字典或 None
        """
        return self._last_status

    def open_gripper(self, force: int = 0xFF, force_cmd: int = None,
                    timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        完全打开夹爪
        :param force: 力矩 (0-0xFF) - 推荐参数名
        :param force_cmd: 力矩 (0-0xFF) - 兼容旧代码
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        self.logger.info("执行打开夹爪指令")
        # 兼容两种参数名
        actual_force = force_cmd if force_cmd is not None else force
        return self.send_command(0xFF, force_cmd=actual_force, timeout=timeout)

    def close_gripper(self, force: int = 0xFF, force_cmd: int = None,
                     timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        完全关闭夹爪
        :param force: 力矩 (0-0xFF) - 推荐参数名
        :param force_cmd: 力矩 (0-0xFF) - 兼容旧代码
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        self.logger.info("执行关闭夹爪指令")
        # 兼容两种参数名
        actual_force = force_cmd if force_cmd is not None else force
        return self.send_command(0x00, force_cmd=actual_force, timeout=timeout)

    def move_to_position(self, position: int, force: int = 0xFF,
                        timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        移动到指定位置
        :param position: 目标位置 (0-0xFF, 0=关闭, 0xFF=打开)
        :param force: 力矩 (0-0xFF)
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        self.logger.info(f"移动到位置 0x{position:02X}")
        return self.send_command(position, force_cmd=force, timeout=timeout)

    def is_moving(self):
        """
        检查夹爪是否正在移动
        :return: True if moving, False otherwise
        """
        if self._last_status:
            return self._last_status['state'] == 0x01
        return False

    def has_fault(self):
        """
        检查夹爪是否有故障
        :return: True if fault exists, False otherwise
        """
        if self._last_status:
            return self._last_status['fault_code'] != 0x00
        return False

    def is_at_position(self, target_position, tolerance=5):
        """
        检查夹爪是否在指定位置
        :param target_position: 目标位置
        :param tolerance: 位置容差
        :return: True if at position, False otherwise
        """
        if self._last_status:
            current_pos = self._last_status['position']
            return abs(current_pos - target_position) <= tolerance
        return False

    def set_query_interval(self, interval_seconds):
        """
        设置高频查询间隔
        :param interval_seconds: 查询间隔（秒），建议0.05-0.5秒
        """
        self._query_interval = max(0.01, interval_seconds)  # 最小10ms
        self.logger.info(f"查询间隔设置为 {self._query_interval*1000:.0f}ms")

    def get_target_position(self):
        """
        获取当前目标位置
        :return: 目标位置 (0-0xFF)
        """
        return self._target_position

    def get_target_parameters(self):
        """
        获取所有目标参数
        :return: 目标参数字典
        """
        return {
            'position': self._target_position,
            'force': self._target_force,
            'velocity': self._target_velocity,
            'acceleration': self._target_acceleration,
            'deceleration': self._target_deceleration
        }

    def close(self):
        """关闭串口连接"""
        self.logger.info("正在关闭RS485夹爪连接...")

        self._recv_thread_running = False
        if self._recv_thread:
            self._recv_thread.join(timeout=1)
            self.logger.debug("接收线程已停止")

        try:
            self.ser.close()
            self.logger.info("串口连接已关闭")
        except Exception as e:
            self.logger.warning(f"关闭串口时出现异常: {e}")

        self.logger.info("RS485夹爪连接已关闭")


