from ctypes import *
import time
import threading
import os
import sys
from typing import Optional, Dict, Any

# 添加父目录到Python路径以导入logger_config
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from common.logger_config import get_logger
from .gripper_base import GripperBase, GripperType, GripperState, GripperFault

VCI_USBCAN2 = 4
STATUS_OK = 1

class VCI_INIT_CONFIG(Structure):
    _fields_ = [
        ("AccCode", c_uint),
        ("AccMask", c_uint),
        ("Reserved", c_uint),
        ("Filter", c_ubyte),
        ("Timing0", c_ubyte),
        ("Timing1", c_ubyte),
        ("Mode", c_ubyte)
    ]

class VCI_CAN_OBJ(Structure):
    _fields_ = [
        ("ID", c_uint),
        ("TimeStamp", c_uint),
        ("TimeFlag", c_ubyte),
        ("SendType", c_ubyte),
        ("RemoteFlag", c_ubyte),
        ("ExternFlag", c_ubyte),
        ("DataLen", c_ubyte),
        ("Data", c_ubyte*8),
        ("Reserved", c_ubyte*3)
    ]

class GripperCAN(GripperBase):
    def __init__(self, node_id=1, channel=0, device_type=VCI_USBCAN2):
        """
        初始化CAN夹爪控制器
        :param node_id: 节点ID，对应can_node_id
        :param channel: CAN通道号（0或1）
        :param device_type: 设备类型，默认VCI_USBCAN2
        """
        # 调用父类构造函数
        super().__init__(node_id)

        self.channel = channel
        self.device_type = device_type

        # 初始化日志系统
        self.logger = get_logger(
            name=f'gripper_can_{node_id}',
            log_dir=os.path.join(os.getcwd(), "logs/gripper_can"),
            log_level=10,  # DEBUG
            console_level=20,  # INFO
            file_level=10   # DEBUG
        )
        # 尝试不同的库路径
        lib_paths = [
           os.path.join(os.path.dirname(__file__), '..', 'lib', 'libcontrolcan.so')
        ]

        self.canDLL = None
        for lib_path in lib_paths:
            try:
                self.canDLL = cdll.LoadLibrary(lib_path)
                self.logger.info(f"成功加载CAN库: {lib_path}")
                break
            except Exception as e:
                self.logger.debug(f"尝试加载CAN库失败: {lib_path}, 错误: {e}")
                continue

        if self.canDLL is None:
            self.logger.error('无法找到libcontrolcan.so文件')
            raise Exception('无法找到libcontrolcan.so文件')
        self._open_device()

        # 重置父类中的默认值为CAN夹爪的默认值
        self._target_position = 0xFF  # 默认目标位置
        self._target_force = 0xFF     # 默认目标力矩
        self._target_velocity = 0xFF  # 默认目标速度
        self._target_acceleration = 0xFF  # 默认目标加速度
        self._target_deceleration = 0xFF  # 默认目标减速度

        # 启动接收线程
        self.logger.info(f"初始化CAN夹爪 - 节点ID: {node_id}, 通道: {channel}")
        self._start_recv_thread()

        # 等待一下让接收线程启动
        time.sleep(0.1)
        # 发送一个保持当前位置的指令来获取初始状态
        self._send_target_position_query()

    def connect(self) -> bool:
        """
        连接到CAN夹爪设备
        :return: 连接成功返回True，失败返回False
        """
        try:
            self._open_device()
            self.logger.info("CAN夹爪连接成功")
            return True
        except Exception as e:
            self.logger.error(f"CAN夹爪连接失败: {e}")
            return False

    def disconnect(self) -> bool:
        """
        断开CAN夹爪连接
        :return: 断开成功返回True，失败返回False
        """
        try:
            self.close()
            self.logger.info("CAN夹爪断开连接成功")
            return True
        except Exception as e:
            self.logger.error(f"CAN夹爪断开连接失败: {e}")
            return False

    def get_gripper_type(self) -> GripperType:
        """
        获取夹爪类型
        :return: 夹爪类型枚举
        """
        return GripperType.CAN

    def _open_device(self):
        self.logger.debug("尝试打开CAN设备")
        ret = self.canDLL.VCI_OpenDevice(self.device_type, 0, 0)
        if ret != STATUS_OK:
            self.logger.error('VCI_OpenDevice 失败')
            raise Exception('VCI_OpenDevice failed')
        self.logger.info("CAN设备打开成功")

        # 配置CAN参数 - 1Mbps，80%采样点
        # Timing0=0x00, Timing1=0x14 对应1Mbps，80%采样点
        vci_initconfig = VCI_INIT_CONFIG(
            0x80000008,  # AccCode - 接收所有帧
            0xFFFFFFFF,  # AccMask - 接收所有帧
            0,           # Reserved
            0,           # Filter - 不使用过滤
            0x00,        # Timing0 - 1Mbps配置
            0x14,        # Timing1 - 80%采样点
            0            # Mode - 正常模式
        )

        self.logger.debug(f"初始化CAN通道 {self.channel}")
        ret = self.canDLL.VCI_InitCAN(self.device_type, 0, self.channel, byref(vci_initconfig))
        if ret != STATUS_OK:
            self.logger.error(f'VCI_InitCAN 失败，通道: {self.channel}')
            raise Exception('VCI_InitCAN failed')

        self.logger.debug(f"启动CAN通道 {self.channel}")
        ret = self.canDLL.VCI_StartCAN(self.device_type, 0, self.channel)
        if ret != STATUS_OK:
            self.logger.error(f'VCI_StartCAN 失败，通道: {self.channel}')
            raise Exception('VCI_StartCAN failed')

        self.logger.info(f"CAN通道 {self.channel} 初始化完成 (1Mbps, 80%采样点)")

    def _start_recv_thread(self):
        self.logger.debug("启动CAN消息接收线程")
        self._recv_thread_running = True
        self._recv_thread = threading.Thread(target=self._recv_loop, daemon=True)
        self._recv_thread.start()
        self.logger.info("CAN消息接收线程已启动")

    def _recv_loop(self):
        rx_obj = VCI_CAN_OBJ()
        last_query_time = time.time()
        self._query_interval = 0.01  # 10ms发送一次目标位置指令，实现高频回调
        self.logger.info(f'CAN消息接收线程已启动，节点ID: {self.node_id}')

        while self._recv_thread_running:
            # 高频发送目标位置指令来获取状态回调
            current_time = time.time()
            if current_time - last_query_time >= self._query_interval:
                self._send_target_position_query()
                last_query_time = current_time

            # 接收消息
            ret = self.canDLL.VCI_Receive(self.device_type, 0, self.channel, byref(rx_obj), 1, 10)  # 10ms超时
            if ret > 0:
                # 检查是否是目标节点的消息
                if rx_obj.ID == self.node_id:
                    status = self.parse_status(rx_obj.Data)
                    if status:  # 只有解析成功才更新状态
                        self._last_status = status
                        self.logger.debug(f'状态更新: 故障码={status["fault_code"]}, 状态={status["state"]}, 位置=0x{status["position"]:02X}')
                        if self._status_callback:
                            try:
                                self._status_callback(status)
                            except Exception as e:
                                self.logger.error(f'状态回调错误: {e}')
                else:
                    # 收到其他节点的消息，显示调试信息
                    self.logger.debug(f'收到其他节点消息: ID=0x{rx_obj.ID:02X}, 期望ID=0x{self.node_id:02X}')

            time.sleep(0.01)

        self.logger.info(f'CAN消息接收线程已停止，节点ID: {self.node_id}')

    def _send_target_position_query(self):
        """
        发送目标位置指令 - 高频发送目标位置来实现持续的状态回调
        这样可以确保夹爪持续向目标移动，同时获得频繁的状态反馈
        """
        try:
            # 发送当前目标位置指令
            data = (c_ubyte*8)(
                0x00,                           # Reserved
                self._target_position & 0xFF,   # 目标位置
                self._target_force & 0xFF,      # 目标力矩
                self._target_velocity & 0xFF,   # 目标速度
                self._target_acceleration & 0xFF, # 目标加速度
                self._target_deceleration & 0xFF, # 目标减速度
                0x00,                           # Reserved
                0x00                            # Reserved
            )
            reserved = (c_ubyte*3)(0, 0, 0)
            can_obj = VCI_CAN_OBJ(self.node_id, 0, 0, 1, 0, 0, 8, data, reserved)
            ret = self.canDLL.VCI_Transmit(self.device_type, 0, self.channel, byref(can_obj), 1)
            if ret != STATUS_OK:
                self.logger.warning(f'目标位置查询发送失败，节点ID: {self.node_id}')
        except Exception as e:
            self.logger.error(f'目标位置查询异常: {e}')

    # def _send_status_query(self):
    #     """
    #     发送状态查询指令 - 兼容旧代码
    #     """
    #     self._send_target_position_query()

    def send_command(self, pos_cmd: int, force_cmd: int = 0xFF, vel_cmd: int = 0xFF,
                    acc_cmd: int = 0xFF, dec_cmd: int = 0xFF, timeout: float = 10.0,
                    wait_for_completion: bool = True, position_tolerance: int = 10) -> Optional[Dict[str, Any]]:
        """
        发送控制指令到夹爪
        :param pos_cmd: 目标位置（0=闭合，0xFF=张开）
        :param force_cmd: 力矩
        :param vel_cmd: 速度
        :param acc_cmd: 加速度
        :param dec_cmd: 减速度
        :param timeout: 超时时间（秒）
        :param wait_for_completion: 是否等待夹爪到达目标位置
        :param position_tolerance: 位置容差（允许的位置误差）
        :return: 接收到的状态字典（成功）或 None（超时/失败）
        """
        self.logger.info(f'发送指令: 节点ID={self.node_id}, 位置=0x{pos_cmd:02X}, 力矩=0x{force_cmd:02X}')

        # 更新目标参数，用于高频查询
        self._target_position = pos_cmd & 0xFF
        self._target_force = force_cmd & 0xFF
        self._target_velocity = vel_cmd & 0xFF
        self._target_acceleration = acc_cmd & 0xFF
        self._target_deceleration = dec_cmd & 0xFF

        data = (c_ubyte*8)(
            0x00,                # Reserved
            pos_cmd & 0xFF,      # 目标位置
            force_cmd & 0xFF,    # 力矩
            vel_cmd & 0xFF,      # 速度
            acc_cmd & 0xFF,      # 加速度
            dec_cmd & 0xFF,      # 减速度
            0x00,                # 保留
            0x00                 # 保留
        )
        reserved = (c_ubyte*3)(0, 0, 0)
        can_obj = VCI_CAN_OBJ(self.node_id, 0, 0, 1, 0, 0, 8, data, reserved)

        ret = self.canDLL.VCI_Transmit(self.device_type, 0, self.channel, byref(can_obj), 1)
        if ret != STATUS_OK:
            self.logger.error(f'指令发送失败，节点ID: {self.node_id}')
            return None

        self.logger.debug(f'指令已发送，等待回复...')

        # 等待接收反馈 - 根据协议，夹爪会立即回复状态
        start_time = time.time()
        last_status = None
        stable_count = 0  # 稳定计数器
        first_response_received = False
        last_progress_time = start_time

        while time.time() - start_time < timeout:
            # 检查接收线程是否已经更新了状态
            if self._last_status:
                current_status = self._last_status.copy()

                if not first_response_received:
                    first_response_received = True
                    self.logger.info(f'收到首次状态回复: 位置=0x{current_status["position"]:02X}, 状态={current_status["state_description"]}')

                # 如果不需要等待完成，收到第一个状态就返回
                if not wait_for_completion:
                    return current_status

                # 检查故障
                if current_status['fault_code'] != 0x00:  # 有故障
                    self.logger.warning(f'夹爪故障: {current_status["fault_description"]}')
                    return current_status

                # 检查是否到达目标位置（考虑容差）
                current_pos = current_status['position']
                pos_diff = abs(current_pos - pos_cmd)

                if pos_diff <= position_tolerance:
                    stable_count += 1
                    if stable_count >= 2:  # 连续2次检查都在容差范围内
                        self.logger.info(f'夹爪已到达目标位置: 目标=0x{pos_cmd:02X}, 实际=0x{current_pos:02X}, 误差={pos_diff}')
                        return current_status
                else:
                    stable_count = 0  # 重置稳定计数器
                    # 减少进度显示频率，每2秒显示一次
                    current_time = time.time()
                    if current_time - last_progress_time > 2.0:
                        self.logger.debug(f'移动中: 目标=0x{pos_cmd:02X}, 当前=0x{current_pos:02X}, 误差={pos_diff}')
                        last_progress_time = current_time

                # 检查其他状态
                if current_status['state'] == 0x02:  # 夹爪堵转
                    self.logger.info(f'夹爪堵转: {current_status["state_description"]}')
                    return current_status
                elif current_status['state'] == 0x03:  # 物体掉落
                    self.logger.info(f'物体掉落: {current_status["state_description"]}')
                    return current_status

                last_status = current_status

            time.sleep(0.05)  # 50ms检查间隔，提高响应速度

        # 超时处理
        if not first_response_received:
            self.logger.error(f'等待回复超时，节点ID: {self.node_id} - 未收到任何回复')
            self.logger.error(f'请检查: 1)CAN连接 2)节点ID是否正确 3)夹爪是否上电 4)接收线程是否运行')
        else:
            self.logger.warning(f'动作超时，节点ID: {self.node_id}')
            if last_status:
                self.logger.warning(f'最后状态: 目标=0x{pos_cmd:02X}, 实际=0x{last_status["position"]:02X}')

        return last_status  # 返回最后收到的状态

    def get_status(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        获取夹爪当前状态
        :param timeout: 超时时间（秒）
        :return: 状态字典或None
        """
        # 发送状态查询指令
        self._send_target_position_query()

        # 等待状态更新
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self._last_status:
                return self._last_status.copy()
            time.sleep(0.01)

        self.logger.warning(f"获取状态超时 ({timeout}s)")
        return None

    def parse_status(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        解析上报状态数据（CAN协议上行帧）
        :param data: 8字节的数据
        :return: 状态字典或 None
        """
        if len(data) != 8:
            return None

        status = {
            'fault_code': data[0],
            'state': data[1],
            'position': data[2],
            'velocity': data[3],
            'force': data[4],
            'reserved1': data[5],
            'reserved2': data[6],
            'reserved3': data[7]
        }

        # 添加可读的状态描述
        status['fault_description'] = self._get_fault_description(data[0])
        status['state_description'] = self._get_state_description(data[1])

        return status

    def _get_fault_description(self, fault_code):
        """获取故障代码描述"""
        fault_descriptions = {
            0x00: "无故障",
            0x01: "过温警报",
            0x02: "超速警报",
            0x03: "初始化故障警报",
            0x04: "超限检测警报"
        }
        return fault_descriptions.get(fault_code, f"未知故障代码: 0x{fault_code:02X}")

    def _get_state_description(self, state):
        """获取状态描述"""
        state_descriptions = {
            0x00: "已达到目标位置",
            0x01: "夹爪移动中",
            0x02: "夹爪堵转",
            0x03: "物体掉落"
        }
        return state_descriptions.get(state, f"未知状态: 0x{state:02X}")

    def set_status_callback(self, callback):
        """
        设置状态回调函数，每收到一帧夹爪状态就会自动调用
        :param callback: function(status_dict)
        """
        self._status_callback = callback

    def get_last_status(self):
        """
        获取最近一次收到的夹爪状态
        :return: 状态字典或 None
        """
        return self._last_status

    def open_gripper(self, force: int = 0xFF, force_cmd: int = None,
                    timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        完全打开夹爪
        :param force: 力矩 (0-0xFF) - 推荐参数名
        :param force_cmd: 力矩 (0-0xFF) - 兼容旧代码
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        self.logger.info("执行打开夹爪指令")
        # 兼容两种参数名
        actual_force = force_cmd if force_cmd is not None else force
        return self.send_command(0xFF, force_cmd=actual_force, timeout=timeout)

    def close_gripper(self, force: int = 0xFF, force_cmd: int = None,
                     timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        完全关闭夹爪
        :param force: 力矩 (0-0xFF) - 推荐参数名
        :param force_cmd: 力矩 (0-0xFF) - 兼容旧代码
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        self.logger.info("执行关闭夹爪指令")
        # 兼容两种参数名
        actual_force = force_cmd if force_cmd is not None else force
        return self.send_command(0x00, force_cmd=actual_force, timeout=timeout)

    def move_to_position(self, position: int, force: int = 0xFF,
                        timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        移动到指定位置
        :param position: 目标位置 (0-0xFF, 0=关闭, 0xFF=打开)
        :param force: 力矩 (0-0xFF)
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        self.logger.info(f"移动到位置 0x{position:02X}")
        return self.send_command(position, force_cmd=force, timeout=timeout)

    def is_moving(self):
        """
        检查夹爪是否正在移动
        :return: True if moving, False otherwise
        """
        if self._last_status:
            return self._last_status['state'] == 0x01
        return False

    def has_fault(self):
        """
        检查夹爪是否有故障
        :return: True if fault exists, False otherwise
        """
        if self._last_status:
            return self._last_status['fault_code'] != 0x00
        return False

    def is_at_position(self, target_position, tolerance=5):
        """
        检查夹爪是否在指定位置
        :param target_position: 目标位置
        :param tolerance: 位置容差
        :return: True if at position, False otherwise
        """
        if self._last_status:
            current_pos = self._last_status['position']
            return abs(current_pos - target_position) <= tolerance
        return False

    def wait_for_position(self, target_position, timeout=5.0, tolerance=5):
        """
        等待夹爪到达指定位置
        :param target_position: 目标位置
        :param timeout: 超时时间
        :param tolerance: 位置容差
        :return: True if reached, False if timeout
        """
        start_time = time.time()
        stable_count = 0

        while time.time() - start_time < timeout:
            if self.is_at_position(target_position, tolerance):
                stable_count += 1
                if stable_count >= 3:  # 连续3次检查都在容差范围内
                    return True
            else:
                stable_count = 0

            if self.has_fault():
                self.logger.warning(f"等待位置时检测到故障: {self.get_last_status().get('fault_description', 'N/A')}")
                return False

            time.sleep(0.1)

        return False

    def set_query_interval(self, interval_seconds):
        """
        设置高频查询间隔
        :param interval_seconds: 查询间隔（秒），建议0.05-0.5秒
        """
        if hasattr(self, '_query_interval'):
            self._query_interval = max(0.005, interval_seconds)  
            self.logger.info(f"查询间隔设置为 {self._query_interval*1000:.0f}ms")

    def get_target_position(self):
        """
        获取当前目标位置
        :return: 目标位置 (0-0xFF)
        """
        return self._target_position

    def get_target_parameters(self):
        """
        获取所有目标参数
        :return: 目标参数字典
        """
        return {
            'position': self._target_position,
            'force': self._target_force,
            'velocity': self._target_velocity,
            'acceleration': self._target_acceleration,
            'deceleration': self._target_deceleration
        }

    def close(self):
        """
        关闭CAN连接和清理资源
        """
        self.logger.info("正在关闭CAN夹爪连接...")

        self._recv_thread_running = False
        if self._recv_thread:
            self._recv_thread.join(timeout=1)
            self.logger.debug("接收线程已停止")

        try:
            self.canDLL.VCI_CloseDevice(self.device_type, 0)
            self.logger.info("CAN设备已关闭")
        except Exception as e:
            self.logger.warning(f"关闭CAN设备时出现异常: {e}")

        self.logger.info("CAN夹爪连接已关闭")
