#!/usr/bin/env python3
"""
夹爪控制系统性能基准测试
用于验证架构分析文档中的性能指标和改进建议
"""

import time
import statistics
import os
import sys
from typing import Dict, List, Any
from collections import deque

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.Gripper_can import GripperCAN
from src.Gripper_485 import GripperRS485
from common.logger_config import get_logger

# 配置日志
logger = get_logger(
    name='performance_test',
    log_dir=os.path.join(os.getcwd(), "logs/performance_test"),
    log_level=10,  # DEBUG
    console_level=20,  # INFO
    file_level=10   # DEBUG
)


class PerformanceTester:
    """性能测试器"""

    def __init__(self):
        self.results = {}

    def test_command_latency(self, gripper, iterations: int = 100) -> Dict[str, float]:
        """测试指令响应延迟"""
        logger.info(f"开始延迟测试，迭代次数: {iterations}")
        latencies = []

        for i in range(iterations):
            start = time.perf_counter()
            result = gripper.send_command(0x80, wait_for_completion=False)
            if result:
                end = time.perf_counter()
                latencies.append(end - start)
                logger.debug(f"第{i+1}次测试延迟: {(end-start)*1000:.2f}ms")
            time.sleep(0.1)  # 避免过于频繁

        if not latencies:
            return {'error': 'No successful commands'}

        result = {
            'avg_latency_ms': statistics.mean(latencies) * 1000,
            'min_latency_ms': min(latencies) * 1000,
            'max_latency_ms': max(latencies) * 1000,
            'p95_latency_ms': statistics.quantiles(latencies, n=20)[18] * 1000 if len(latencies) >= 20 else max(latencies) * 1000,
            'success_rate': len(latencies) / iterations,
            'total_tests': iterations
        }

        logger.info(f"延迟测试完成: 平均延迟 {result['avg_latency_ms']:.2f}ms, 成功率 {result['success_rate']:.2%}")
        return result

    def test_throughput(self, gripper, duration: float = 10.0) -> Dict[str, float]:
        """测试状态更新吞吐量"""
        logger.info(f"开始吞吐量测试，持续时间: {duration}秒")

        callback_count = 0
        callback_times = []
        start_time = time.perf_counter()

        def counter_callback(status):
            nonlocal callback_count
            callback_count += 1
            callback_times.append(time.perf_counter())
            if callback_count % 50 == 0:
                logger.debug(f"已收到 {callback_count} 次回调")

        # 保存原始回调
        original_callback = gripper._status_callback
        gripper.set_status_callback(counter_callback)

        # 发送一个命令开始接收状态
        gripper.send_command(0x80, wait_for_completion=False)

        # 等待指定时间
        time.sleep(duration)

        # 恢复原始回调
        gripper.set_status_callback(original_callback)

        # 计算统计
        if len(callback_times) > 1:
            actual_duration = callback_times[-1] - callback_times[0]
            throughput = (callback_count - 1) / actual_duration if actual_duration > 0 else 0

            # 计算间隔统计
            intervals = []
            for i in range(1, len(callback_times)):
                intervals.append(callback_times[i] - callback_times[i-1])

            result = {
                'throughput_hz': throughput,
                'total_callbacks': callback_count,
                'duration_seconds': actual_duration,
                'avg_interval_ms': statistics.mean(intervals) * 1000 if intervals else 0,
                'min_interval_ms': min(intervals) * 1000 if intervals else 0,
                'max_interval_ms': max(intervals) * 1000 if intervals else 0
            }
        else:
            result = {
                'throughput_hz': 0,
                'total_callbacks': callback_count,
                'duration_seconds': duration,
                'error': 'Insufficient callbacks received'
            }

        logger.info(f"吞吐量测试完成: {result.get('throughput_hz', 0):.1f} Hz, 总回调 {callback_count} 次")
        return result

    def test_stability(self, gripper, duration: float = 60.0) -> Dict[str, Any]:
        """测试系统稳定性"""
        logger.info(f"开始稳定性测试，持续时间: {duration}秒")

        error_count = 0
        total_commands = 0
        callback_count = 0
        start_time = time.perf_counter()

        def stability_callback(status):
            nonlocal callback_count
            callback_count += 1

        original_callback = gripper._status_callback
        gripper.set_status_callback(stability_callback)

        # 定期发送命令
        positions = [0x20, 0x40, 0x60, 0x80, 0xA0, 0xC0, 0xE0, 0xFF]
        pos_index = 0

        while time.perf_counter() - start_time < duration:
            try:
                result = gripper.send_command(positions[pos_index], wait_for_completion=False)
                total_commands += 1
                if not result:
                    error_count += 1
                pos_index = (pos_index + 1) % len(positions)
                time.sleep(2)  # 每2秒发送一次命令
            except Exception as e:
                error_count += 1
                logger.error(f"稳定性测试异常: {e}")

        gripper.set_status_callback(original_callback)

        actual_duration = time.perf_counter() - start_time
        result = {
            'duration_seconds': actual_duration,
            'total_commands': total_commands,
            'error_count': error_count,
            'error_rate': error_count / max(total_commands, 1),
            'total_callbacks': callback_count,
            'avg_callbacks_per_second': callback_count / actual_duration
        }

        logger.info(f"稳定性测试完成: 错误率 {result['error_rate']:.2%}, 平均回调频率 {result['avg_callbacks_per_second']:.1f} Hz")
        return result


def test_can_gripper():
    """测试CAN夹爪性能"""
    logger.info("="*60)
    logger.info("开始CAN夹爪性能测试")
    logger.info("="*60)

    try:
        gripper = GripperCAN(node_id=1, channel=0)
        time.sleep(1)  # 等待初始化

        tester = PerformanceTester()
        results = {}

        # 延迟测试
        results['latency'] = tester.test_command_latency(gripper, iterations=50)

        # 吞吐量测试
        results['throughput'] = tester.test_throughput(gripper, duration=10.0)

        # 稳定性测试
        results['stability'] = tester.test_stability(gripper, duration=30.0)

        gripper.close()

        # 输出结果
        logger.info("\n" + "="*60)
        logger.info("CAN夹爪性能测试结果")
        logger.info("="*60)

        if 'error' not in results['latency']:
            logger.info(f"延迟测试:")
            logger.info(f"  平均延迟: {results['latency']['avg_latency_ms']:.2f}ms")
            logger.info(f"  最小延迟: {results['latency']['min_latency_ms']:.2f}ms")
            logger.info(f"  最大延迟: {results['latency']['max_latency_ms']:.2f}ms")
            logger.info(f"  P95延迟: {results['latency']['p95_latency_ms']:.2f}ms")
            logger.info(f"  成功率: {results['latency']['success_rate']:.2%}")

        if 'error' not in results['throughput']:
            logger.info(f"\n吞吐量测试:")
            logger.info(f"  回调频率: {results['throughput']['throughput_hz']:.1f} Hz")
            logger.info(f"  平均间隔: {results['throughput']['avg_interval_ms']:.2f}ms")
            logger.info(f"  总回调数: {results['throughput']['total_callbacks']}")

        logger.info(f"\n稳定性测试:")
        logger.info(f"  错误率: {results['stability']['error_rate']:.2%}")
        logger.info(f"  平均回调频率: {results['stability']['avg_callbacks_per_second']:.1f} Hz")
        logger.info(f"  总命令数: {results['stability']['total_commands']}")

        return results

    except Exception as e:
        logger.error(f"CAN夹爪测试失败: {e}")
        return None


def test_rs485_gripper():
    """测试RS485夹爪性能"""
    logger.info("="*60)
    logger.info("开始RS485夹爪性能测试")
    logger.info("="*60)

    try:
        # 尝试常见的串口
        ports = ['/dev/ttyUSB0', '/dev/ttyUSB1', '/dev/ttyACM0', 'COM3', 'COM4']
        gripper = None

        for port in ports:
            try:
                gripper = GripperRS485(port=port, node_id=1)
                logger.info(f"成功连接到串口: {port}")
                break
            except:
                continue

        if not gripper:
            logger.warning("未找到可用的RS485串口，跳过测试")
            return None

        time.sleep(1)  # 等待初始化

        tester = PerformanceTester()
        results = {}

        # 延迟测试（较少迭代，因为RS485较慢）
        results['latency'] = tester.test_command_latency(gripper, iterations=20)

        # 吞吐量测试
        results['throughput'] = tester.test_throughput(gripper, duration=10.0)

        # 稳定性测试
        results['stability'] = tester.test_stability(gripper, duration=30.0)

        gripper.close()

        # 输出结果
        logger.info("\n" + "="*60)
        logger.info("RS485夹爪性能测试结果")
        logger.info("="*60)

        if 'error' not in results['latency']:
            logger.info(f"延迟测试:")
            logger.info(f"  平均延迟: {results['latency']['avg_latency_ms']:.2f}ms")
            logger.info(f"  成功率: {results['latency']['success_rate']:.2%}")

        if 'error' not in results['throughput']:
            logger.info(f"\n吞吐量测试:")
            logger.info(f"  回调频率: {results['throughput']['throughput_hz']:.1f} Hz")
            logger.info(f"  平均间隔: {results['throughput']['avg_interval_ms']:.2f}ms")

        logger.info(f"\n稳定性测试:")
        logger.info(f"  错误率: {results['stability']['error_rate']:.2%}")
        logger.info(f"  平均回调频率: {results['stability']['avg_callbacks_per_second']:.1f} Hz")

        return results

    except Exception as e:
        logger.error(f"RS485夹爪测试失败: {e}")
        return None


def main():
    """主函数"""
    logger.info("🚀 夹爪控制系统性能基准测试")
    logger.info("基于架构分析文档的性能验证")

    if os.geteuid() != 0:
        logger.error("❌ 需要root权限才能访问硬件设备")
        logger.info("请使用: sudo python3 debug/performance_test.py")
        return

    all_results = {}

    # 测试CAN夹爪
    can_results = test_can_gripper()
    if can_results:
        all_results['can'] = can_results

    # 测试RS485夹爪
    rs485_results = test_rs485_gripper()
    if rs485_results:
        all_results['rs485'] = rs485_results

    # 生成对比报告
    if all_results:
        logger.info("\n" + "="*60)
        logger.info("性能对比总结")
        logger.info("="*60)

        for protocol, results in all_results.items():
            logger.info(f"\n{protocol.upper()}协议:")
            if 'latency' in results and 'error' not in results['latency']:
                logger.info(f"  平均延迟: {results['latency']['avg_latency_ms']:.2f}ms")
            if 'throughput' in results and 'error' not in results['throughput']:
                logger.info(f"  回调频率: {results['throughput']['throughput_hz']:.1f} Hz")
            if 'stability' in results:
                logger.info(f"  错误率: {results['stability']['error_rate']:.2%}")

    logger.info("\n✅ 性能测试完成")


if __name__ == '__main__':
    main()