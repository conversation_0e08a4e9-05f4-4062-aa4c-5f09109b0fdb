# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0

"""
YMbot-D robot implementation.

This module provides the YMbot-D robot implementation for the JZRobot framework.
"""

from .ymbot_d import YMbotD
from .config_ymbot import YMbotDConfig

__all__ = ["YMbotD", "YMbotDConfig"]