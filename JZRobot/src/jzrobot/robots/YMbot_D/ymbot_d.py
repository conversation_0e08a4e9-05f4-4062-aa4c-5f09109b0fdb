import logging
import time
from functools import cached_property
import threading
from typing import Any, Dict
import numpy as np

from jzrobot.errors import DeviceAlreadyConnectedError, DeviceNotConnectedError
from jzrobot.robots.robot import Robot
from jzrobot.robots.YMbot_D.config_ymbot import YMbotDConfig

logger = logging.getLogger(__name__)

try:
    import rclpy
    # 更安全的导入方式
    try:
        from .ymrobot import YMrobot
    except ImportError as ymrobot_error:
        logger.warning(f"Failed to import YMrobot: {ymrobot_error}")
        YMrobot = None
except ImportError:
    logger.warning("ROS2 or YMrobot dependencies not available - some functionality will be limited")
    rclpy = None
    YMrobot = None


class YMbotD(Robot):
    """
    YMbot-D robot implementation for JZRobot framework.
    
    This robot features:
    - Dual 7-DOF arms
    - 4-DOF body
    - 2-DOF neck
    - Dual grippers
    - Multi-camera system
    """

    config_class = YMbotDConfig
    name = "ymbot_d"

    def __init__(self, config: YMbotDConfig):
        super().__init__(config)
        self.config = config
        
        # Initialize ROS2
        if rclpy is None:
            raise ImportError("ROS2 not available - cannot initialize YMbot-D")
        
        if not rclpy.ok():
            rclpy.init()
        
        self.robot = None
        self._connected = False

    @property
    def _motors_ft(self) -> dict[str, type]:
        """Define the motor feature types for the robot."""
        # Arms: 14 joints (7 left + 7 right)
        arm_joints = [
            'Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 'Left_Arm_Joint4',
            'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7',
            'Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3', 'Right_Arm_Joint4',
            'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7'
        ]
        
        # Body: 4 joints
        body_joints = ['Body_Joint1', 'Body_Joint2', 'Body_Joint3', 'Body_Joint4']
        
        # Neck: 2 joints
        neck_joints = ['Neck_Joint1', 'Neck_Joint2']
        
        # Grippers
        gripper_names = ['left_gripper', 'right_gripper']
        
        # End-effector poses (position + quaternion for each arm)
        end_effector_names = [
            "left_ee_x", "left_ee_y", "left_ee_z", "left_ee_qx", "left_ee_qy", "left_ee_qz", "left_ee_qw",
            "right_ee_x", "right_ee_y", "right_ee_z", "right_ee_qx", "right_ee_qy", "right_ee_qz", "right_ee_qw"
        ]

        return {
            "state.joint.position": {
                "dtype": "float32",
                "shape": (len(arm_joints),),
                "names": arm_joints,
            },
            "state.body.position": {
                "dtype": "float32", 
                "shape": (len(body_joints),),
                "names": body_joints,
            },
            "state.neck.position": {
                "dtype": "float32",
                "shape": (len(neck_joints),),
                "names": neck_joints,
            },
            "state.gripper.position": {
                "dtype": "float32",
                "shape": (len(gripper_names),),
                "names": gripper_names,
            },
            "state.end.position": {
                "dtype": "float32",
                "shape": (len(end_effector_names),),
                "names": end_effector_names,
            },
        }
    
    @property
    def _cameras_ft(self) -> dict[str, tuple]:
        """Define camera feature types for the robot."""
        cameras_dict = {}
        
        if self.robot:
            try:
                # Standard camera resolution (can be configured)
                height = 480
                width = 640
                
                # Define available cameras based on YMbot-D capabilities
                camera_names = ["head", "left_wrist", "right_wrist"]
                
                for cam_name in camera_names:
                    cameras_dict[f"observation.images.{cam_name}"] = (height, width, 3)  # RGB
                    
            except Exception as e:
                logger.warning(f"Failed to get camera features: {e}")
        
        return cameras_dict
    
    @cached_property
    def observation_features(self) -> dict[str, type | tuple]:
        """Combined motor and camera features for observations."""
        return {**self._motors_ft, **self._cameras_ft}

    @cached_property
    def action_features(self) -> dict[str, type]:
        """Convert motor features from 'state.*' to 'action.*' for actions."""
        action_ft = {}
        for key, value in self._motors_ft.items():
            if key.startswith("state."):
                # Replace "state." with "action."
                action_key = key.replace("state.", "action.", 1)
                action_ft[action_key] = value
            else:
                action_ft[key] = value
        return action_ft

    @property
    def is_connected(self) -> bool:
        """Check if robot is connected."""
        return self._connected

    def _get_ee_pose(self) -> np.ndarray:
        """
        Get the end-effector pose of the robot.
        Returns concatenated left and right arm end-effector poses.
        """
        if self.robot:
            try:
                # Get arm end-effector poses from the robot
                left_pose, left_stamp, right_pose, right_stamp = self.robot.get_arm_end_effector_pose()
                
                if left_pose is not None and right_pose is not None:
                    # left_pose and right_pose are already arrays of [x, y, z, qx, qy, qz, qw]
                    ee_pose = np.concatenate([left_pose, right_pose])
                    return ee_pose
                else:
                    logger.warning("End-effector poses not available")
                    return np.zeros(14)  # 7 for each arm (position + quaternion)
                    
            except Exception as e:
                logger.warning(f"Failed to read end-effector pose: {e}")
                return np.zeros(14)
        else:
            return np.zeros(14)  # Return zeros when robot is not available

    def connect(self, calibrate: bool = True) -> None:
        """Connect to the YMbot-D robot."""
        if self.is_connected:
            raise DeviceAlreadyConnectedError(f"{self} already connected")

        if YMrobot is None:
            raise ImportError("YMrobot not available - cannot connect to robot")

        try:
            # Create robot instance
            self.robot = YMrobot()
            
            # Wait for initialization
            time.sleep(2.0)
            
            self._connected = True
            logger.info(f"{self} connected successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to {self}: {e}")
            raise

    def configure(self) -> None:
        """Configure the robot after connection."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected")
        
        # Set safety parameters
        self.robot.set_max_joint_change_threshold(self.config.max_joint_change_deg)
        logger.info(f"Set joint change threshold to {self.config.max_joint_change_deg}°")

    def _get_proprioceptive(self) -> dict[str, Any]:
        """Get proprioceptive state of the robot."""
        proprioceptive_dict = {}
        
        if self.robot:
            try:
                # Get full joint state (arms + body + neck)
                joint_state = self.robot.get_full_joint_state()
                if joint_state is not None and len(joint_state) >= 20:
                    # Extract arm joints (first 14)
                    proprioceptive_dict["observation.state.joint.position"] = joint_state[:14]
                    
                    # Extract body joints (next 4)
                    proprioceptive_dict["observation.state.body.position"] = joint_state[14:18]
                    
                    # Extract neck joints (next 2)
                    proprioceptive_dict["observation.state.neck.position"] = joint_state[18:20]
                
                # Get gripper states
                try:
                    left_gripper_state = self.robot.get_gripper_state("left")["position"]
                    right_gripper_state = self.robot.get_gripper_state("right")["position"]
                    
                    # Extract position values from gripper states
                    left_pos = left_gripper_state if isinstance(left_gripper_state, (int, float)) else 0.0
                    right_pos = right_gripper_state if isinstance(right_gripper_state, (int, float)) else 0.0
                    
                    proprioceptive_dict["observation.state.gripper.position"] = [left_pos, right_pos]
                except Exception as e:
                    logger.warning(f"Failed to read gripper states: {e}")
                    proprioceptive_dict["observation.state.gripper.position"] = [0.0, 0.0]
                
                # Get end-effector poses
                proprioceptive_dict["observation.state.end.position"] = self._get_ee_pose()
                    
            except Exception as e:
                logger.warning(f"Failed to read robot state: {e}")
                # Return default values on error
                proprioceptive_dict = {
                    "observation.state.joint.position": np.zeros(14),
                    "observation.state.body.position": np.zeros(4),
                    "observation.state.neck.position": np.zeros(2),
                    "observation.state.gripper.position": np.zeros(2),
                    "observation.state.end.position": np.zeros(14),
                }
        else:
            # Return default values when robot is not available
            proprioceptive_dict = {
                "observation.state.joint.position": np.zeros(14),
                "observation.state.body.position": np.zeros(4),
                "observation.state.neck.position": np.zeros(2),
                "observation.state.gripper.position": np.zeros(2),
                "observation.state.end.position": np.zeros(14),
            }
                
        return proprioceptive_dict

    def _get_images(self) -> dict[str, Any]:
        """Get camera images."""
        images_dict = {}
        
        if self.robot:
            try:
                # Head RGB camera
                head_rgb = self.robot.get_head_rgb()
                if head_rgb is not None:
                    images_dict["observation.images.head"] = head_rgb
                
                # Left wrist camera
                left_wrist_rgb = self.robot.get_left_wrist_image()
                if left_wrist_rgb is not None:
                    images_dict["observation.images.left_wrist"] = left_wrist_rgb
                
                # Right wrist camera  
                right_wrist_rgb = self.robot.get_right_wrist_image()
                if right_wrist_rgb is not None:
                    images_dict["observation.images.right_wrist"] = right_wrist_rgb
                    
            except Exception as e:
                logger.warning(f"Failed to read camera images: {e}")
                
        return images_dict

    def get_observation(self) -> dict[str, Any]:
        """Get current observation from the robot."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected")

        obs_dict = {}
        
        start = time.perf_counter()
        obs_dict.update(self._get_proprioceptive())
        obs_dict.update(self._get_images())
        dt_ms = (time.perf_counter() - start) * 1e3
        logger.debug(f"{self} read state: {dt_ms:.1f}ms")

        return obs_dict

    def send_action(self, action: dict[str, Any]) -> dict[str, Any]:
        """Send action to the robot."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected")

        sent_action = {}
        
        try:
            # Handle arm joint positions
            if "action.joint.position" in action:
                joint_positions = action["action.joint.position"]
                if len(joint_positions) == 14:
                    # Split into left and right arms
                    left_arm = joint_positions[:7]
                    right_arm = joint_positions[7:14]
                    
                    success_left = self.robot.control_left_arm_joint_position_ros2controller(left_arm)
                    success_right = self.robot.control_right_arm_joint_position_ros2controller(right_arm)
                    
                    if success_left and success_right:
                        sent_action["action.joint.position"] = joint_positions
            
            # Handle body positions
            if "action.body.position" in action:
                body_positions = action["action.body.position"]
                success = self.robot.control_body_joint_position(body_positions)
                if success:
                    sent_action["action.body.position"] = body_positions
            
            # Handle neck positions
            if "action.neck.position" in action:
                neck_positions = action["action.neck.position"]
                success = self.robot.control_neck_joint_position(neck_positions)
                if success:
                    sent_action["action.neck.position"] = neck_positions
            
            # Handle gripper positions
            if "action.gripper.position" in action:
                gripper_positions = action["action.gripper.position"]
                if len(gripper_positions) >= 2:
                    self.robot.move_gripper_to_position("left", gripper_positions[0])
                    self.robot.move_gripper_to_position("right", gripper_positions[1])
                    sent_action["action.gripper.position"] = gripper_positions
                    
        except Exception as e:
            logger.error(f"Failed to send action: {e}")
        
        return sent_action

    def reset(self) -> None:
        """Reset robot to initial pose."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected")
        
        try:
            success = self.robot.reset()
            if success:
                logger.info("Robot reset successfully")
            else:
                logger.warning("Robot reset failed")
        except Exception as e:
            logger.error(f"Error during reset: {e}")

    def disconnect(self) -> None:
        """Disconnect from the robot."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected")

        try:
            if self.robot:
                self.robot.shutdown()
            
            self._connected = False
            self.robot = None

            rclpy.shutdown()
            
            logger.info(f"{self} disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting {self}: {e}")
