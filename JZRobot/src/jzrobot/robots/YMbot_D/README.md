# YMbot-D Robot Control

This directory contains the implementation for the YMbot-D robot control system, providing comprehensive control over the dual-arm humanoid robot with ROS2 integration.

## 🤖 Robot Overview

The YMbot-D is a sophisticated dual-arm humanoid robot featuring:

- **🦾 Dual 7-DOF Arms**: Full articulation with safety limits
- **🤏 Dual Grippers**: RS485-controlled precise manipulation
- **👀 Multi-Camera System**: Head and wrist-mounted cameras
- **🏃 Mobile Body**: 4-DOF body positioning
- **🎯 Head System**: 2-DOF pan-tilt for camera positioning

## 📁 Directory Structure

```
src/jzrobot/robots/YMbot_D/
├── README.md              # This file
├── ymbot_d.py             # Main robot implementation (JZRobot framework)
├── config_ymbot.py        # Robot configuration classes
├── ymrobot.py             # Original ROS2 implementation
├── ymrobot_test.py        # Original usage example
└── example_usage.py       # JZRobot framework usage example
```

## 🚀 Environment Setup

### 1. Install ROS2 Dependencies

```bash
# Install ROS2 (if not already installed)
sudo apt update
sudo apt install ros-humble-desktop

# Source ROS2 environment
source /opt/ros/humble/setup.bash

# Install additional dependencies
sudo apt install ros-humble-moveit ros-humble-joint-trajectory-controller
```

### 2. Install JZRobot with YMbot-D Support

```bash
# Create conda environment
conda create -n ymbot python=3.10
conda activate ymbot

# Install JZRobot
pip install -e ".[ymbot]"
```

## 🔧 Quick Start

### Basic Usage with JZRobot Framework

```python
from jzrobot.robots.YMbot_D import YMbotD, YMbotDConfig

# Create robot configuration
config = YMbotDConfig(
    id="ymbot_d_robot",
    max_joint_change_deg=30.0,  # Safety limit
    default_trajectory_time=0.2
)

# Initialize and connect to robot
robot = YMbotD(config)
robot.connect()
robot.configure()

# Get current observation
observation = robot.get_observation()
print(f"Joint positions: {observation['observation.state.joint.position']}")

# Send action commands
action = {
    "action.joint.position": [0.01] * 14,    # Arm movements
    "action.body.position": [0.0] * 4,       # Body position
    "action.neck.position": [0.0, 0.0],      # Head pan/tilt
    "action.gripper.position": [0.5, 0.5]    # Gripper positions
}
robot.send_action(action)

# Reset and disconnect
robot.reset()
robot.disconnect()
```

## 📊 Robot Specifications

### Degrees of Freedom
- **Arms**: 2 × 7 DOF (14 total)
- **Body**: 4 DOF
- **Neck**: 2 DOF (pan, tilt)
- **Grippers**: 2 × 1 DOF (2 total)
- **Total**: 22 DOF

### Camera System
- **Head RGB Camera**: Variable resolution
- **Left Wrist Camera**: Variable resolution
- **Right Wrist Camera**: Variable resolution

### Safety Features
- **Joint Change Limits**: Configurable maximum change per command
- **Trajectory Validation**: Automatic rejection of unsafe movements
- **Emergency Stop**: Immediate stop capabilities

## 🛡️ Safety Configuration

```python
# Conservative mode (15° threshold)
config.max_joint_change_deg = 15.0

# Normal mode (30° threshold) - default
config.max_joint_change_deg = 30.0

# Aggressive mode (45° threshold)
config.max_joint_change_deg = 45.0
```

## 🔍 Troubleshooting

### Common Issues

#### ROS2 Connection Problems
```bash
# Check ROS2 environment
echo $ROS_DOMAIN_ID
source /opt/ros/humble/setup.bash

# Check if robot nodes are running
ros2 node list
ros2 topic list
```

#### Gripper Issues
```bash
# Check USB ports
ls /dev/ttyUSB*

# Verify gripper connections
# Left gripper: /dev/ttyUSB1
# Right gripper: /dev/ttyUSB2
```

## 📚 API Reference

### Key Classes
- **`YMbotD`**: Main robot control class (JZRobot framework)
- **`YMbotDConfig`**: Robot configuration
- **`YMrobot`**: Original ROS2 implementation

### Key Methods
- **`connect()`**: Establish robot connection
- **`disconnect()`**: Close robot connection
- **`get_observation()`**: Get current robot state
- **`send_action(action)`**: Send movement commands
- **`reset()`**: Reset robot to default state

### Data Formats
- **Joint Positions**: Radians
- **Gripper Positions**: 0-1 range
- **Images**: Numpy arrays (H, W, C)

## 🤝 Migration from Original Implementation

The JZRobot framework implementation (`ymbot_d.py`) wraps the original ROS2 implementation (`ymrobot.py`) to provide:

- Standardized API compatible with other robots
- Unified observation/action format
- Enhanced error handling
- Better integration with data collection pipelines

Original functionality remains available through the underlying `YMrobot` class.