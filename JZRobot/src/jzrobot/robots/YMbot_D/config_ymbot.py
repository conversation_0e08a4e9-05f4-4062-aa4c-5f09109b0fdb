# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0

from dataclasses import dataclass, field

from ..config import RobotConfig
from ...cameras.configs import CameraConfig


@RobotConfig.register_subclass("ymbot_d")
@dataclass
class YMbotDConfig(RobotConfig):
    # ROS2 node configuration
    node_name: str = "ymrobot"
    
    # Safety parameters
    max_joint_change_deg: float = 30.0  # Maximum joint change in degrees
    
    # Gripper configuration
    left_gripper_port: str = "/dev/ttyUSB1"
    right_gripper_port: str = "/dev/ttyUSB2"
    gripper_node_id: int = 1
    
    # Camera configuration
    cameras: dict[str, CameraConfig] = field(default_factory=dict)
    
    # Control parameters
    default_trajectory_time: float = 0.2
    control_frequency: float = 50.0