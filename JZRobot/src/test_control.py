from jzrobot.robots.YMbot_D import YMbot<PERSON>, YMbotDConfig

# Example with YMbot-D robot implementation
# (Specific robot implementations available in src/jzrobot/robots/)

# Create robot configuration
config = YMbotDConfig(
    id="ymbot_d",
    max_joint_change_deg=30.0,
    default_trajectory_time=0.2
)

# Initialize robot with YMbot-D implementation
robot = YMbotD(config)

# Connect to robot
robot.connect()

# Get observation
# observation = robot.get_observation()
# print(f"Observation keys: {list(observation.keys())}")
# print("Observation:", observation)

# Send action (actions use 'action.*' prefix, observations use 'state.*')
action = {
    "action.joint.position": [0.01] * 14,    # Joint movements
    "action.gripper.position": [50, 150]    # Gripper positions
}
robot.send_action(action)

# Disconnect
robot.disconnect()
