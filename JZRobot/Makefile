# Makefile for JZRobot project

.PHONY: help install install-dev test test-units test-integration benchmark clean lint format check

# Default target
help:
	@echo "JZRobot Development Commands:"
	@echo ""
	@echo "Installation:"
	@echo "  install      - Install package in development mode"
	@echo "  install-dev  - Install with development dependencies"
	@echo ""
	@echo "Testing:"
	@echo "  test         - Run all tests"
	@echo "  test-units   - Run unit tests only"
	@echo "  test-integration - Run integration tests (requires hardware)"
	@echo "  benchmark    - Run performance benchmarks"
	@echo ""
	@echo "Development:"
	@echo "  lint         - Run code linting"
	@echo "  format       - Format code with black and isort"
	@echo "  check        - Run all checks (lint + test)"
	@echo "  clean        - Clean build artifacts"
	@echo ""
	@echo "Environment:"
	@echo "  source-env   - Source a2d_sdk environment"

# Installation targets
install:
	@echo "Installing JZRobot package..."
	pip install -e .

install-aloha:
	@echo "Installing JZRobot with Aloha (Genie01) dependencies..."
	pip install -e ".[aloha]"

install-pusht:
	@echo "Installing JZRobot with PushT dependencies..."
	pip install -e ".[pusht]"

install-dev:
	@echo "Installing JZRobot with development dependencies..."
	pip install -e ".[dev,cameras]"

install-all:
	@echo "Installing JZRobot with all dependencies..."
	pip install -e ".[all]"

# Testing targets
test: test-units test-integration

test-units:
	@echo "Running unit tests..."
	python test/robot/test_agibot_units.py

test-integration:
	@echo "Running integration tests (requires hardware)..."
	@echo "Make sure to source environment first: source ~/workspace/a2d_sdk/env.sh"
	python test/robot/test_agibot.py

benchmark:
	@echo "Running performance benchmarks..."
	@echo "Make sure to source environment first: source ~/workspace/a2d_sdk/env.sh"
	python test/robot/benchmark_agibot.py

# Development targets
lint:
	@echo "Running linting..."
	-flake8 src/ test/ --max-line-length=100 --ignore=E203,W503
	-mypy src/jzrobot --ignore-missing-imports

format:
	@echo "Formatting code..."
	-black src/ test/ --line-length=100
	-isort src/ test/ --profile=black

check: lint test-units
	@echo "All checks completed!"

# Cleanup targets
clean:
	@echo "Cleaning build artifacts..."
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Environment helpers
source-env:
	@echo "To source the a2d_sdk environment, run:"
	@echo "source ~/workspace/a2d_sdk/env.sh"

# Quick development workflow
dev-setup: install-dev
	@echo "Development environment setup complete!"
	@echo "Run 'make test-units' to verify installation"

# CI/CD targets
ci-test: install test-units lint
	@echo "CI tests completed!"

# Documentation (placeholder)
docs:
	@echo "Documentation generation not yet implemented"

# Package building
build:
	@echo "Building package..."
	python setup.py sdist bdist_wheel

# Installation verification
verify:
	@echo "Verifying installation..."
	python -c "import jzrobot; print(f'JZRobot version: {jzrobot.__version__}')"
	python -c "from jzrobot.robots.agibot_g01 import Genie01; print('Genie01 import successful')"
	@echo "Installation verified successfully!"
