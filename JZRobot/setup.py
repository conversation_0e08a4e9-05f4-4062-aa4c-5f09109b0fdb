#!/usr/bin/env python3

"""
Setup script for JZRobot framework.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), "README.md")
    if os.path.exists(readme_path):
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return "JZRobot Control Framework"

# Read requirements
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), "requirements.txt")
    if os.path.exists(requirements_path):
        with open(requirements_path, "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return []

setup(
    name="jzrobot",
    version="0.1.0",
    description="A comprehensive, modular robot control framework",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="JZRobot Team",
    author_email="<EMAIL>",
    url="https://github.com/jzrobot/jzrobot",
    
    # Package configuration
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # Python version requirement
    python_requires=">=3.8",
    
    # Dependencies
    install_requires=read_requirements(),
    
    # Optional dependencies
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "isort>=5.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "cameras": [
            "opencv-python>=4.5.0",
            "pyrealsense2>=2.50.0",
        ],
        "visualization": [
            "matplotlib>=3.3.0",
            "plotly>=5.0.0",
        ],
        "all": [
            "opencv-python>=4.5.0",
            "pyrealsense2>=2.50.0",
            "matplotlib>=3.3.0",
            "plotly>=5.0.0",
        ],
    },
    
    # Entry points
    entry_points={
        "console_scripts": [
            "jzrobot-test=jzrobot.cli.test:main",
            "jzrobot-benchmark=jzrobot.cli.benchmark:main",
        ],
    },
    
    # Package data
    include_package_data=True,
    package_data={
        "jzrobot": [
            "config/*.yaml",
            "config/*.json",
        ],
    },
    
    # Classifiers
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: Apache Software License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    # Keywords
    keywords="robotics, control, framework, automation, ai",
    
    # Project URLs
    project_urls={
        "Bug Reports": "https://github.com/jzrobot/jzrobot/issues",
        "Source": "https://github.com/jzrobot/jzrobot",
        "Documentation": "https://jzrobot.readthedocs.io/",
    },
)
