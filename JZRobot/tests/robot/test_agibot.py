#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Genie01 (Agibot) robot.

This script provides comprehensive testing for the Genie01 robot implementation,
including connection, observation, action sending, and error handling tests.

Usage:
    python test/robot/test_agibot.py [options]

Note: This script requires sourcing ~/workspace/a2d_sdk/env.sh before running.
"""

import os
import sys
import time
import logging
import argparse
import traceback
from pathlib import Path
from typing import Dict, Any, List, Optional
import numpy as np

# Add src to path for development
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from jzrobot.robots.agibot_g01.agibot import Genie01
    from jzrobot.robots.agibot_g01.config_agibot import Genie01Config
    from jzrobot.cameras.genie.configuration_genie import GenieCameraConfig
    from jzrobot.errors import DeviceNotConnectedError, DeviceAlreadyConnectedError
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install the package first: python install.py")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_agibot")


class AgibotTester:
    """Comprehensive tester for Genie01 robot."""
    
    def __init__(self, config: Genie01Config):
        self.config = config
        self.robot: Optional[Genie01] = None
        self.test_results = {
            "connection": {"passed": False, "error": None},
            "observation": {"passed": False, "error": None, "data": None},
            "camera_bandwidth": {"passed": False, "error": None},
            "action": {"passed": False, "error": None},
            "disconnection": {"passed": False, "error": None},
            "error_handling": {"passed": False, "error": None}
        }
    
    def setup_robot(self) -> bool:
        """Initialize robot instance."""
        try:
            logger.info("Setting up robot instance...")
            self.robot = Genie01(self.config)
            logger.info(f"Robot instance created: {self.robot.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to setup robot: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test robot connection."""
        logger.info("=== Testing Robot Connection ===")
        
        try:
            # Test initial state
            assert not self.robot.is_connected, "Robot should not be connected initially"
            logger.info("✓ Initial connection state correct")
            
            # Test connection
            logger.info("Attempting to connect to robot...")
            self.robot.connect(calibrate=True)
            
            # Verify connection
            assert self.robot.is_connected, "Robot should be connected after connect()"
            assert self.robot.robot is not None, "Robot hardware interface should be initialized"
            logger.info("✓ Robot connected successfully")
            
            # Test double connection (should raise error)
            try:
                self.robot.connect()
                assert False, "Double connection should raise DeviceAlreadyConnectedError"
            except DeviceAlreadyConnectedError:
                logger.info("✓ Double connection properly handled")
            
            self.test_results["connection"]["passed"] = True
            return True
            
        except Exception as e:
            error_msg = f"Connection test failed: {e}"
            logger.error(error_msg)
            self.test_results["connection"]["error"] = error_msg
            return False
    
    def test_observation_features(self) -> bool:
        """Test observation and action features."""
        logger.info("=== Testing Observation Features ===")
        
        try:
            # Test observation features
            obs_features = self.robot.observation_features
            logger.info(f"Observation features: {list(obs_features.keys())}")
            
            # Verify expected features
            expected_obs_features = [
                "state.joint.position",
                "state.effector.position", 
                "state.effector.force",
                "state.end.position",
                "state.head.position",
                "state.waist.position"
            ]
            
            for feature in expected_obs_features:
                assert feature in obs_features, f"Missing observation feature: {feature}"
                logger.info(f"✓ Feature {feature}: {obs_features[feature]}")
            
            # Test action features
            action_features = self.robot.action_features
            logger.info(f"Action features: {list(action_features.keys())}")
            
            # Action features should be subset of observation features
            for feature in action_features:
                assert feature in obs_features, f"Action feature {feature} not in observation features"
            
            logger.info("✓ All features validated")
            return True
            
        except Exception as e:
            error_msg = f"Feature test failed: {e}"
            logger.error(error_msg)
            return False
    
    def test_observation_collection(self) -> bool:
        """Test observation collection."""
        logger.info("=== Testing Observation Collection ===")
        
        try:
            # Test single observation
            logger.info("Collecting single observation...")
            start_time = time.time()
            observation = self.robot.get_observation()
            obs_time = (time.time() - start_time) * 1000  # ms
            
            logger.info(f"Observation collected in {obs_time:.2f}ms")
            logger.info(f"Observation keys: {list(observation.keys())}")
            
            # Validate observation structure
            self._validate_observation(observation)
            
            # Test multiple observations for consistency
            logger.info("Testing observation consistency...")
            observations = []
            for i in range(5):
                obs = self.robot.get_observation()
                observations.append(obs)
                time.sleep(0.1)
            
            # Check that observations have consistent structure
            first_keys = set(observations[0].keys())
            for i, obs in enumerate(observations[1:], 1):
                obs_keys = set(obs.keys())
                assert obs_keys == first_keys, f"Observation {i} has different keys"
            
            logger.info("✓ Observation consistency validated")
            
            # Store observation data for analysis
            self.test_results["observation"]["data"] = {
                "sample_observation": observation,
                "observation_time_ms": obs_time,
                "keys": list(observation.keys())
            }
            
            self.test_results["observation"]["passed"] = True
            return True
            
        except Exception as e:
            error_msg = f"Observation test failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            self.test_results["observation"]["error"] = error_msg
            return False
    
    def _validate_observation(self, observation: Dict[str, Any]) -> None:
        """Validate observation data structure."""
        logger.info("Validating observation structure...")

        # Check for expected observation keys
        expected_patterns = [
            "observation.state.joint.position",
            "observation.state.effector.position",
            "observation.state.effector.force",
            "observation.state.end.position",
            "observation.state.head.position",
            "observation.state.waist.position"
        ]

        for pattern in expected_patterns:
            found = any(pattern in key for key in observation.keys())
            if found:
                logger.info(f"✓ Found observation data for: {pattern}")
            else:
                logger.warning(f"⚠ Missing observation data for: {pattern}")

        # Check for camera data based on configuration
        camera_keys = [key for key in observation.keys() if "images" in key]
        if camera_keys:
            logger.info(f"✓ Found camera data: {camera_keys}")

            # Validate expected cameras based on config
            expected_cameras = ["head", "head_depth", "hand_left", "hand_right"]
            for expected_cam in expected_cameras:
                cam_key = f"observation.images.{expected_cam}"
                if cam_key in observation:
                    logger.info(f"✓ Camera {expected_cam} data present")
                else:
                    logger.warning(f"⚠ Camera {expected_cam} data missing (may be due to bandwidth limitations)")
        else:
            logger.warning("⚠ No camera data found - check camera configuration and USB bandwidth")

        # Validate data types and values
        for key, value in observation.items():
            if value is not None:
                if isinstance(value, (list, np.ndarray)):
                    logger.info(f"✓ {key}: {type(value)} with shape/length {len(value) if hasattr(value, '__len__') else 'N/A'}")
                else:
                    logger.info(f"✓ {key}: {type(value)}")
            else:
                logger.warning(f"⚠ {key}: None (data not available - may be due to hardware/bandwidth issues)")
    
    def test_action_sending(self) -> bool:
        """Test action sending capabilities."""
        logger.info("=== Testing Action Sending ===")
        
        try:
            # Create safe test actions
            test_actions = self._create_test_actions()
            
            for action_name, action_data in test_actions.items():
                logger.info(f"Testing action: {action_name}")
                
                try:
                    start_time = time.time()
                    returned_action = self.robot.send_action(action_data)
                    action_time = (time.time() - start_time) * 1000  # ms
                    
                    logger.info(f"✓ Action {action_name} sent in {action_time:.2f}ms")
                    
                    # Verify returned action
                    assert returned_action == action_data, f"Returned action differs for {action_name}"
                    
                    # Brief pause between actions
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"✗ Action {action_name} failed: {e}")
                    raise
            
            self.test_results["action"]["passed"] = True
            return True
            
        except Exception as e:
            error_msg = f"Action test failed: {e}"
            logger.error(error_msg)
            self.test_results["action"]["error"] = error_msg
            return False
    
    def _create_test_actions(self) -> Dict[str, Dict[str, Any]]:
        """Create safe test actions for different components."""
        return {
            "joint_positions": {
                "state.joint.position": [0.01] * 14  # Small joint movements
            },
            "gripper_positions": {
                "state.effector.position": [0.1, 0.1]  # Small gripper opening
            },
            "head_positions": {
                "state.head.position": [0.05, 0.05]  # Small head movement
            },
            "waist_positions": {
                "state.waist.position": [0.02, 0.5]  # Small waist movement
            }
        }

    def test_camera_bandwidth_handling(self) -> bool:
        """Test camera bandwidth and USB hub requirements."""
        logger.info("=== Testing Camera Bandwidth Handling ===")

        try:
            if not self.robot.is_connected:
                logger.warning("Robot not connected - skipping camera bandwidth test")
                return True

            # Test camera observation collection
            logger.info("Testing camera data collection...")
            observation = self.robot.get_observation()

            # Count available cameras
            camera_keys = [key for key in observation.keys() if "images" in key]
            camera_count = len(camera_keys)

            logger.info(f"Found {camera_count} cameras in observation")

            # Check for bandwidth-related issues
            failed_cameras = []
            for key, value in observation.items():
                if "images" in key and value is None:
                    camera_name = key.split(".")[-1]
                    failed_cameras.append(camera_name)

            if failed_cameras:
                logger.warning(f"Cameras with no data (possible bandwidth issues): {failed_cameras}")
                logger.warning("Consider using separate USB hubs or PCIe cards as mentioned in config")
            else:
                logger.info("✓ All configured cameras providing data")

            # Test multiple rapid camera reads (bandwidth stress test)
            logger.info("Testing camera bandwidth under rapid reads...")
            for i in range(5):
                obs = self.robot.get_observation()
                camera_data_count = sum(1 for k, v in obs.items() if "images" in k and v is not None)
                logger.info(f"Rapid read {i+1}: {camera_data_count} cameras with data")
                time.sleep(0.1)

            logger.info("✓ Camera bandwidth test completed")
            self.test_results["camera_bandwidth"]["passed"] = True
            return True

        except Exception as e:
            error_msg = f"Camera bandwidth test failed: {e}"
            logger.error(error_msg)
            self.test_results["camera_bandwidth"]["error"] = error_msg
            return False

    def test_error_handling(self) -> bool:
        """Test error handling scenarios."""
        logger.info("=== Testing Error Handling ===")
        
        try:
            # Test observation without connection (should be connected, so disconnect first)
            if self.robot.is_connected:
                self.robot.disconnect()
            
            # Test observation on disconnected robot
            try:
                self.robot.get_observation()
                assert False, "Should raise DeviceNotConnectedError"
            except DeviceNotConnectedError:
                logger.info("✓ Properly handles observation on disconnected robot")
            
            # Test action on disconnected robot
            try:
                self.robot.send_action({"state.joint.position": [0.0] * 14})
                assert False, "Should raise DeviceNotConnectedError"
            except DeviceNotConnectedError:
                logger.info("✓ Properly handles action on disconnected robot")
            
            # Reconnect for further tests
            try:
                self.robot.connect()
                logger.info("✓ Robot reconnected successfully after error handling tests")
            except Exception as reconnect_error:
                logger.error(f"Failed to reconnect robot: {reconnect_error}")
                # Don't fail the error handling test if reconnection fails
                # but log it for debugging

            self.test_results["error_handling"]["passed"] = True
            return True
            
        except Exception as e:
            error_msg = f"Error handling test failed: {e}"
            logger.error(error_msg)
            self.test_results["error_handling"]["error"] = error_msg
            return False
    
    def test_disconnection(self) -> bool:
        """Test robot disconnection."""
        logger.info("=== Testing Robot Disconnection ===")

        try:
            # Ensure robot is connected before testing disconnection
            if not self.robot.is_connected:
                logger.info("Robot not connected, attempting to connect for disconnection test...")
                try:
                    self.robot.connect()
                    logger.info("✓ Robot connected for disconnection test")
                except Exception as connect_error:
                    logger.error(f"Failed to connect robot for disconnection test: {connect_error}")
                    self.test_results["disconnection"]["error"] = f"Could not connect robot: {connect_error}"
                    return False

            # Test disconnection
            assert self.robot.is_connected, "Robot should be connected before disconnect test"

            self.robot.disconnect()

            # Verify disconnection
            assert not self.robot.is_connected, "Robot should not be connected after disconnect()"
            assert self.robot.robot is None, "Robot hardware interface should be None"
            logger.info("✓ Robot disconnected successfully")

            # Test double disconnection (should raise error)
            try:
                self.robot.disconnect()
                assert False, "Double disconnection should raise DeviceNotConnectedError"
            except DeviceNotConnectedError:
                logger.info("✓ Double disconnection properly handled")

            self.test_results["disconnection"]["passed"] = True
            return True

        except Exception as e:
            error_msg = f"Disconnection test failed: {e}"
            logger.error(error_msg)
            self.test_results["disconnection"]["error"] = error_msg
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return results."""
        logger.info("Starting comprehensive Agibot tests...")
        
        if not self.setup_robot():
            return self.test_results
        
        try:
            # Run tests in sequence
            self.test_connection()
            
            if self.test_results["connection"]["passed"]:
                self.test_observation_features()
                self.test_observation_collection()
                self.test_camera_bandwidth_handling()  # New camera bandwidth test
                # self.test_action_sending()
                self.test_error_handling()
                self.test_disconnection()
        
        except Exception as e:
            logger.error(f"Test suite failed with unexpected error: {e}")
            logger.error(traceback.format_exc())
        
        finally:
            # Ensure robot is disconnected
            if self.robot and self.robot.is_connected:
                try:
                    self.robot.disconnect()
                except:
                    pass
        
        return self.test_results
    
    def print_test_summary(self) -> None:
        """Print test results summary."""
        logger.info("\n" + "="*50)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("="*50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["passed"])
        
        logger.info(f"Total tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success rate: {passed_tests/total_tests*100:.1f}%")
        
        logger.info("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "✓ PASS" if result["passed"] else "✗ FAIL"
            logger.info(f"  {test_name}: {status}")
            if not result["passed"] and result["error"]:
                logger.info(f"    Error: {result['error']}")


def create_test_config() -> Genie01Config:
    """Create test configuration for Genie01."""
    # Create camera configuration for testing
    # Note: Due to bandwidth limitations mentioned in config_agibot.py,
    # multiple cameras might need separate USB hubs or PCIe cards
    camera_config = {"cameras": GenieCameraConfig(
        camera_group=["head", "head_depth", "hand_left", "hand_right"],
        width=640,
        height=480,
        fps=30
    )}

    return Genie01Config(
        id="test_genie01",
        port="/dev/ttyUSB0",  # Placeholder port
        disable_torque_on_disconnect=True,
        max_relative_target=5,  # Safe movement limit
        cameras=camera_config
    )


def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test Genie01 (Agibot) robot")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    parser.add_argument("--test", choices=["connection", "observation", "action", "error", "all"], 
                       default="all", help="Specific test to run")
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Check environment
    # env_script = os.path.expanduser("~/workspace/a2d_sdk/env.sh")
    # if os.path.exists(env_script) and "A2D_SDK_ROOT" not in os.environ:
    #     logger.error(f"Please source the environment first: source {env_script}")
    #     sys.exit(1)
    
    # Create test configuration
    config = create_test_config()
    
    # Create tester
    tester = AgibotTester(config)
    
    # Run tests
    if args.test == "all":
        results = tester.run_all_tests()
    else:
        # Run specific test (simplified for this example)
        logger.info(f"Running specific test: {args.test}")
        results = tester.run_all_tests()  # For now, run all tests
    
    # Print summary
    tester.print_test_summary()
    
    # Exit with appropriate code
    all_passed = all(result["passed"] for result in results.values())
    sys.exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
