#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Camera configuration tests for Genie01 robot.

This script tests camera configuration handling, bandwidth limitations,
and USB hub requirements as mentioned in config_agibot.py.
"""

import sys
import unittest
import logging
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path for development
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from jzrobot.robots.agibot_g01.agibot import Genie01
    from jzrobot.robots.agibot_g01.config_agibot import Genie01Config
    from jzrobot.cameras.configs import GenieCameraConfig
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install the package first: python install.py")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.WARNING)


class TestCameraConfiguration(unittest.TestCase):
    """Test camera configuration for Genie01 robot."""
    
    def test_empty_camera_config(self):
        """Test robot with no cameras configured."""
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras={}  # Empty camera dict
        )
        
        robot = Genie01(config)
        self.assertEqual(len(config.cameras), 0)
        self.assertIsNone(robot.cameras)
    
    def test_single_camera_config(self):
        """Test robot with single camera (no bandwidth issues)."""
        camera_config = {
            "head_color": {
                "type": "genie",
                "width": 640,
                "height": 480,
                "fps": 30
            }
        }
        
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=camera_config
        )
        
        robot = Genie01(config)
        self.assertEqual(len(config.cameras), 1)
        self.assertIn("head_color", config.cameras)
    
    def test_multiple_cameras_bandwidth_concern(self):
        """Test multiple cameras that may cause bandwidth issues."""
        # This configuration might require separate USB hubs as mentioned in config
        high_bandwidth_cameras = {
            "head_color": {
                "type": "genie",
                "width": 1920,  # High resolution
                "height": 1080,
                "fps": 60  # High frame rate
            },
            "head_depth": {
                "type": "genie",
                "width": 1920,
                "height": 1080,
                "fps": 60
            },
            "hand_left_color": {
                "type": "genie",
                "width": 1920,
                "height": 1080,
                "fps": 60
            },
            "hand_right_color": {
                "type": "genie",
                "width": 1920,
                "height": 1080,
                "fps": 60
            }
        }
        
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=high_bandwidth_cameras
        )
        
        # Should create config successfully
        robot = Genie01(config)
        self.assertEqual(len(config.cameras), 4)
        
        # In real usage, this would require proper USB hub setup
        # to avoid bandwidth limitations mentioned in the config comment
    
    def test_standard_camera_setup(self):
        """Test standard camera setup for typical usage."""
        # Standard resolution cameras that should work on single USB controller
        standard_cameras = {
            "head_color": {
                "type": "genie",
                "width": 640,
                "height": 480,
                "fps": 30
            },
            "head_depth": {
                "type": "genie",
                "width": 640,
                "height": 480,
                "fps": 30
            },
            "hand_left_color": {
                "type": "genie",
                "width": 640,
                "height": 480,
                "fps": 30
            },
            "hand_right_color": {
                "type": "genie",
                "width": 640,
                "height": 480,
                "fps": 30
            }
        }
        
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=standard_cameras
        )
        
        robot = Genie01(config)
        self.assertEqual(len(config.cameras), 4)
        
        # Verify all expected cameras are configured
        expected_cameras = ["head_color", "head_depth", "hand_left_color", "hand_right_color"]
        for cam_name in expected_cameras:
            self.assertIn(cam_name, config.cameras)
            cam_config = config.cameras[cam_name]
            self.assertEqual(cam_config["type"], "genie")
            self.assertEqual(cam_config["width"], 640)
            self.assertEqual(cam_config["height"], 480)
            self.assertEqual(cam_config["fps"], 30)
    
    def test_mixed_resolution_cameras(self):
        """Test mixed resolution cameras (some high, some low)."""
        mixed_cameras = {
            "head_color": {
                "type": "genie",
                "width": 1920,  # High resolution for head
                "height": 1080,
                "fps": 30
            },
            "head_depth": {
                "type": "genie",
                "width": 640,   # Lower resolution for depth
                "height": 480,
                "fps": 30
            },
            "hand_left_color": {
                "type": "genie",
                "width": 640,   # Standard resolution for hands
                "height": 480,
                "fps": 30
            },
            "hand_right_color": {
                "type": "genie",
                "width": 640,
                "height": 480,
                "fps": 30
            }
        }
        
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=mixed_cameras
        )
        
        robot = Genie01(config)
        self.assertEqual(len(config.cameras), 4)
        
        # Verify mixed resolutions
        self.assertEqual(config.cameras["head_color"]["width"], 1920)
        self.assertEqual(config.cameras["head_depth"]["width"], 640)
        self.assertEqual(config.cameras["hand_left_color"]["width"], 640)
        self.assertEqual(config.cameras["hand_right_color"]["width"], 640)
    
    def test_camera_bandwidth_calculation(self):
        """Test estimated bandwidth calculation for camera configurations."""
        def calculate_bandwidth_mbps(width, height, fps, bytes_per_pixel=3):
            """Calculate estimated bandwidth in Mbps."""
            pixels_per_second = width * height * fps
            bytes_per_second = pixels_per_second * bytes_per_pixel
            bits_per_second = bytes_per_second * 8
            mbps = bits_per_second / (1024 * 1024)
            return mbps
        
        # Test high bandwidth configuration
        high_bandwidth_cam = {
            "width": 1920,
            "height": 1080,
            "fps": 60
        }
        
        high_bw = calculate_bandwidth_mbps(**high_bandwidth_cam)
        
        # Test standard bandwidth configuration
        standard_cam = {
            "width": 640,
            "height": 480,
            "fps": 30
        }
        
        standard_bw = calculate_bandwidth_mbps(**standard_cam)
        
        # High resolution should require significantly more bandwidth
        self.assertGreater(high_bw, standard_bw * 10)
        
        # Standard configuration should be reasonable for USB 2.0/3.0
        self.assertLess(standard_bw, 100)  # Less than 100 Mbps
        
        # Multiple high-bandwidth cameras would exceed typical USB limits
        total_high_bw = high_bw * 4  # 4 cameras
        usb3_limit = 5000  # USB 3.0 theoretical limit ~5 Gbps
        
        if total_high_bw > usb3_limit:
            # This would require separate USB controllers/hubs
            # as mentioned in the config comment
            pass
    
    def test_camera_config_validation(self):
        """Test camera configuration validation."""
        # Test invalid camera type
        invalid_camera = {
            "head_color": {
                "type": "invalid_type",
                "width": 640,
                "height": 480,
                "fps": 30
            }
        }
        
        # Should still create config (validation happens at runtime)
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=invalid_camera
        )
        
        self.assertEqual(len(config.cameras), 1)
        self.assertEqual(config.cameras["head_color"]["type"], "invalid_type")
    
    def test_camera_config_bandwidth_recommendations(self):
        """Test bandwidth recommendations based on camera count and resolution."""
        def get_bandwidth_recommendation(camera_config):
            """Get bandwidth recommendation based on camera configuration."""
            total_cameras = len(camera_config)
            high_res_cameras = sum(1 for cam in camera_config.values() 
                                 if cam.get("width", 0) > 1280 or cam.get("fps", 0) > 30)
            
            if total_cameras <= 2 and high_res_cameras == 0:
                return "single_usb_ok"
            elif total_cameras <= 4 and high_res_cameras <= 1:
                return "usb_hub_recommended"
            else:
                return "separate_usb_controllers_required"
        
        # Test scenarios
        scenarios = [
            # Low bandwidth scenario
            ({
                "head_color": {"width": 640, "height": 480, "fps": 30}
            }, "single_usb_ok"),
            
            # Medium bandwidth scenario
            ({
                "head_color": {"width": 640, "height": 480, "fps": 30},
                "head_depth": {"width": 640, "height": 480, "fps": 30},
                "hand_left": {"width": 640, "height": 480, "fps": 30}
            }, "usb_hub_recommended"),
            
            # High bandwidth scenario
            ({
                "head_color": {"width": 1920, "height": 1080, "fps": 60},
                "head_depth": {"width": 1920, "height": 1080, "fps": 60},
                "hand_left": {"width": 1920, "height": 1080, "fps": 60},
                "hand_right": {"width": 1920, "height": 1080, "fps": 60}
            }, "separate_usb_controllers_required")
        ]
        
        for camera_config, expected_recommendation in scenarios:
            recommendation = get_bandwidth_recommendation(camera_config)
            self.assertEqual(recommendation, expected_recommendation)


def run_camera_tests():
    """Run all camera configuration tests."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test class
    suite.addTests(loader.loadTestsFromTestCase(TestCameraConfiguration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_camera_tests()
    sys.exit(0 if success else 1)
