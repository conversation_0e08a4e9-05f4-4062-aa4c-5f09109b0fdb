# Robot Test Suite

This directory contains comprehensive test scripts for the JZRobot framework, specifically for the Genie01 (Agibot) robot implementation.

## Test Scripts

### 1. `test_agibot.py` - Comprehensive Integration Tests
Full integration testing for the Genie01 robot with real hardware interaction.

**Features:**
- Connection and disconnection testing
- Observation collection validation
- Action sending verification
- Error handling scenarios
- Performance monitoring

**Usage:**
```bash
# Source environment first (only once per terminal)
source ~/workspace/a2d_sdk/env.sh

# Run all tests
python test/robot/test_agibot.py

# Run with verbose output
python test/robot/test_agibot.py --verbose

# Run specific test
python test/robot/test_agibot.py --test observation
```

**Safety Note:** This script interacts with real robot hardware. Ensure the robot is in a safe environment.

### 2. `test_agibot_units.py` - Unit Tests
Isolated unit tests for individual components and methods using mocks.

**Features:**
- Configuration testing
- Method-level testing
- Mock-based testing (no hardware required)
- Component validation
- Error condition testing

**Usage:**
```bash
# Run unit tests (no hardware required)
python test/robot/test_agibot_units.py
```

**Safety Note:** This script uses mocks and does not require real hardware.

### 3. `benchmark_agibot.py` - Performance Benchmarks
Performance benchmarking for robot operations.

**Features:**
- Observation latency measurement
- Action sending latency measurement
- Throughput testing
- Observation-action loop performance
- Statistical analysis (mean, median, P95, P99)

**Usage:**
```bash
# Source environment first
source ~/workspace/a2d_sdk/env.sh

# Run all benchmarks
python test/robot/benchmark_agibot.py

# Run with custom parameters
python test/robot/benchmark_agibot.py --samples 200 --duration 15

# Save results to file
python test/robot/benchmark_agibot.py --output benchmark_results.json

# Run specific benchmark
python test/robot/benchmark_agibot.py --test latency
```

**Safety Note:** This script sends commands to real hardware with small, safe movements.

## Test Categories

### Connection Tests
- Robot initialization
- Hardware connection establishment
- Connection state validation
- Disconnection handling
- Error scenarios (SDK not available, already connected, etc.)

### Observation Tests
- Feature definition validation
- Observation data structure verification
- Data consistency checks
- Camera integration testing
- Performance measurement

### Action Tests
- Action sending for different components:
  - Joint positions (14 DOF arms)
  - Gripper positions (2 DOF)
  - Head positions (2 DOF)
  - Waist positions (2 DOF)
  - Hand positions (12 DOF)
- Action validation and safety checks
- Performance measurement

### Error Handling Tests
- Disconnected robot scenarios
- Invalid action handling
- Hardware communication errors
- Recovery mechanisms

## Performance Expectations

Based on the robot architecture, expected performance characteristics:

### Latency Targets
- **Observation latency**: < 30ms (target: 10-20ms)
- **Action latency**: < 10ms (target: 2-5ms)
- **End-to-end loop**: < 50ms (target: 20-30ms)

### Throughput Targets
- **Observation rate**: > 30 Hz (target: 50-100 Hz)
- **Action rate**: > 50 Hz (target: 100+ Hz)
- **Loop rate**: > 20 Hz (target: 30+ Hz)

## Prerequisites

### Environment Setup
```bash
# Source the a2d_sdk environment (required for hardware interaction)
source ~/workspace/a2d_sdk/env.sh
```

### Hardware Requirements
- Genie01 robot connected and powered
- Proper USB/communication interface
- Robot in safe operational environment

### Software Dependencies
- Python 3.8+
- numpy
- a2d_sdk (for hardware communication)
- unittest (for unit tests)
- json (for result storage)

## Running Tests Safely

### Safety Guidelines
1. **Always test in a safe environment** with adequate clearance
2. **Have emergency stop readily available**
3. **Start with unit tests** before hardware tests
4. **Use small movements** during action testing
5. **Monitor robot behavior** during all tests

### Test Sequence Recommendation
1. Run unit tests first: `python test/robot/test_agibot_units.py`
2. Run observation-only tests: `python test/robot/test_agibot.py --test observation`
3. Run full integration tests: `python test/robot/test_agibot.py`
4. Run performance benchmarks: `python test/robot/benchmark_agibot.py`

## Interpreting Results

### Test Results
- **PASS**: Test completed successfully
- **FAIL**: Test failed with error details
- **Success Rate**: Percentage of tests passed

### Performance Metrics
- **Mean**: Average performance
- **P95/P99**: 95th/99th percentile (worst-case scenarios)
- **Throughput**: Operations per second
- **Latency**: Time per operation

### Common Issues
- **Connection failures**: Check hardware connection and environment setup
- **High latency**: May indicate system load or hardware issues
- **Action failures**: Verify robot is in safe operational state
- **Camera errors**: Check camera connections and configurations

## Troubleshooting

### Environment Issues
```bash
# Verify environment is sourced
echo $A2D_SDK_ROOT

# Re-source if needed
source ~/workspace/a2d_sdk/env.sh
```

### Hardware Issues
- Check robot power and connections
- Verify robot is not in error state
- Ensure proper permissions for device access
- Check for conflicting processes using the robot

### Test Failures
- Review error messages and stack traces
- Check robot logs for hardware-specific errors
- Verify test environment meets safety requirements
- Ensure robot is in expected initial state

## Contributing

When adding new tests:
1. Follow the existing test structure and naming conventions
2. Include both positive and negative test cases
3. Add appropriate safety checks and error handling
4. Document expected behavior and performance characteristics
5. Test with both mock and real hardware scenarios
