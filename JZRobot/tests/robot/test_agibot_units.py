#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unit tests for Genie01 (Agibot) robot components.

This script provides focused unit tests for individual methods and components
of the Genie01 robot implementation.
"""

import os
import sys
import unittest
import logging
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np

# Add src to path for development
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from jzrobot.robots.agibot_g01.agibot import Genie01
    from jzrobot.robots.agibot_g01.config_agibot import Genie01Config
    from jzrobot.errors import DeviceNotConnectedError, DeviceAlreadyConnectedError
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install the package first: python install.py")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise during tests


class TestGenie01Config(unittest.TestCase):
    """Test Genie01Config class."""

    def test_config_creation(self):
        """Test basic config creation."""
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            disable_torque_on_disconnect=True,
            max_relative_target=5
        )

        self.assertEqual(config.id, "test_robot")
        self.assertEqual(config.port, "/dev/ttyUSB0")
        self.assertTrue(config.disable_torque_on_disconnect)
        self.assertEqual(config.max_relative_target, 5)
        self.assertIsInstance(config.cameras, dict)
        self.assertEqual(len(config.cameras), 0)  # Default empty cameras dict

    def test_config_defaults(self):
        """Test config default values."""
        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0"
        )

        self.assertTrue(config.disable_torque_on_disconnect)
        self.assertEqual(config.max_relative_target, 5)
        self.assertIsInstance(config.cameras, dict)
        self.assertEqual(len(config.cameras), 0)  # Default empty cameras dict

    def test_config_with_cameras(self):
        """Test config creation with camera configuration."""
        from jzrobot.cameras.genie.configuration_genie import GenieCameraConfig

        # Use new camera config structure
        camera_config = {"cameras": GenieCameraConfig(
            camera_group=["head", "head_depth"],
            width=640,
            height=480,
            fps=30
        )}

        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=camera_config
        )

        self.assertEqual(len(config.cameras), 1)  # One "cameras" key
        self.assertIn("cameras", config.cameras)
        self.assertEqual(config.cameras["cameras"].width, 640)
        self.assertEqual(config.cameras["cameras"].height, 480)
        self.assertEqual(len(config.cameras["cameras"].camera_group), 2)

    def test_config_camera_bandwidth_note(self):
        """Test that camera configuration handles bandwidth limitations."""
        from jzrobot.cameras.genie.configuration_genie import GenieCameraConfig

        # This test verifies the comment about USB hub/PCIe card requirements
        # for multiple cameras to avoid bandwidth issues

        # Simulate multiple high-bandwidth cameras
        high_bandwidth_cameras = {"cameras": GenieCameraConfig(
            camera_group=["head", "head_depth", "hand_left", "hand_right"],
            width=1920,  # High resolution
            height=1080,
            fps=60  # High frame rate
        )}

        config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            cameras=high_bandwidth_cameras
        )

        # Should still create config successfully
        self.assertEqual(len(config.cameras), 1)  # One "cameras" key
        self.assertEqual(config.cameras["cameras"].width, 1920)
        self.assertEqual(config.cameras["cameras"].height, 1080)
        self.assertEqual(len(config.cameras["cameras"].camera_group), 4)

        # Test bandwidth estimation
        estimated_bandwidth = config.cameras["cameras"].estimate_bandwidth_mbps()
        self.assertGreater(estimated_bandwidth, 100)  # Should be high bandwidth

        # Test USB hub recommendation
        self.assertTrue(config.cameras["cameras"].requires_separate_usb_hubs())

        # In real implementation, this would require proper USB hub setup
        # as mentioned in the comment about bandwidth limitations


class TestGenie01Robot(unittest.TestCase):
    """Test Genie01 robot class."""

    def setUp(self):
        """Set up test fixtures."""
        self.config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0",
            disable_torque_on_disconnect=True,
            max_relative_target=5
        )

        # Start with empty camera configuration to avoid initialization issues
        self.config.cameras = {}

        self.robot = Genie01(self.config)

    def test_robot_with_camera_config(self):
        """Test robot initialization with camera configuration."""
        from jzrobot.cameras.genie.configuration_genie import GenieCameraConfig

        # Create config with new camera structure
        camera_config = {"cameras": GenieCameraConfig(
            camera_group=["head", "head_depth", "hand_left", "hand_right"],
            width=640,
            height=480,
            fps=30
        )}

        config_with_cameras = Genie01Config(
            id="test_robot_with_cameras",
            port="/dev/ttyUSB0",
            cameras=camera_config
        )

        # Robot should initialize even with camera config
        # (actual camera connection would happen in connect())
        robot_with_cameras = Genie01(config_with_cameras)

        self.assertEqual(robot_with_cameras.name, "genie01")
        self.assertEqual(len(config_with_cameras.cameras), 1)  # One "cameras" key
        self.assertIn("cameras", config_with_cameras.cameras)
        self.assertFalse(robot_with_cameras.is_connected)

        # Test that camera object was created
        self.assertIsNotNone(robot_with_cameras.cameras)
        self.assertEqual(len(robot_with_cameras.cameras.config.camera_group), 4)
    
    def test_robot_initialization(self):
        """Test robot initialization."""
        self.assertEqual(self.robot.name, "genie01")
        self.assertEqual(self.robot.config, self.config)
        self.assertFalse(self.robot.is_connected)
        self.assertIsNone(self.robot.robot)
        self.assertIsNone(self.robot._motion_controller)
    
    def test_motor_features(self):
        """Test motor feature definitions."""
        features = self.robot._motors_ft
        
        # Check expected feature keys
        expected_keys = [
            "state.joint.position",
            "state.effector.position", 
            "state.effector.force",
            "state.end.position",
            "state.head.position",
            "state.waist.position"
        ]
        
        for key in expected_keys:
            self.assertIn(key, features)
            self.assertIn("dtype", features[key])
            self.assertIn("shape", features[key])
            self.assertIn("names", features[key])
    
    def test_observation_features(self):
        """Test observation features property."""
        obs_features = self.robot.observation_features
        motor_features = self.robot._motors_ft
        
        # Should include all motor features
        for key in motor_features:
            self.assertIn(key, obs_features)
    
    def test_action_features(self):
        """Test action features property."""
        action_features = self.robot.action_features
        motor_features = self.robot._motors_ft

        # Action features should have same structure as motor features but with "action." prefix
        self.assertEqual(len(action_features), len(motor_features))

        # Check that all motor feature keys are converted from "state." to "action."
        for motor_key, motor_value in motor_features.items():
            if motor_key.startswith("state."):
                action_key = motor_key.replace("state.", "action.", 1)
                self.assertIn(action_key, action_features)
                self.assertEqual(action_features[action_key], motor_value)
            else:
                # Non-state keys should remain unchanged
                self.assertIn(motor_key, action_features)
                self.assertEqual(action_features[motor_key], motor_value)

        # Verify specific expected action keys
        expected_action_keys = [
            "action.joint.position",
            "action.effector.position",
            "action.effector.force",
            "action.end.position",
            "action.head.position",
            "action.waist.position"
        ]

        for key in expected_action_keys:
            self.assertIn(key, action_features)
    
    @patch('jzrobot.robots.agibot_g01.agibot.RobotDds')
    @patch('jzrobot.robots.agibot_g01.agibot.RobotController')
    def test_connection(self, mock_controller, mock_robot_dds):
        """Test robot connection."""
        # Mock the robot hardware
        mock_robot_instance = Mock()
        mock_controller_instance = Mock()
        mock_robot_dds.return_value = mock_robot_instance
        mock_controller.return_value = mock_controller_instance
        
        # Test connection
        self.robot.connect()
        
        # Verify connection state
        self.assertTrue(self.robot.is_connected)
        self.assertIsNotNone(self.robot.robot)
        self.assertIsNotNone(self.robot._motion_controller)
        
        # Verify mocks were called
        mock_robot_dds.assert_called_once()
        mock_controller.assert_called_once()
    
    def test_connection_already_connected(self):
        """Test connection when already connected."""
        # Simulate connected state
        self.robot._connected = True
        
        with self.assertRaises(DeviceAlreadyConnectedError):
            self.robot.connect()
    
    @patch('jzrobot.robots.agibot_g01.agibot.RobotDds', None)
    def test_connection_no_sdk(self):
        """Test connection when SDK is not available."""
        with self.assertRaises(ImportError):
            self.robot.connect()
    
    def test_observation_not_connected(self):
        """Test observation when not connected."""
        with self.assertRaises(DeviceNotConnectedError):
            self.robot.get_observation()
    
    def test_action_not_connected(self):
        """Test action when not connected."""
        action = {"state.joint.position": [0.0] * 14}
        
        with self.assertRaises(DeviceNotConnectedError):
            self.robot.send_action(action)
    
    def test_disconnect_not_connected(self):
        """Test disconnect when not connected."""
        with self.assertRaises(DeviceNotConnectedError):
            self.robot.disconnect()
    
    @patch('jzrobot.robots.agibot_g01.agibot.RobotDds')
    @patch('jzrobot.robots.agibot_g01.agibot.RobotController')
    def test_full_lifecycle(self, mock_controller, mock_robot_dds):
        """Test full robot lifecycle: connect -> observe -> act -> disconnect."""
        # Mock the robot hardware
        mock_robot_instance = Mock()
        mock_controller_instance = Mock()
        mock_robot_dds.return_value = mock_robot_instance
        mock_controller.return_value = mock_controller_instance
        
        # Mock robot methods
        mock_robot_instance.arm_joint_states.return_value = ([0.0] * 14, 0.0)
        mock_robot_instance.gripper_states.return_value = ([0.0, 0.0], 0.0)
        mock_robot_instance.hand_force_states.return_value = ([0.0] * 12, 0.0)
        mock_robot_instance.head_joint_states.return_value = ([0.0, 0.0], 0.0)
        mock_robot_instance.waist_joint_states.return_value = ([0.0, 0.0], 0.0)
        
        # Mock motion controller
        mock_controller_instance.get_motion_status.return_value = {
            'frames': {
                'arm_left_link7': {
                    'position': {'x': 0.0, 'y': 0.0, 'z': 0.0},
                    'orientation': {'quaternion': {'x': 0.0, 'y': 0.0, 'z': 0.0, 'w': 1.0}}
                },
                'arm_right_link7': {
                    'position': {'x': 0.0, 'y': 0.0, 'z': 0.0},
                    'orientation': {'quaternion': {'x': 0.0, 'y': 0.0, 'z': 0.0, 'w': 1.0}}
                }
            }
        }
        
        # Connect
        self.robot.connect()
        self.assertTrue(self.robot.is_connected)
        
        # Observe
        observation = self.robot.get_observation()
        self.assertIsInstance(observation, dict)
        
        # Act
        action = {"state.joint.position": [0.01] * 14}
        returned_action = self.robot.send_action(action)
        self.assertEqual(returned_action, action)
        
        # Disconnect
        self.robot.disconnect()
        self.assertFalse(self.robot.is_connected)


class TestGenie01Methods(unittest.TestCase):
    """Test individual methods of Genie01."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = Genie01Config(
            id="test_robot",
            port="/dev/ttyUSB0"
        )
        self.config.cameras = None  # Avoid camera initialization
        self.robot = Genie01(self.config)
        
        # Mock robot hardware
        self.robot.robot = Mock()
        self.robot._motion_controller = Mock()
        self.robot._connected = True
    
    def test_get_ee_pose(self):
        """Test end-effector pose retrieval."""
        # Mock motion controller response
        self.robot._motion_controller.get_motion_status.return_value = {
            'frames': {
                'arm_left_link7': {
                    'position': {'x': 1.0, 'y': 2.0, 'z': 3.0},
                    'orientation': {'quaternion': {'x': 0.1, 'y': 0.2, 'z': 0.3, 'w': 0.9}}
                },
                'arm_right_link7': {
                    'position': {'x': 4.0, 'y': 5.0, 'z': 6.0},
                    'orientation': {'quaternion': {'x': 0.4, 'y': 0.5, 'z': 0.6, 'w': 0.8}}
                }
            }
        }
        
        ee_pose = self.robot._get_ee_pose()
        
        self.assertIsInstance(ee_pose, np.ndarray)
        self.assertEqual(len(ee_pose), 14)  # 7 DOF per arm
        
        # Check left arm pose
        np.testing.assert_array_equal(ee_pose[:7], [1.0, 2.0, 3.0, 0.1, 0.2, 0.3, 0.9])
        # Check right arm pose
        np.testing.assert_array_equal(ee_pose[7:], [4.0, 5.0, 6.0, 0.4, 0.5, 0.6, 0.8])
    
    def test_get_proprioceptive(self):
        """Test proprioceptive data retrieval."""
        # Mock robot methods
        self.robot.robot.arm_joint_states.return_value = ([0.1] * 14, 0.0)
        self.robot.robot.gripper_states.return_value = ([0.2, 0.3], 0.0)
        self.robot.robot.hand_force_states.return_value = ([0.4] * 12, 0.0)
        self.robot.robot.head_joint_states.return_value = ([0.5, 0.6], 0.0)
        self.robot.robot.waist_joint_states.return_value = ([0.7, 0.8], 0.0)
        
        # Mock _get_ee_pose
        with patch.object(self.robot, '_get_ee_pose', return_value=np.array([0.9] * 14)):
            proprioceptive = self.robot._get_proprioceptive()
        
        self.assertIsInstance(proprioceptive, dict)
        
        # Check expected keys
        expected_keys = [
            "observation.state.joint.position",
            "observation.state.effector.position",
            "observation.state.effector.force",
            "observation.state.end.position",
            "observation.state.head.position",
            "observation.state.waist.position"
        ]
        
        for key in expected_keys:
            self.assertIn(key, proprioceptive)
    
    def test_get_images_no_cameras(self):
        """Test image retrieval when no cameras are available."""
        images = self.robot._get_images()
        self.assertIsInstance(images, dict)
        self.assertEqual(len(images), 0)
    
    def test_send_action_components(self):
        """Test action sending for different components."""
        # Mock robot methods
        self.robot.robot.move_arm = Mock()
        self.robot.robot.move_gripper = Mock()
        self.robot.robot.move_head = Mock()
        self.robot.robot.move_waist = Mock()
        self.robot.robot.move_hand = Mock()
        
        # Test different action types
        actions = {
            "state.joint.position": [0.1] * 14,
            "state.effector.position": [0.2, 0.3],
            "state.head.position": [0.4, 0.5],
            "state.waist.position": [0.6, 0.7],
            "state.hand.position": [0.8] * 12
        }
        
        returned_action = self.robot.send_action(actions)
        
        # Verify methods were called
        self.robot.robot.move_arm.assert_called_with([0.1] * 14)
        self.robot.robot.move_gripper.assert_called_with([0.2, 0.3])
        self.robot.robot.move_head.assert_called_with([0.4, 0.5])
        self.robot.robot.move_waist.assert_called_with([0.6, 0.7])
        self.robot.robot.move_hand.assert_called_with([0.8] * 12)
        
        # Verify returned action
        self.assertEqual(returned_action, actions)


def run_tests():
    """Run all unit tests."""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestGenie01Config))
    suite.addTests(loader.loadTestsFromTestCase(TestGenie01Robot))
    suite.addTests(loader.loadTestsFromTestCase(TestGenie01Methods))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
