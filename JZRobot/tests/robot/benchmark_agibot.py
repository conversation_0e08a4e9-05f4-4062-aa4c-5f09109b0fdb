#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Performance benchmark script for Genie01 (Agibot) robot.

This script measures the performance characteristics of the Genie01 robot
implementation, including observation latency, action sending speed, and
throughput metrics.
"""

import os
import sys
import time
import logging
import argparse
import statistics
from pathlib import Path
from typing import List, Dict, Any
import numpy as np
import json

# Add src to path for development
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from jzrobot.robots.agibot_g01.agibot import Genie01
    from jzrobot.robots.agibot_g01.config_agibot import Genie01Config
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install the package first: python install.py")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("benchmark_agibot")


class AgibotBenchmark:
    """Performance benchmark suite for Genie01 robot."""
    
    def __init__(self, config: Genie01Config):
        self.config = config
        self.robot = Genie01(config)
        self.results = {}
    
    def setup(self) -> bool:
        """Setup robot for benchmarking."""
        try:
            logger.info("Setting up robot for benchmarking...")
            self.robot.connect()
            logger.info("Robot connected successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to setup robot: {e}")
            return False
    
    def teardown(self) -> None:
        """Cleanup after benchmarking."""
        try:
            if self.robot.is_connected:
                self.robot.disconnect()
                logger.info("Robot disconnected")
        except Exception as e:
            logger.error(f"Error during teardown: {e}")
    
    def benchmark_observation_latency(self, num_samples: int = 100) -> Dict[str, float]:
        """Benchmark observation collection latency."""
        logger.info(f"Benchmarking observation latency with {num_samples} samples...")
        
        latencies = []
        
        # Warm-up
        for _ in range(10):
            self.robot.get_observation()
        
        # Actual benchmark
        for i in range(num_samples):
            start_time = time.perf_counter()
            observation = self.robot.get_observation()
            end_time = time.perf_counter()
            
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            if (i + 1) % 20 == 0:
                logger.info(f"Progress: {i + 1}/{num_samples} observations")
        
        # Calculate statistics
        results = {
            "mean_ms": statistics.mean(latencies),
            "median_ms": statistics.median(latencies),
            "std_ms": statistics.stdev(latencies) if len(latencies) > 1 else 0.0,
            "min_ms": min(latencies),
            "max_ms": max(latencies),
            "p95_ms": np.percentile(latencies, 95),
            "p99_ms": np.percentile(latencies, 99),
            "samples": num_samples
        }
        
        logger.info(f"Observation latency - Mean: {results['mean_ms']:.2f}ms, "
                   f"P95: {results['p95_ms']:.2f}ms, P99: {results['p99_ms']:.2f}ms")
        
        return results
    
    def benchmark_action_latency(self, num_samples: int = 100) -> Dict[str, float]:
        """Benchmark action sending latency."""
        logger.info(f"Benchmarking action latency with {num_samples} samples...")
        
        latencies = []
        
        # Create test action
        test_action = {
            "state.joint.position": [0.01] * 14,  # Small movements
            "state.effector.position": [0.1, 0.1]
        }
        
        # Warm-up
        for _ in range(10):
            self.robot.send_action(test_action)
            time.sleep(0.01)
        
        # Actual benchmark
        for i in range(num_samples):
            start_time = time.perf_counter()
            self.robot.send_action(test_action)
            end_time = time.perf_counter()
            
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            if (i + 1) % 20 == 0:
                logger.info(f"Progress: {i + 1}/{num_samples} actions")
            
            # Brief pause to avoid overwhelming the robot
            time.sleep(0.01)
        
        # Calculate statistics
        results = {
            "mean_ms": statistics.mean(latencies),
            "median_ms": statistics.median(latencies),
            "std_ms": statistics.stdev(latencies) if len(latencies) > 1 else 0.0,
            "min_ms": min(latencies),
            "max_ms": max(latencies),
            "p95_ms": np.percentile(latencies, 95),
            "p99_ms": np.percentile(latencies, 99),
            "samples": num_samples
        }
        
        logger.info(f"Action latency - Mean: {results['mean_ms']:.2f}ms, "
                   f"P95: {results['p95_ms']:.2f}ms, P99: {results['p99_ms']:.2f}ms")
        
        return results
    
    def benchmark_observation_throughput(self, duration_seconds: float = 10.0) -> Dict[str, float]:
        """Benchmark observation collection throughput."""
        logger.info(f"Benchmarking observation throughput for {duration_seconds} seconds...")
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        observation_count = 0
        
        while time.perf_counter() < end_time:
            self.robot.get_observation()
            observation_count += 1
        
        actual_duration = time.perf_counter() - start_time
        throughput_hz = observation_count / actual_duration
        
        results = {
            "throughput_hz": throughput_hz,
            "total_observations": observation_count,
            "duration_seconds": actual_duration
        }
        
        logger.info(f"Observation throughput: {throughput_hz:.1f} Hz "
                   f"({observation_count} observations in {actual_duration:.2f}s)")
        
        return results
    
    def benchmark_action_throughput(self, duration_seconds: float = 10.0) -> Dict[str, float]:
        """Benchmark action sending throughput."""
        logger.info(f"Benchmarking action throughput for {duration_seconds} seconds...")
        
        # Create test action
        test_action = {
            "state.joint.position": [0.005] * 14,  # Very small movements
            "state.effector.position": [0.05, 0.05]
        }
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        action_count = 0
        
        while time.perf_counter() < end_time:
            self.robot.send_action(test_action)
            action_count += 1
            time.sleep(0.001)  # Small delay to avoid overwhelming
        
        actual_duration = time.perf_counter() - start_time
        throughput_hz = action_count / actual_duration
        
        results = {
            "throughput_hz": throughput_hz,
            "total_actions": action_count,
            "duration_seconds": actual_duration
        }
        
        logger.info(f"Action throughput: {throughput_hz:.1f} Hz "
                   f"({action_count} actions in {actual_duration:.2f}s)")
        
        return results
    
    def benchmark_observation_action_loop(self, duration_seconds: float = 10.0) -> Dict[str, float]:
        """Benchmark observation-action loop performance."""
        logger.info(f"Benchmarking observation-action loop for {duration_seconds} seconds...")
        
        # Create test action
        test_action = {
            "state.joint.position": [0.002] * 14,  # Tiny movements
            "state.effector.position": [0.02, 0.02]
        }
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        loop_count = 0
        obs_times = []
        action_times = []
        
        while time.perf_counter() < end_time:
            # Observation
            obs_start = time.perf_counter()
            observation = self.robot.get_observation()
            obs_end = time.perf_counter()
            obs_times.append((obs_end - obs_start) * 1000)
            
            # Action
            action_start = time.perf_counter()
            self.robot.send_action(test_action)
            action_end = time.perf_counter()
            action_times.append((action_end - action_start) * 1000)
            
            loop_count += 1
            
            # Small delay
            time.sleep(0.001)
        
        actual_duration = time.perf_counter() - start_time
        loop_rate_hz = loop_count / actual_duration
        
        results = {
            "loop_rate_hz": loop_rate_hz,
            "total_loops": loop_count,
            "duration_seconds": actual_duration,
            "avg_obs_latency_ms": statistics.mean(obs_times),
            "avg_action_latency_ms": statistics.mean(action_times),
            "avg_loop_time_ms": statistics.mean([o + a for o, a in zip(obs_times, action_times)])
        }
        
        logger.info(f"Observation-action loop: {loop_rate_hz:.1f} Hz "
                   f"({loop_count} loops in {actual_duration:.2f}s)")
        logger.info(f"Average loop time: {results['avg_loop_time_ms']:.2f}ms "
                   f"(obs: {results['avg_obs_latency_ms']:.2f}ms, "
                   f"action: {results['avg_action_latency_ms']:.2f}ms)")
        
        return results
    
    def run_all_benchmarks(self) -> Dict[str, Any]:
        """Run all benchmark tests."""
        logger.info("Starting comprehensive performance benchmarks...")
        
        if not self.setup():
            return {}
        
        try:
            # Run benchmarks
            self.results["observation_latency"] = self.benchmark_observation_latency()
            self.results["action_latency"] = self.benchmark_action_latency()
            self.results["observation_throughput"] = self.benchmark_observation_throughput()
            self.results["action_throughput"] = self.benchmark_action_throughput()
            self.results["observation_action_loop"] = self.benchmark_observation_action_loop()
            
            # Add metadata
            self.results["metadata"] = {
                "robot_type": self.robot.name,
                "timestamp": time.time(),
                "config": {
                    "max_relative_target": self.config.max_relative_target,
                    "disable_torque_on_disconnect": self.config.disable_torque_on_disconnect
                }
            }
            
        except Exception as e:
            logger.error(f"Benchmark failed: {e}")
        
        finally:
            self.teardown()
        
        return self.results
    
    def save_results(self, filename: str) -> None:
        """Save benchmark results to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            logger.info(f"Benchmark results saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save results: {e}")
    
    def print_summary(self) -> None:
        """Print benchmark results summary."""
        if not self.results:
            logger.error("No benchmark results available")
            return
        
        logger.info("\n" + "="*60)
        logger.info("PERFORMANCE BENCHMARK SUMMARY")
        logger.info("="*60)
        
        # Observation latency
        if "observation_latency" in self.results:
            obs_lat = self.results["observation_latency"]
            logger.info(f"Observation Latency:")
            logger.info(f"  Mean: {obs_lat['mean_ms']:.2f}ms")
            logger.info(f"  P95:  {obs_lat['p95_ms']:.2f}ms")
            logger.info(f"  P99:  {obs_lat['p99_ms']:.2f}ms")
        
        # Action latency
        if "action_latency" in self.results:
            act_lat = self.results["action_latency"]
            logger.info(f"Action Latency:")
            logger.info(f"  Mean: {act_lat['mean_ms']:.2f}ms")
            logger.info(f"  P95:  {act_lat['p95_ms']:.2f}ms")
            logger.info(f"  P99:  {act_lat['p99_ms']:.2f}ms")
        
        # Throughput
        if "observation_throughput" in self.results:
            obs_thr = self.results["observation_throughput"]
            logger.info(f"Observation Throughput: {obs_thr['throughput_hz']:.1f} Hz")
        
        if "action_throughput" in self.results:
            act_thr = self.results["action_throughput"]
            logger.info(f"Action Throughput: {act_thr['throughput_hz']:.1f} Hz")
        
        # Loop performance
        if "observation_action_loop" in self.results:
            loop = self.results["observation_action_loop"]
            logger.info(f"Observation-Action Loop: {loop['loop_rate_hz']:.1f} Hz")
            logger.info(f"  Average loop time: {loop['avg_loop_time_ms']:.2f}ms")
        
        logger.info("="*60)


def create_benchmark_config() -> Genie01Config:
    """Create configuration for benchmarking."""
    # Create optimized camera configuration for benchmarking
    # Note: For performance testing, consider reducing camera count or resolution
    # to avoid USB bandwidth limitations mentioned in config_agibot.py
    camera_config = {
        "head_color": {
            "type": "genie",
            "width": 640,  # Standard resolution for benchmarking
            "height": 480,
            "fps": 30
        },
        "head_depth": {
            "type": "genie",
            "width": 640,
            "height": 480,
            "fps": 30
        }
        # Note: Limiting to 2 cameras for benchmark to avoid bandwidth issues
        # Add more cameras only if using separate USB hubs/PCIe cards
    }

    return Genie01Config(
        id="benchmark_genie01",
        port="/dev/ttyUSB0",
        disable_torque_on_disconnect=True,
        max_relative_target=5,
        cameras=camera_config
    )


def main():
    """Main benchmark function."""
    parser = argparse.ArgumentParser(description="Benchmark Genie01 (Agibot) robot performance")
    parser.add_argument("--samples", type=int, default=100, help="Number of samples for latency tests")
    parser.add_argument("--duration", type=float, default=10.0, help="Duration for throughput tests")
    parser.add_argument("--output", type=str, help="Output file for results")
    parser.add_argument("--test", choices=["latency", "throughput", "loop", "all"], 
                       default="all", help="Specific benchmark to run")
    args = parser.parse_args()
    
    # Check environment
    env_script = os.path.expanduser("~/workspace/a2d_sdk/env.sh")
    if os.path.exists(env_script) and "A2D_SDK_ROOT" not in os.environ:
        logger.error(f"Please source the environment first: source {env_script}")
        sys.exit(1)
    
    # Create benchmark configuration
    config = create_benchmark_config()
    
    # Create benchmark suite
    benchmark = AgibotBenchmark(config)
    
    # Run benchmarks
    if args.test == "all":
        results = benchmark.run_all_benchmarks()
    else:
        # Run specific benchmark (simplified for this example)
        logger.info(f"Running specific benchmark: {args.test}")
        results = benchmark.run_all_benchmarks()  # For now, run all
    
    # Print summary
    benchmark.print_summary()
    
    # Save results if requested
    if args.output:
        benchmark.save_results(args.output)


if __name__ == "__main__":
    main()
