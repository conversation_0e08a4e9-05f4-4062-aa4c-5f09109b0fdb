#!/usr/bin/env python3

"""
Installation script for JZRobot framework.

This script installs the JZRobot package in development mode and sets up
the necessary dependencies.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(cmd, check=True, shell=False):
    """Run a command and return the result."""
    logger.info(f"Running: {cmd}")
    try:
        if isinstance(cmd, str) and not shell:
            cmd = cmd.split()
        
        result = subprocess.run(
            cmd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=shell
        )
        
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        if e.stderr:
            logger.error(f"Error: {e.stderr.strip()}")
        if not check:
            return e
        raise


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error(f"Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def install_package():
    """Install the JZRobot package in development mode."""
    logger.info("Installing JZRobot package in development mode...")
    
    # Install in development mode
    run_command([sys.executable, "-m", "pip", "install", "-e", "."])
    
    logger.info("JZRobot package installed successfully!")


def install_optional_dependencies():
    """Install optional dependencies."""
    logger.info("Installing optional dependencies...")
    
    optional_deps = {
        "cameras": ["opencv-python>=4.5.0"],
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0", 
            "black>=21.0",
            "isort>=5.0",
            "flake8>=3.8"
        ]
    }
    
    for category, deps in optional_deps.items():
        logger.info(f"Installing {category} dependencies...")
        try:
            for dep in deps:
                run_command([sys.executable, "-m", "pip", "install", dep])
        except Exception as e:
            logger.warning(f"Failed to install {category} dependencies: {e}")


def create_test_environment():
    """Create test environment setup."""
    logger.info("Setting up test environment...")
    
    # Create test directories if they don't exist
    test_dirs = [
        "test",
        "test/robot", 
        "test/cameras",
        "test/integration",
        "tmp_tests"
    ]
    
    for test_dir in test_dirs:
        Path(test_dir).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {test_dir}")


def verify_installation():
    """Verify that the installation was successful."""
    logger.info("Verifying installation...")
    
    try:
        # Test basic import
        result = run_command([
            sys.executable, "-c", 
            "import jzrobot; print(f'JZRobot version: {jzrobot.__version__}')"
        ])
        
        # Test robot import
        run_command([
            sys.executable, "-c",
            "from jzrobot.robots.agibot_g01 import Genie01, Genie01Config; print('Genie01 import successful')"
        ])
        
        logger.info("Installation verification successful!")
        return True
        
    except Exception as e:
        logger.error(f"Installation verification failed: {e}")
        return False


def main():
    """Main installation function."""
    logger.info("Starting JZRobot installation...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    try:
        # Install the package
        install_package()
        
        # Install optional dependencies
        install_optional_dependencies()
        
        # Create test environment
        create_test_environment()
        
        # Verify installation
        if verify_installation():
            logger.info("JZRobot installation completed successfully!")
            logger.info("\nNext steps:")
            logger.info("1. Source the a2d_sdk environment: source ~/workspace/a2d_sdk/env.sh")
            logger.info("2. Run tests: python test/robot/test_agibot_units.py")
            logger.info("3. Run integration tests: python test/robot/test_agibot.py")
        else:
            logger.error("Installation verification failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Installation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
