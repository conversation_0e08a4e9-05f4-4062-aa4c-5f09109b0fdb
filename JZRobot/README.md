# JZRobot Control Framework

A comprehensive, modular robot control framework inspired by LeRobot architecture, designed for multiple robot platforms with advanced camera integration and real-time control capabilities.

## 🚀 Features

- **🏗️ Modular Architecture**: Clean separation between robot control, observation, and action systems
- **🤖 Multiple Robot Support**: Extensible framework supporting different robot types
- **📷 Advanced Camera Integration**: Multi-camera support with depth and color cameras, bandwidth management
- **🛡️ Safety-First Design**: Built-in safety checks, movement limits, and comprehensive error handling
- **⚡ Performance Optimized**: Ultra-low latency observation and action loops (< 30ms)
- **🧪 Comprehensive Testing**: Unit tests, integration tests, and performance benchmarks
- **🔧 Developer Friendly**: Easy installation, clear APIs, and extensive documentation

## 📦 Quick Start

### Installation

1. **Install the package**:
```bash
# Basic installation
pip install -e .

# For development with all dependencies
pip install -e ".[dev,cameras]"

# Install everything
pip install -e ".[all]"

# Or using make
make install           # Basic installation
make install-dev      # Development setup
make install-all      # Everything
```

2. **Set up robot-specific environment** (if required):
```bash
# For specific robot platforms, follow their setup guides
# See individual robot documentation in src/jzrobot/robots/
```

3. **Verify installation**:
```bash
# Basic verification
python -c "import jzrobot; print('JZ<PERSON>obot installed successfully!')"

# Or using make
make verify
```

### Basic Usage

```python
from jzrobot.robots.robot import Robot
from jzrobot.robots.config import RobotConfig

# Example with a generic robot implementation
# (Specific robot implementations available in src/jzrobot/robots/)

# Create robot configuration
config = RobotConfig(
    id="my_robot",
    # Robot-specific configuration parameters
)

# Initialize robot (replace with specific robot class)
robot = Robot(config)

# Connect to robot
robot.connect()

# Get observation
observation = robot.get_observation()
print(f"Observation keys: {list(observation.keys())}")

# Send action (actions use 'action.*' prefix, observations use 'state.*')
action = {
    "action.joint.position": [...],    # Joint movements
    "action.effector.position": [...], # End-effector positions
    # ... other action components
}
robot.send_action(action)

# Disconnect
robot.disconnect()
```

For specific robot implementations, see the documentation in `src/jzrobot/robots/`.

## 🧪 Testing

### Unit Tests (No Hardware Required)
```bash
make test-units
# or
python test/robot/test_*_units.py
```

### Integration Tests (Hardware Required)
```bash
# Source robot-specific environment if needed
# (See individual robot documentation)

make test-integration
# or
python test/robot/test_*.py
```

### Performance Benchmarks
```bash
# Source robot-specific environment if needed
# (See individual robot documentation)

make benchmark
# or
python test/robot/benchmark_*.py
```

### Robot-Specific Tests
```bash
# Run tests for specific robot implementations
python test/robot/test_*_config.py
```

## 📁 Project Structure

```
jzrobot/
├── src/jzrobot/                    # Main package
│   ├── robots/                     # Robot implementations
│   │   ├── robot.py               # Base robot class
│   │   ├── config.py              # Base configuration classes
│   │   └── [robot_name]/          # Specific robot implementations
│   │       ├── robot_impl.py      # Robot implementation
│   │       ├── config_impl.py     # Robot configuration
│   │       └── README.md          # Robot-specific documentation
│   ├── cameras/                   # Camera utilities
│   │   ├── configs.py             # Base camera configs
│   │   ├── utils.py               # Camera utilities
│   │   └── [camera_type]/         # Specific camera implementations
│   └── errors.py                  # Custom exceptions
├── test/robot/                    # Test scripts
│   ├── test_*.py                 # Robot-specific tests
│   └── benchmark_*.py            # Performance benchmarks
├── setup.py                      # Package setup
├── requirements.txt              # Dependencies
├── install.py                   # Installation script
├── Makefile                     # Development commands
└── README.md                    # This file
```

## 🤖 Robot Support

JZRobot provides a unified interface for multiple robot platforms:

### Supported Robots

- **ALOHA Platform**: Dual-arm manipulation systems
- **PushT Environment**: Simulation and real-world pushing tasks
- **Custom Robots**: Extensible framework for new robot types

### Common Features

- **🦾 Multi-DOF Control**: Support for various joint configurations
- **🤏 End-Effector Control**: Gripper and tool control
- **📹 Camera Integration**: Multi-camera support with various types
- **💪 Force Sensing**: Force/torque feedback when available
- **🎯 Real-time Control**: Low-latency observation and action loops

### Observation Features

Robots provide comprehensive state information through a unified interface:

```python
# Example observation structure (varies by robot)
observation_features = {
    # Motor states (current robot state)
    "state.joint.position": (...,),     # Joint positions
    "state.effector.position": (...,),  # End-effector positions
    "state.effector.force": (...,),     # Force/torque data (if available)

    # Camera data (when available)
    "observation.images.camera_name": (H, W, C),  # Camera images
    # ... additional sensors
}
```

### Action Features

Actions control robot movement through a unified interface:

```python
# Example action structure (varies by robot)
action_features = {
    # Motor commands (target positions)
    "action.joint.position": (...,),     # Target joint positions
    "action.effector.position": (...,),  # Target end-effector positions
    "action.effector.force": (...,),     # Target force/torque (if supported)
    # ... additional actuators
}
```

**Key Difference**:
- **Observations** use `state.*` prefix (current robot state)
- **Actions** use `action.*` prefix (desired robot commands)

## 📷 Camera System

### Camera Configuration

The camera system supports multiple camera types with bandwidth management:

```python
# Example camera configuration (varies by robot/camera type)
from jzrobot.cameras.configs import CameraConfig

# Basic camera configuration
camera_config = CameraConfig(
    type="camera_type",
    width=640,
    height=480,
    fps=30
)

# Robot-specific camera configurations available in respective modules
# See src/jzrobot/cameras/ for specific implementations
```

### Bandwidth Management

Camera implementations may include bandwidth estimation:

```python
# Example bandwidth checking (if supported by camera implementation)
if hasattr(camera_config, 'estimate_bandwidth_mbps'):
    bandwidth = camera_config.estimate_bandwidth_mbps()
    print(f"Estimated bandwidth: {bandwidth:.1f} Mbps")

if hasattr(camera_config, 'requires_separate_usb_hubs'):
    if camera_config.requires_separate_usb_hubs():
        print("⚠️  Consider using separate USB hubs or PCIe cards")
```

### Camera Features

- **🔄 Real-time Streaming**: 30 fps per camera with low latency
- **📊 Bandwidth Estimation**: Automatic calculation of USB bandwidth requirements
- **🔌 USB Hub Management**: Recommendations for multi-camera setups
- **🛡️ Error Handling**: Graceful handling of camera connection issues
- **🎛️ Configurable Settings**: Resolution, FPS, and camera group customization

## 🛡️ Safety Features

JZRobot prioritizes safety in all operations:

### Movement Safety
- **📏 Movement Limits**: Configurable `max_relative_target` for safe incremental movements
- **🚫 Joint Limits**: Hardware and software joint limit enforcement
- **⚡ Emergency Stop**: Immediate torque disable on disconnect or error
- **🔒 Connection Validation**: All operations require active robot connection

### Error Handling
- **🚨 Exception Management**: Comprehensive exception handling for all operations
- **🔄 Automatic Recovery**: Graceful handling of temporary connection issues
- **📝 Detailed Logging**: Extensive logging for debugging and monitoring
- **✅ Input Validation**: All actions validated before execution

### Operational Safety
```python
# Safe movement example (configuration varies by robot)
config = RobotConfig(
    id="safe_robot",
    max_relative_target=0.1,  # Limit incremental movements
    # ... other safety parameters
)

# Safety limits are enforced by the framework
# Large movements may be rejected or limited automatically
```

## ⚡ Performance Characteristics

Optimized for real-time robotics applications:

### Latency Targets
- **🎯 Observation Latency**: < 30ms (typical: 10-20ms)
- **🚀 Action Latency**: < 10ms (typical: 2-5ms)
- **🔄 Control Loop Rate**: > 20 Hz (target: 30+ Hz)
- **📹 Camera Frame Rate**: 30 fps per camera

### Throughput
- **📊 Joint Data**: 14 joints @ 1kHz internal sampling
- **💪 Force Data**: 12-axis force/torque @ 1kHz
- **📷 Camera Data**: Up to 4 cameras @ 30fps (bandwidth permitting)
- **🎛️ Control Commands**: Real-time action processing

### Resource Usage
```python
# Performance monitoring example
import time

robot = Robot(config)  # Use specific robot implementation
robot.connect()

# Measure observation latency
start_time = time.time()
observation = robot.get_observation()
obs_latency = (time.time() - start_time) * 1000
print(f"Observation latency: {obs_latency:.1f}ms")

# Measure action latency
action = {"action.joint.position": [...]}  # Robot-specific action
start_time = time.time()
robot.send_action(action)
action_latency = (time.time() - start_time) * 1000
print(f"Action latency: {action_latency:.1f}ms")
```

## 🔧 Development

### Code Quality Tools
```bash
make format  # Format code with black and isort
make lint    # Run linting checks with flake8 and mypy
make check   # Run all checks (lint + test)
make clean   # Clean build artifacts
```

### Development Workflow
```bash
# Set up development environment
make dev-setup

# Run tests during development
make test-units        # Fast unit tests
make test-integration  # Hardware integration tests
make benchmark        # Performance benchmarks

# Check code quality
make check
```

### Adding New Robots

JZRobot is designed for extensibility:

1. **Create robot implementation** in `src/jzrobot/robots/your_robot/`
2. **Inherit from Robot base class**:
   ```python
   from jzrobot.robots.robot import Robot

   class YourRobot(Robot):
       name = "your_robot"

       def connect(self):
           # Implement connection logic
           pass

       def get_observation(self):
           # Implement observation collection
           pass

       def send_action(self, action):
           # Implement action execution
           pass
   ```
3. **Add configuration class** inheriting from `RobotConfig`
4. **Create comprehensive tests** in `test/robot/`
5. **Update documentation** and examples

### Camera Integration

Add new camera types:

1. **Create camera driver** in `src/jzrobot/cameras/your_camera/`
2. **Inherit from Camera base class**
3. **Implement configuration class**
4. **Add to camera registry**

## 🐛 Troubleshooting

### Common Issues

#### Import Errors
```bash
# Reinstall the package
python install.py

# Or install in development mode
pip install -e .

# Check Python path
python -c "import sys; print(sys.path)"
```

#### Hardware Connection Issues
```bash
# Check robot-specific environment variables
# (See individual robot documentation)

# Test basic import
python -c "import jzrobot; print('JZRobot import OK')"

# Test specific robot import
python -c "
from jzrobot.robots.robot import Robot
from jzrobot.robots.config import RobotConfig
print('Base classes import OK')
"
```

#### Camera Issues
```bash
# Check camera imports
python -c "
from jzrobot.cameras.configs import CameraConfig
print('Camera base classes import OK')
"

# Test robot-specific camera configuration
# (See individual robot documentation)
```

#### Test Failures
- **🔌 Hardware**: Ensure robot is powered and connected
- **🚫 Conflicts**: Check no other processes are using the robot
- **🌍 Environment**: Verify robot-specific environment is set up
- **🧪 Isolation**: Run unit tests first to isolate hardware issues

### Performance Issues

#### Slow Observations
- Check camera bandwidth usage
- Reduce camera resolution or FPS
- Use separate USB controllers for multiple cameras
- Monitor system CPU and memory usage

#### High Action Latency
- Verify robot connection stability
- Check for network issues (if using remote connection)
- Monitor system load during operation
- Use performance benchmarks to identify bottlenecks

### Debug Mode

Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Now all JZRobot operations will show detailed logs
robot = Robot(config)  # Use specific robot implementation
robot.connect()  # Will show detailed connection process
```

## 🤝 Contributing

We welcome contributions to JZRobot! Here's how to get started:

### Development Setup
```bash
# Clone the repository
git clone https://github.com/your-org/jzrobot.git
cd jzrobot

# Set up development environment
make dev-setup

# Run tests to verify setup
make test-units
```

### Contribution Process
1. **🍴 Fork** the repository
2. **🌿 Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **✨ Make** your changes with comprehensive tests
4. **✅ Verify** all tests pass (`make check`)
5. **📝 Commit** your changes (`git commit -m 'Add amazing feature'`)
6. **🚀 Push** to the branch (`git push origin feature/amazing-feature`)
7. **📬 Submit** a pull request

### Code Standards
- **🐍 Python Style**: Follow PEP 8, use `black` for formatting
- **📝 Documentation**: Add docstrings for all public methods
- **🧪 Testing**: Include unit tests for new functionality
- **🔍 Type Hints**: Use type hints for better code clarity
- **📊 Performance**: Consider performance implications of changes

### Areas for Contribution
- **🤖 New Robot Support**: Add support for additional robot platforms
- **📷 Camera Drivers**: Implement new camera types and protocols
- **🔧 Performance**: Optimize latency and throughput
- **📚 Documentation**: Improve examples and tutorials
- **🧪 Testing**: Expand test coverage and scenarios

## 📄 License

Copyright 2024 The HuggingFace Inc. team. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at:

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

## 🆘 Support

### Getting Help

For issues and questions:

1. **📖 Documentation**: Check this README and inline documentation
2. **🐛 Issues**: Search existing [GitHub Issues](https://github.com/your-org/jzrobot/issues)
3. **💬 Discussions**: Join [GitHub Discussions](https://github.com/your-org/jzrobot/discussions)
4. **📧 Contact**: Reach out to the development team

### Reporting Issues

When reporting issues, please include:
- **🖥️ System Information**: OS, Python version, hardware setup
- **📋 Steps to Reproduce**: Detailed steps to reproduce the issue
- **📊 Expected vs Actual**: What you expected vs what happened
- **📝 Logs**: Relevant log output (use debug mode if needed)
- **🧪 Test Results**: Output from unit tests and integration tests

### Community

- **⭐ Star** the repository if you find it useful
- **👀 Watch** for updates and new releases
- **🍴 Fork** to contribute your improvements
- **📢 Share** with others who might benefit

---

## 🎯 Quick Reference

### Essential Commands
```bash
pip install -e ".[all]"             # Install everything
source ~/path/to/a2d_sdk/env.sh   # Source A2D environment

# Testing
make test-units                      # Unit tests
make test-integration               # Integration tests (needs hardware)
make benchmark                      # Performance tests

# Development
make install-dev                    # Development setup
make check                         # Code quality checks
make clean                         # Clean artifacts
```

### Key Classes
```python
# Base robot control
from jzrobot.robots.robot import Robot
from jzrobot.robots.config import RobotConfig

# Base camera configuration
from jzrobot.cameras.configs import CameraConfig

# Error handling
from jzrobot.errors import DeviceNotConnectedError, DeviceAlreadyConnectedError

# Specific robot implementations available in respective modules
# from jzrobot.robots.[robot_name] import RobotClass, RobotConfig
```

### Important Notes
- **🔑 Actions** use `action.*` prefix, **📊 observations** use `state.*` prefix
- **📷 Multiple cameras** may require separate USB hubs for bandwidth
- **🛡️ Safety limits** are enforced through robot-specific configuration
- **🔌 Hardware connection** may require robot-specific environment setup
- **📚 Robot-specific documentation** available in `src/jzrobot/robots/[robot_name]/README.md`

---

**Built with ❤️ for the robotics community**
