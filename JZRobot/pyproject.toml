[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "jzrobot"
version = "0.1.0"
description = "A comprehensive, modular robot control framework for Genie01 (Agibot) robot system"
readme = "README.md"
license = {text = "Apache-2.0"}
authors = [
    {name = "JZRobot Team", email = "<EMAIL>"},
]
maintainers = [
    {name = "JZRobot Team", email = "<EMAIL>"},
]
keywords = ["robotics", "control", "framework", "automation", "ai", "genie01", "agibot"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.20.0",
    "scipy>=1.7.0",
    "draccus>=0.1.0",
    "dataclasses-json>=0.5.0",
    "pyyaml>=5.4.0",
    "loguru>=0.6.0",
    "tqdm>=4.60.0",
    "click>=8.0.0",
]

[project.optional-dependencies]
# Agibot G01 (Genie01) robot dependencies
aloha = [
    "protobuf==3.12.4",
    "ruckig==0.14.0",
    "opencv-python==*********",
    "pyzmq==26.2.0",
    "matplotlib>=3.3.0",
]

# PushT environment dependencies
pusht = [
    "opencv-python>=4.5.0",
    "matplotlib>=3.3.0",
    "plotly>=5.0.0",
]

# Camera support
cameras = [
    "opencv-python>=4.5.0",
    "pyrealsense2>=2.50.0",
]

# Visualization tools
visualization = [
    "matplotlib>=3.3.0",
    "plotly>=5.0.0",
    "seaborn>=0.11.0",
]

# Development dependencies
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "isort>=5.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "pre-commit>=2.15.0",
]

# All optional dependencies
all = [
    "protobuf==3.12.4",
    "ruckig==0.14.0", 
    "opencv-python==*********",
    "pyzmq==26.2.0",
    "matplotlib>=3.3.0",
    "plotly>=5.0.0",
    "seaborn>=0.11.0",
    "pyrealsense2>=2.50.0",
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "isort>=5.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "pre-commit>=2.15.0",
]

[project.urls]
Homepage = "https://github.com/jzrobot/jzrobot"
Documentation = "https://jzrobot.readthedocs.io/"
Repository = "https://github.com/jzrobot/jzrobot"
"Bug Reports" = "https://github.com/jzrobot/jzrobot/issues"
Changelog = "https://github.com/jzrobot/jzrobot/blob/main/CHANGELOG.md"

[project.scripts]
jzrobot-test = "jzrobot.cli.test:main"
jzrobot-benchmark = "jzrobot.cli.benchmark:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
jzrobot = [
    "config/*.yaml",
    "config/*.json",
    "robots/*/config/*.yaml",
    "robots/*/config/*.json",
]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "draccus.*",
    "ruckig.*",
    "pyrealsense2.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["test"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=src/jzrobot",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "hardware: marks tests that require hardware",
]

[tool.coverage.run]
source = ["src/jzrobot"]
omit = [
    "*/test*",
    "*/tests/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
