## What this does

Explain what this PR does. Feel free to tag your PR with the appropriate label(s).

Examples:
| Title | Label |
|----------------------|-----------------|
| Fixes #[issue] | (🐛 Bug) |
| Adds new dataset | (🗃️ Dataset) |
| Optimizes something | (⚡️ Performance) |

## How it was tested

Explain/show how you tested your changes.

Examples:

- Added `test_something` in `tests/test_stuff.py`.
- Added `new_feature` and checked that training converges with policy X on dataset/environment Y.
- Optimized `some_function`, it now runs X times faster than previously.

## How to checkout & try? (for the reviewer)

Provide a simple way for the reviewer to try out your changes.

Examples:

```bash
pytest -sx tests/test_stuff.py::test_something
```

```bash
lerobot-train --some.option=true
```

## SECTION TO REMOVE BEFORE SUBMITTING YOUR PR

**Note**: Anyone in the community is free to review the PR once the tests have passed. Feel free to tag
members/contributors who may be interested in your PR. Try to avoid tagging more than 3 people.

**Note**: Before submitting this PR, please read the [contributor guideline](https://github.com/huggingface/lerobot/blob/main/CONTRIBUTING.md#submitting-a-pull-request-pr).
