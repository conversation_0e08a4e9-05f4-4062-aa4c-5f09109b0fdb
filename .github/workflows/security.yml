# Copyright 2025 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This workflow handles secret scanning using TruffleHog to detect sensitive information in the codebase.
name: Security
permissions:
  contents: read

on:
  # Allows running this workflow manually from the Actions tab
  workflow_dispatch:

  # Triggers the workflow on push events to main
  push:
    branches:
      - main

  # Triggers the workflow on pull request events targeting main
  pull_request:
    branches:
      - main

# Ensures that only the latest commit for a PR or branch is built, canceling older runs.
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  # This job runs TruffleHog to scan the full history of the repository for secrets.
  trufflehog:
    name: Secret Leaks Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4 # zizmor: ignore[unpinned-uses]
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Secret Scanning
        uses: trufflesecurity/trufflehog@v3.90.0  # zizmor: ignore[unpinned-uses]
        with:
          extra_args: --only-verified
