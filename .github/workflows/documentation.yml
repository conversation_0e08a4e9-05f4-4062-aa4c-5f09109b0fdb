# Copyright 2025 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This workflow handles building documentation for both main branches and PRs.
name: Documentation

on:
  # Allows running this workflow manually from the Actions tab
  workflow_dispatch:

  # Triggers the workflow on push events to main for the docs folder
  push:
    branches:
      - main
    paths:
      - "docs/**"

  # Triggers the workflow on pull request events targeting main for the docs folder
  pull_request:
    branches:
      - main
    paths:
      - "docs/**"

# Ensures that only the latest commit for a PR or branch is built, canceling older runs.
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  # This job builds and deploys the official documentation.
  build_main_docs:
    name: Build Main Docs
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    permissions:
      contents: read
    uses: huggingface/doc-builder/.github/workflows/build_main_documentation.yml@main
    with:
      commit_sha: ${{ github.sha }}
      package: lerobot
      additional_args: --not_python_module
    secrets:
      token: ${{ secrets.HUGGINGFACE_PUSH }}
      hf_token: ${{ secrets.HF_DOC_BUILD_PUSH }}

  # This job builds a preview of the documentation for a pull request.
  # The result of this job triggers the 'Upload PR Documentation' workflow.
  build_pr_docs:
    name: Build PR Docs
    if: github.event_name == 'pull_request'
    permissions:
      contents: read
      pull-requests: write
    uses: huggingface/doc-builder/.github/workflows/build_pr_documentation.yml@main
    with:
      commit_sha: ${{ github.event.pull_request.head.sha }}
      pr_number: ${{ github.event.number }}
      package: lerobot
      additional_args: --not_python_module
