#!/usr/bin/env python3
"""
Test script for ROS2 bag to LeRobotDataset conversion.
"""

import os
import sys
from pathlib import Path

def test_dataset_loading(dataset_path: str):
    """Test loading the converted LeRobotDataset."""
    try:
        from lerobot.datasets.lerobot_dataset import LeRobotDataset
        
        print(f"Loading dataset from: {dataset_path}")
        dataset = LeRobotDataset(dataset_path)
        
        print(f"✓ Dataset loaded successfully")
        print(f"  - Repository ID: {dataset.repo_id}")
        print(f"  - Episodes: {dataset.num_episodes}")
        print(f"  - Frames: {dataset.num_frames}")
        print(f"  - FPS: {dataset.fps}")
        print(f"  - Features: {list(dataset.features.keys())}")
        
        # Test loading a sample
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"✓ Sample loaded successfully")
            print(f"  - Sample keys: {list(sample.keys())}")
            
            # Check observation structure
            if 'observation.state' in sample:
                print(f"  - Joint state shape: {sample['observation.state'].shape}")
            if 'observation.gripper_state' in sample:
                print(f"  - Gripper state shape: {sample['observation.gripper_state'].shape}")
            if 'action' in sample:
                print(f"  - Action shape: {sample['action'].shape}")
                
            # Check images
            image_keys = [k for k in sample.keys() if 'images' in k]
            for img_key in image_keys:
                print(f"  - {img_key} shape: {sample[img_key].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error loading dataset: {e}")
        return False

def test_bag_structure(bag_dir: str):
    """Test if bag directory has the expected structure."""
    bag_path = Path(bag_dir)
    
    if not bag_path.exists():
        print(f"✗ Bag directory does not exist: {bag_dir}")
        return False
        
    print(f"Checking bag structure: {bag_dir}")
    
    # Look for bag files
    db3_files = list(bag_path.rglob("*.db3"))
    mcap_files = list(bag_path.rglob("*.mcap"))
    
    if not db3_files and not mcap_files:
        print(f"✗ No .db3 or .mcap files found in {bag_dir}")
        return False
        
    print(f"✓ Found {len(db3_files)} .db3 files and {len(mcap_files)} .mcap files")
    
    # Check for metadata files
    metadata_files = list(bag_path.rglob("metadata.yaml"))
    print(f"✓ Found {len(metadata_files)} metadata.yaml files")
    
    return True

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python test_conversion.py <dataset_path>  # Test converted dataset")
        print("  python test_conversion.py --check-bag <bag_dir>  # Check bag structure")
        sys.exit(1)
    
    if sys.argv[1] == "--check-bag":
        if len(sys.argv) < 3:
            print("Please provide bag directory path")
            sys.exit(1)
        test_bag_structure(sys.argv[2])
    else:
        dataset_path = sys.argv[1]
        success = test_dataset_loading(dataset_path)
        if not success:
            sys.exit(1)

if __name__ == "__main__":
    main()
