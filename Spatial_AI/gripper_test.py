#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray

class GripperPublisher(Node):
    def __init__(self):
        super().__init__('gripper_publisher')
        self.publisher_ = self.create_publisher(Float64MultiArray, '/gripper_infer', 10)
        
        # 定时器：15Hz -> 1/15 ≈ 0.0667秒
        timer_period = 1.0 / 15.0
        self.timer = self.create_timer(timer_period, self.timer_callback)

        self.step = 1  # 每次变化 1
        self.value1 = 0
        self.value2 = 255

    def timer_callback(self):
        # 发布消息
        msg = Float64MultiArray()
        msg.data = [float(self.value1), float(self.value2)]
        self.publisher_.publish(msg)
        self.get_logger().info(f'Publishing: {msg.data}')

        # 更新数值
        self.value1 += self.step
        self.value2 -= self.step

        # 到达边界后循环
        if self.value1 > 255:
            self.value1 = 0
            self.value2 = 255

def main(args=None):
    rclpy.init(args=args)
    node = GripperPublisher()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
