#!/usr/bin/env python3
"""
YMrobot夹爪控制节点 - ROS2夹爪控制器

功能：
1. 订阅 /gripper_infer 话题，接收夹爪位置控制命令
2. 发布 /gripper_states 话题，实时发布夹爪状态
3. 控制左右RS485夹爪

话题接口：
    订阅：
        /gripper_infer (std_msgs/Float64MultiArray)
            - data[0]: 左夹爪目标位置
            - data[1]: 右夹爪目标位置

    发布：
        /gripper_states (std_msgs/Float64MultiArray)
            - data[0]: 左夹爪当前位置
            - data[1]: 右夹爪当前位置

使用方法：
    # 直接运行脚本
    python3 ymrobot_zy_gripper.py

    # 或使用ROS2运行
    ros2 run <package_name> ymrobot_zy_gripper.py

    # 发送夹爪控制命令
    ros2 topic pub /gripper_infer std_msgs/msg/Float64MultiArray "data: [50.0, 75.0]"
"""
import rclpy
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
import signal
import sys

from gripper.src.gripper_controller import create_rs485_gripper


class YMrobotZYgripper(Node):
    def __init__(self):
        super().__init__('ymrobot_gripper_controller')
        # gripper
        try:
            self.rs485_gripper_left = create_rs485_gripper(port='/dev/ttyUSB1', node_id=1)
            self.rs485_gripper_right = create_rs485_gripper(port='/dev/ttyUSB0', node_id=1)
        except Exception as e:
            print(f"RS485夹爪添加失败: {e}")

        # --- New: Gripper state publisher ---
        self.gripper_state_pub = self.create_publisher(Float64MultiArray, '/gripper_states', 10)

        # --- New: Gripper inference subscriber ---
        self.gripper_infer_sub = self.create_subscription(
            Float64MultiArray,
            '/gripper_infer',
            self.gripper_infer_callback,
            10
        )

        # Node initialization complete
        self.get_logger().info("YMrobot gripper controller initialized")

        # --- New: Timer for gripper state publishing ---
        self.gripper_state_timer = self.create_timer(0.033, self.publish_gripper_states)  # 30Hz
        self.get_logger().info("Gripper state publisher initialized at 10Hz")
        self.get_logger().info("Gripper inference subscriber initialized")



    def shutdown(self):
        """Clean up resources"""
        self.destroy_node()

    def close_gripper(self, gripper_name):
        """Close a gripper"""
        if gripper_name == "left":
            self.rs485_gripper_left.close_gripper()
        elif gripper_name == "right":
            self.rs485_gripper_right.close_gripper()
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")
    
    def open_gripper(self, gripper_name):
        """Open a gripper"""
        if gripper_name == "left":
            self.rs485_gripper_left.open_gripper()
        elif gripper_name == "right":
            self.rs485_gripper_right.open_gripper()
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")

    def move_gripper_to_position(self, gripper_name, position):
        """Move a gripper to a position"""
        if gripper_name == "left":
            self.rs485_gripper_left.move_to_position(position)
        elif gripper_name == "right":
            self.rs485_gripper_right.move_to_position(position)
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")

    def get_gripper_state(self, gripper_name):
        """Get a gripper state"""
        if gripper_name == "left":
            return self.rs485_gripper_left.get_status()
        elif gripper_name == "right":
            return self.rs485_gripper_right.get_status()
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")

    def publish_gripper_states(self):
        """Publish gripper states as [left_gripper_position, right_gripper_position]"""
        try:
            # Get left gripper state
            left_state = self.get_gripper_state("left")
            left_position = 0.0  # Default value
            if left_state and 'position' in left_state:
                left_position = float(left_state['position'])

            # Get right gripper state
            right_state = self.get_gripper_state("right")
            right_position = 0.0  # Default value
            if right_state and 'position' in right_state:
                right_position = float(right_state['position'])

            # Create and publish the message
            msg = Float64MultiArray()
            msg.data = [left_position, right_position]
            self.gripper_state_pub.publish(msg)

        except Exception as e:
            self.get_logger().warn(f"Failed to publish gripper states: {e}")

    def gripper_infer_callback(self, msg):
        """
        Callback function for /gripper_infer topic
        Expects Float64MultiArray with at least 2 values:
        - msg.data[0]: position for left gripper
        - msg.data[1]: position for right gripper
        """
        try:
            if len(msg.data) < 2:
                self.get_logger().warn(f"Gripper infer message has insufficient data: {len(msg.data)} values, expected at least 2")
                return

            left_position = msg.data[0]
            right_position = msg.data[1]

            self.get_logger().info(f"Received gripper infer command: left={left_position}, right={right_position}")

            # Execute gripper movements
            self.move_gripper_to_position("left", int(left_position))
            self.move_gripper_to_position("right", int(right_position))

        except Exception as e:
            self.get_logger().error(f"Error in gripper infer callback: {e}")

    def test_grippers(self):
        """Test gripper functionality"""
        self.get_logger().info("Testing gripper functionality...")

        try:
            # Test left gripper
            self.get_logger().info("Testing left gripper...")
            left_state = self.get_gripper_state("left")
            if left_state:
                self.get_logger().info(f"Left gripper state: {left_state}")
            else:
                self.get_logger().warn("Failed to get left gripper state")

            # Test right gripper
            self.get_logger().info("Testing right gripper...")
            right_state = self.get_gripper_state("right")
            if right_state:
                self.get_logger().info(f"Right gripper state: {right_state}")
            else:
                self.get_logger().warn("Failed to get right gripper state")

            self.get_logger().info("Gripper test completed")

        except Exception as e:
            self.get_logger().error(f"Error during gripper test: {e}")


def signal_handler(signum, _):
    """Handle shutdown signals gracefully"""
    print(f"\nReceived signal {signum}, cleaning up...")
    sys.exit(0)


def main(args=None):
    """Main function to run the YMrobot node"""
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Initialize rclpy
    rclpy.init(args=args)

    robot = None
    try:
        # Create the robot node
        robot = YMrobotZYgripper()

        # Test gripper connectivity
        robot.test_grippers()

        robot.get_logger().info("Ready to receive gripper commands...")
        robot.get_logger().info("Press Ctrl+C to stop")

        # Keep the node running
        try:
            rclpy.spin(robot)
        except KeyboardInterrupt:
            robot.get_logger().info("Received keyboard interrupt, shutting down...")

    except Exception as e:
        print(f"Failed to start YMrobot: {e}")
        return 1

    finally:
        # Clean shutdown
        if robot is not None:
            try:
                robot.get_logger().info("Shutting down YMrobot gripper controller...")
                robot.shutdown()
            except Exception as e:
                print(f"Error during shutdown: {e}")

        try:
            rclpy.shutdown()
        except Exception as e:
            print(f"Error shutting down rclpy: {e}")

    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
