#!/usr/bin/env python3
"""
YMrobot控制类 - 包含关节限位和变化检测功能

使用示例：
    # 创建机器人实例
    robot = YMrobot()

    # 设置安全模式
    robot.set_conservative_mode()  # 15度阈值
    robot.set_normal_mode()        # 30度阈值（默认）
    robot.set_aggressive_mode()    # 45度阈值

    # 自定义阈值
    robot.set_max_joint_change_threshold(25.0)  # 设置为25度

    # 重置跟踪（机器人重启后）
    robot.reset_joint_velocity_tracking()

    # 控制机器人（自动应用限位和变化检测）
    success = robot.control_left_arm_joint_position([0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7])
    if not success:
        print("命令被拒绝：可能是关节限位或变化过大")
"""
import rclpy
from rclpy.node import Node
from rclpy.action import ActionClient
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy
from moveit_msgs.action import MoveGroup
from moveit_msgs.msg import Constraints, JointConstraint, PositionConstraint, OrientationConstraint, BoundingVolume
from geometry_msgs.msg import Pose, PoseStamped, Quaternion
from std_msgs.msg import Float64MultiArray, String, Int32MultiArray
from trajectory_msgs.msg import JointTrajectory, JointTrajectoryPoint
from builtin_interfaces.msg import Duration
from sensor_msgs.msg import JointState, Image
from shape_msgs.msg import SolidPrimitive
from cv_bridge import CvBridge
import numpy as np
import time
import threading
from rclpy.qos import qos_profile_sensor_data
import cv2
import math

from gripper.src.gripper_controller import (
    create_can_gripper, 
    create_rs485_gripper, 
    create_gripper,
    MultiGripperController
)
from gripper.src.gripper_base import GripperType


class YMrobot(Node):
    def __init__(self):
        super().__init__('ymrobot')

        # Define planning groups and their joints
        self.planning_groups = {
            'arms': [
                'Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 'Left_Arm_Joint4',
                'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7',
                'Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3', 'Right_Arm_Joint4',
                'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7'
            ],
            'body': ['Body_Joint1', 'Body_Joint2', 'Body_Joint3', 'Body_Joint4'],
            'left_arm': ['Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 'Left_Arm_Joint4',
                         'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7'],
            'right_arm': ['Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3', 'Right_Arm_Joint4',
                          'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7'],
            'neck': ['Neck_Joint1', 'Neck_Joint2']
        }

        # 存储上一帧的关节值，用于检测大幅度变化
        self.previous_joint_values = {
            'left_arm': None,
            'right_arm': None
        }

        # 关节变化检测的阈值（度）
        self.max_joint_change_deg = 30.0
        self.get_logger().info(f"Joint change detection initialized with threshold: {self.max_joint_change_deg}°")

        # Initialize MoveIt action client
        self.action_client = ActionClient(self, MoveGroup, 'move_action')




        # gripper
        # 创建多夹爪控制器
        self.multi_controller = MultiGripperController()
        try:
            self.rs485_gripper_left = create_rs485_gripper(port='/dev/ttyUSB1', node_id=1)
            self.rs485_gripper_right = create_rs485_gripper(port='/dev/ttyUSB0', node_id=1)
            self.multi_controller.add_gripper("rs485_gripper_left", self.rs485_gripper_left.gripper)
            self.multi_controller.add_gripper("rs485_gripper_right", self.rs485_gripper_right.gripper)
        except Exception as e:
            print(f"RS485夹爪添加失败: {e}")








        # CV Bridge for image conversion
        self.cv_bridge = CvBridge()

        # QoS profile for camera and TF topics
        qos = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )

        # Subscribers
        self.joint_state = None
        self.joint_state_sub = self.create_subscription(
            JointState, '/joint_states', self.joint_state_callback, qos_profile_sensor_data
        )
        self.left_hand_state = None
        self.right_hand_state = None
        self.create_subscription(Int32MultiArray, '/left_gripper_position',  self.cb_left_gripper,  10)
        self.create_subscription(Int32MultiArray, '/right_gripper_position', self.cb_right_gripper, 10)
        self.head_rgb = None
        self.head_rgb_stamp = None
        self.head_rgb_sub = self.create_subscription(
            Image, '/camera/color/image_raw', self.head_rgb_callback, qos
        )
        self.left_wrist_rgb = None
        self.left_wrist_rgb_stamp = None
        self.left_wrist_rgb_sub = self.create_subscription(
            Image, '/camera1/camera1/color/image_raw', self.left_wrist_rgb_callback, qos
        )
        self.right_wrist_rgb = None
        self.right_wrist_rgb_stamp = None
        self.right_wrist_rgb_sub = self.create_subscription(
            Image, '/camera2/camera2/color/image_raw', self.right_wrist_rgb_callback, qos
        )
        self.head_depth = None
        self.head_depth_stamp = None
        self.head_depth_sub = self.create_subscription(
            Image, '/camera/depth/image_raw', self.head_depth_callback, qos
        )
        self.left_wrist_depth = None
        self.left_wrist_depth_stamp = None
        self.left_wrist_depth_sub = self.create_subscription(
            Image, '/camera1/camera1/depth/image_rect_raw', self.left_wrist_depth_callback, qos
        )
        self.right_wrist_depth = None
        self.right_wrist_depth_stamp = None
        self.right_wrist_depth_sub = self.create_subscription(
            Image, '/camera2/camera2/depth/image_rect_raw', self.right_wrist_depth_callback, qos
        )
        self.left_arm_pose = None
        self.left_arm_pose_stamp = None
        self.left_arm_pose_sub = self.create_subscription(
            PoseStamped, '/tf/Left_Arm_Link8/pose', self.left_arm_pose_callback, qos
        )
        self.right_arm_pose = None
        self.right_arm_pose_stamp = None
        self.right_arm_pose_sub = self.create_subscription(
            PoseStamped, '/tf/Right_Arm_Link8/pose', self.right_arm_pose_callback, qos
        )

        # --- New: Trajectory publishers for high-frequency control ---
        self.left_arm_traj_pub = self.create_publisher(
            JointTrajectory, '/left_arm_controller/joint_trajectory', 10)
        self.right_arm_traj_pub = self.create_publisher(
            JointTrajectory, '/right_arm_controller/joint_trajectory', 10)

        # --- New: Gripper state publisher ---
        self.gripper_state_pub = self.create_publisher(
            Float64MultiArray, '/gripper_states', 10)

        self.pubs = {}
        # 为每个组创建一个 Float64MultiArray 的 publisher
        for group in self.planning_groups:
            topic = f"/{group}_position_controller/commands"
            self.pubs[group] = self.create_publisher(
                Float64MultiArray, topic, 10
            )
            self.get_logger().info(f"Initialized publisher for '{group}' on topic: {topic}")

        # 反复 spin_once，直到 qpos 被填上
        # while rclpy.ok() and self.joint_state is None:
        #     rclpy.spin_once(self, timeout_sec=0.1)

        # while rclpy.ok() and self.head_rgb is None:
        #     rclpy.spin_once(self, timeout_sec=0.1)

        # while rclpy.ok() and self.left_wrist_rgb is None:
        #     rclpy.spin_once(self, timeout_sec=0.1)

        # while rclpy.ok() and self.right_wrist_rgb is None:
        #     rclpy.spin_once(self, timeout_sec=0.1)

        # Start a thread for spinning the node
        self.spin_thread = threading.Thread(target=self.spin_node, daemon=True)
        self.spin_thread.start()
        self.get_logger().info("YMrobot_observation initialized, spinning in separate thread")

        # --- New: Timer for gripper state publishing ---
        self.gripper_state_timer = self.create_timer(0.033, self.publish_gripper_states)  # 30Hz
        self.get_logger().info("Gripper state publisher initialized at 10Hz")

        # 示例：可以在这里设置不同的安全模式
        # self.set_conservative_mode()  # 15度阈值，更安全
        # self.set_normal_mode()        # 30度阈值，默认
        # self.set_aggressive_mode()    # 45度阈值，允许更大变化

    def control_left_arm_trajectory(self, joint_positions: list[float], time_from_start: float = 0.2) -> bool:
        """Send a single-point trajectory to the left_arm_controller with joint limits and velocity check."""
        if len(joint_positions) != len(self.planning_groups['left_arm']):
            self.get_logger().error(
                f"Left arm expects {len(self.planning_groups['left_arm'])} joints, got {len(joint_positions)}")
            return False

        # 检查关节变化是否过大
        if not self._check_joint_velocity(joint_positions, 'left_arm'):
            self.get_logger().error("Left arm joint change too large, trajectory command rejected for safety")
            return False

        # 应用关节限位
        limited_positions = self._apply_joint_limits(joint_positions, 'left_arm')

        traj = JointTrajectory()
        traj.joint_names = self.planning_groups['left_arm']

        point = JointTrajectoryPoint()
        point.positions = limited_positions
        point.time_from_start = Duration(sec=int(time_from_start), nanosec=int((time_from_start%1)*1e9))
        traj.points = [point]

        self.left_arm_traj_pub.publish(traj)
        return True

    def control_right_arm_trajectory(self, joint_positions: list[float], time_from_start: float = 0.2) -> bool:
        """Send a single-point trajectory to the right_arm_controller with joint limits and velocity check."""
        if len(joint_positions) != len(self.planning_groups['right_arm']):
            self.get_logger().error(
                f"Right arm expects {len(self.planning_groups['right_arm'])} joints, got {len(joint_positions)}")
            return False

        # 检查关节变化是否过大
        if not self._check_joint_velocity(joint_positions, 'right_arm'):
            self.get_logger().error("Right arm joint change too large, trajectory command rejected for safety")
            return False

        # 应用关节限位
        limited_positions = self._apply_joint_limits(joint_positions, 'right_arm')

        traj = JointTrajectory()
        traj.joint_names = self.planning_groups['right_arm']

        point = JointTrajectoryPoint()
        point.positions = limited_positions
        point.time_from_start = Duration(sec=int(time_from_start), nanosec=int((time_from_start%1)*1e9))
        traj.points = [point]

        self.right_arm_traj_pub.publish(traj)
        return True

    # Optionally: generic helper for either arm
    def control_arm_trajectory(self, arm: str, joint_positions: list[float], time_from_start: float = 0.2) -> bool:
        if arm == 'left_arm':
            return self.control_left_arm_trajectory(joint_positions, time_from_start)
        elif arm == 'right_arm':
            return self.control_right_arm_trajectory(joint_positions, time_from_start)
        else:
            self.get_logger().error(f"Unsupported arm '{arm}' for trajectory control")
            return False


    def _publish(self, group_name: str, joint_values: list[float]) -> bool:
        """
        内部发布函数。检查长度并发布数据。
        """
        if group_name not in self.pubs:
            self.get_logger().error(f"Unknown planning group '{group_name}'")
            return False

        expected_len = len(self.planning_groups[group_name])
        if len(joint_values) != expected_len:
            self.get_logger().error(
                f"Group '{group_name}' expects {expected_len} values, got {len(joint_values)}"
            )
            return False

        msg = Float64MultiArray()
        msg.data = joint_values
        self.pubs[group_name].publish(msg)
        return True
    
    def _apply_joint_limits(self, joint_values: list[float], arm: str) -> list[float]:
        """
        应用关节限位，确保关节值在安全范围内
        Args:
            joint_values: 关节值列表
            arm: 'left_arm' 或 'right_arm'
        Returns:
            限位后的关节值列表
        """
        if arm == 'left_arm':
            # 左臂关节限位 (基于URDF定义)
            limits = [
                (-2.791111111, 2.791111111),  # Left_Arm_Joint1
                (-0.21, 1.4),                 # Left_Arm_Joint2
                (-2.791111111, 2.791111111),  # Left_Arm_Joint3
                (-0.65, 0.87),                # Left_Arm_Joint4
                (-3.14159, 3.14159),          # Left_Arm_Joint5 (无URDF限位，使用默认)
                (-1.42, 0.03),                # Left_Arm_Joint6
                (-2.791111111, 2.791111111),  # Left_Arm_Joint7
            ]
        elif arm == 'right_arm':
            # 右臂关节限位 (基于URDF定义)
            limits = [
                (-2.791111111, 2.791111111),  # Right_Arm_Joint1
                (-1.4, 0.21),                 # Right_Arm_Joint2
                (-2.791111111, 2.791111111),  # Right_Arm_Joint3
                (-0.87, 0.6),                 # Right_Arm_Joint4
                (-3.14159, 3.14159),          # Right_Arm_Joint5 (无URDF限位，使用默认)
                (-0.03, 1.4),                 # Right_Arm_Joint6
                (-2.791111111, 2.791111111),  # Right_Arm_Joint7
            ]
        else:
            self.get_logger().error(f"Unknown arm type: {arm}")
            return joint_values

        if len(joint_values) != len(limits):
            self.get_logger().error(f"Joint values length mismatch for {arm}: expected {len(limits)}, got {len(joint_values)}")
            return joint_values

        # 应用限位
        limited_values = []
        for i, (value, (lower, upper)) in enumerate(zip(joint_values, limits)):
            if value < lower:
                self.get_logger().warn(f"{arm} Joint{i+1}: value {value:.3f} below limit {lower:.3f}, clamping")
                limited_values.append(lower)
            elif value > upper:
                self.get_logger().warn(f"{arm} Joint{i+1}: value {value:.3f} above limit {upper:.3f}, clamping")
                limited_values.append(upper)
            else:
                limited_values.append(value)

        return limited_values

    def _check_joint_velocity(self, joint_values: list[float], arm: str, max_change_deg: float = None) -> bool:
        """
        检查关节变化是否过大
        Args:
            joint_values: 当前关节值列表 (弧度)
            arm: 'left_arm' 或 'right_arm'
            max_change_deg: 最大允许变化角度 (度)
        Returns:
            True: 变化在安全范围内, False: 变化过大
        """
        if arm not in self.previous_joint_values:
            self.get_logger().error(f"Unknown arm type for velocity check: {arm}")
            return True

        # 如果没有上一帧数据，直接通过
        if self.previous_joint_values[arm] is None:
            self.previous_joint_values[arm] = joint_values.copy()
            return True

        if len(joint_values) != len(self.previous_joint_values[arm]):
            self.get_logger().error(f"Joint values length mismatch for {arm} velocity check")
            return True

        # 使用传入的阈值或默认阈值
        if max_change_deg is None:
            max_change_deg = self.max_joint_change_deg

        max_change_rad = max_change_deg * 3.14159 / 180.0  # 转换为弧度

        # 检查每个关节的变化
        for i, (current, previous) in enumerate(zip(joint_values, self.previous_joint_values[arm])):
            change = abs(current - previous)
            if change > max_change_rad:
                change_deg = change * 180.0 / 3.14159
                self.get_logger().warn(
                    f"WARNING: {arm} Joint{i+1} large change detected! "
                    f"Change: {change_deg:.1f}° (limit: {max_change_deg}°), "
                    f"Previous: {previous*180/3.14159:.1f}°, Current: {current*180/3.14159:.1f}°"
                )
                return False

        # 更新上一帧数据
        self.previous_joint_values[arm] = joint_values.copy()
        return True

    def set_max_joint_change_threshold(self, max_change_deg: float):
        """
        设置关节变化检测的阈值
        Args:
            max_change_deg: 最大允许变化角度 (度)
        """
        self.max_joint_change_deg = max_change_deg
        self.get_logger().info(f"Joint change threshold set to {max_change_deg}°")

    def reset_joint_velocity_tracking(self):
        """
        重置关节变化跟踪，清除上一帧数据
        用于机器人重新启动或位置重置后
        """
        self.previous_joint_values = {
            'left_arm': None,
            'right_arm': None
        }
        self.get_logger().info("Joint velocity tracking reset")

    def set_conservative_mode(self):
        """设置保守模式：较小的关节变化阈值（15度）"""
        self.set_max_joint_change_threshold(15.0)

    def set_normal_mode(self):
        """设置正常模式：默认关节变化阈值（30度）"""
        self.set_max_joint_change_threshold(30.0)

    def set_aggressive_mode(self):
        """设置激进模式：较大的关节变化阈值（45度）"""
        self.set_max_joint_change_threshold(45.0)

    def control_left_arm_joint_position_ros2controller(self, joint_values: list[float]) -> bool:
        """对外接口：发布左臂关节位置（带关节限位和变化检测）"""
        # 检查关节变化是否过大
        # if not self._check_joint_velocity(joint_values, 'left_arm'):
        #     self.get_logger().error("Left arm joint change too large, command rejected for safety")
        #     return False

        # limited_values = self._apply_joint_limits(joint_values, 'left_arm')
        return self._publish('left_arm', joint_values)

    def control_right_arm_joint_position_ros2controller(self, joint_values: list[float]) -> bool:
        """对外接口：发布右臂关节位置（带关节限位和变化检测）"""
        # 检查关节变化是否过大
        # if not self._check_joint_velocity(joint_values, 'right_arm'):
        #     self.get_logger().error("Right arm joint change too large, command rejected for safety")
        #     return False

        # limited_values = self._apply_joint_limits(joint_values, 'right_arm')
        return self._publish('right_arm', joint_values)
    
    def control_neck_joint_position_ros2controller(self, joint_values: list[float]) -> bool:
        return self._publish('neck', joint_values)
    
    def control_body_joint_position_ros2controller(self, joint_values: list[float]) -> bool:
        return self._publish('body', joint_values)

    def spin_node(self):
        """Run rclpy.spin in a separate thread"""
        try:
            rclpy.spin(self)
        except Exception as e:
            self.get_logger().error(f"Spin thread error: {e}")

    def joint_state_callback(self, msg):
        self.joint_state = msg

    # Gripper state callbacks
    def cb_left_gripper(self, msg: Int32MultiArray):
        # 将gripper数值除以1000进行缩放
        self.left_hand_state = [val / 1000.0 for val in msg.data]

    def cb_right_gripper(self, msg: Int32MultiArray):
        # 将gripper数值除以1000进行缩放
        self.right_hand_state = [val / 1000.0 for val in msg.data]

    def pad_image(self, img):
        # pad height from HxW to 480x640, center vertically
        h, w = img.shape[:2]
        target_h, target_w = 480, 640
        top = (target_h - h) // 2
        bottom = target_h - h - top
        left = (target_w - w) // 2
        right = target_w - w - left
        # pad with black
        return cv2.copyMakeBorder(img, top, bottom, left, right,
                                  cv2.BORDER_CONSTANT, value=[0,0,0])
        
    def head_rgb_callback(self, msg):
        self.head_rgb = self.cv_bridge.imgmsg_to_cv2(msg, 'bgr8')
        self.head_rgb = self.pad_image(self.head_rgb)
        self.head_rgb_stamp = msg.header.stamp

    def left_wrist_rgb_callback(self, msg):
        self.left_wrist_rgb = self.cv_bridge.imgmsg_to_cv2(msg, 'bgr8')
        self.left_wrist_rgb_stamp = msg.header.stamp

    def right_wrist_rgb_callback(self, msg):
        self.right_wrist_rgb = self.cv_bridge.imgmsg_to_cv2(msg, 'bgr8')
        self.right_wrist_rgb_stamp = msg.header.stamp

    def head_depth_callback(self, msg):
        self.head_depth = self.cv_bridge.imgmsg_to_cv2(msg, '32FC1')
        self.head_depth_stamp = msg.header.stamp

    def left_wrist_depth_callback(self, msg):
        self.left_wrist_depth = self.cv_bridge.imgmsg_to_cv2(msg, '32FC1')
        self.left_wrist_depth_stamp = msg.header.stamp

    def right_wrist_depth_callback(self, msg):
        self.right_wrist_depth = self.cv_bridge.imgmsg_to_cv2(msg, '32FC1')
        self.right_wrist_depth_stamp = msg.header.stamp

    def left_arm_pose_callback(self, msg):
        self.left_arm_pose = msg
        self.left_arm_pose_stamp = msg.header.stamp

    def right_arm_pose_callback(self, msg):
        self.right_arm_pose = msg
        self.right_arm_pose_stamp = msg.header.stamp

    def control_joint_position(self, group_name, joint_values):
        """Control joint positions for a specified planning group"""
        if group_name not in self.planning_groups:
            self.get_logger().error(f"Invalid planning group: {group_name}")
            return False

        if len(joint_values) != len(self.planning_groups[group_name]):
            self.get_logger().error(f"Joint values mismatch, expected {len(self.planning_groups[group_name])}, got {len(joint_values)}")
            return False

        self.action_client.wait_for_server()
        goal_msg = MoveGroup.Goal()
        goal_msg.request.workspace_parameters.header.frame_id = 'base_link'
        goal_msg.request.group_name = group_name
        goal_msg.request.allowed_planning_time = 4.0
        goal_msg.request.max_velocity_scaling_factor = 0.5
        goal_msg.request.max_acceleration_scaling_factor = 0.5

        constraints = Constraints()
        for joint_name, joint_value in zip(self.planning_groups[group_name], joint_values):
            joint_constraint = JointConstraint()
            joint_constraint.joint_name = joint_name
            joint_constraint.position = joint_value
            joint_constraint.tolerance_above = 0.02
            joint_constraint.tolerance_below = 0.02
            joint_constraint.weight = 1.0
            constraints.joint_constraints.append(joint_constraint)

        goal_msg.request.goal_constraints.append(constraints)
        future = self.action_client.send_goal_async(goal_msg)
        rclpy.spin_until_future_complete(self, future)
        if future.result() and future.result().accepted:
            result_future = future.result().get_result_async()
            rclpy.spin_until_future_complete(self, result_future)
            if result_future.result().result.error_code.val == 1:
                self.get_logger().info(f"{group_name} joint position control succeeded")
                return True
        self.get_logger().error(f"{group_name} joint position control failed")
        return False

    def control_arms_joint_position(self, joint_values):
        """Control joint positions for the arms planning group with joint limits and velocity check"""
        if len(joint_values) != 14:  # 7 left + 7 right
            self.get_logger().error(f"Arms group expects 14 joints, got {len(joint_values)}")
            return False

        # 分离左臂和右臂的关节值
        left_arm_values = joint_values[:7]
        right_arm_values = joint_values[7:14]

        # 检查左臂关节变化是否过大
        if not self._check_joint_velocity(left_arm_values, 'left_arm'):
            self.get_logger().error("Left arm joint change too large, arms command rejected for safety")
            return False

        # 检查右臂关节变化是否过大
        if not self._check_joint_velocity(right_arm_values, 'right_arm'):
            self.get_logger().error("Right arm joint change too large, arms command rejected for safety")
            return False

        # 分别应用限位
        limited_left = self._apply_joint_limits(left_arm_values, 'left_arm')
        limited_right = self._apply_joint_limits(right_arm_values, 'right_arm')

        # 重新组合
        limited_values = limited_left + limited_right

        return self.control_joint_position('arms', limited_values)

    def control_body_joint_position(self, joint_values):
        """Control joint positions for the body planning group"""
        return self.control_joint_position('body', joint_values)

    def control_left_arm_joint_position(self, joint_values):
        """Control joint positions for the left_arm planning group with joint limits and velocity check"""
        # 检查关节变化是否过大
        if not self._check_joint_velocity(joint_values, 'left_arm'):
            self.get_logger().error("Left arm joint change too large, position command rejected for safety")
            return False

        limited_values = self._apply_joint_limits(joint_values, 'left_arm')
        return self.control_joint_position('left_arm', limited_values)

    def control_right_arm_joint_position(self, joint_values):
        """Control joint positions for the right_arm planning group with joint limits and velocity check"""
        # 检查关节变化是否过大
        if not self._check_joint_velocity(joint_values, 'right_arm'):
            self.get_logger().error("Right arm joint change too large, position command rejected for safety")
            return False

        limited_values = self._apply_joint_limits(joint_values, 'right_arm')
        return self.control_joint_position('right_arm', limited_values)

    def control_neck_joint_position(self, joint_values):
        """Control joint positions for the neck planning group"""
        return self.control_joint_position('neck', joint_values)

    def control_arm_end_effector(self, arm, pose):
        """Control the end-effector pose for a single arm"""
        if arm not in ['left_arm', 'right_arm']:
            self.get_logger().error(f"Invalid arm: {arm}")
            return False

        self.action_client.wait_for_server()
        goal_msg = MoveGroup.Goal()
        goal_msg.request.workspace_parameters.header.frame_id = 'base_link'
        goal_msg.request.group_name = arm
        goal_msg.request.allowed_planning_time = 8.0
        goal_msg.request.max_velocity_scaling_factor = 0.05
        goal_msg.request.max_acceleration_scaling_factor = 0.05

        constraints = Constraints()
        # Position constraint
        pos_constraint = PositionConstraint()
        pos_constraint.header.frame_id = 'base_link'
        pos_constraint.link_name = 'Left_Arm_Link8' if arm == 'left_arm' else 'Right_Arm_Link8'
        pos_constraint.target_point_offset.x = 0.0
        pos_constraint.target_point_offset.y = 0.0
        pos_constraint.target_point_offset.z = 0.0
        bounding_volume = BoundingVolume()
        primitive = SolidPrimitive()
        primitive.type = SolidPrimitive.SPHERE
        primitive.dimensions = [0.1]  # 1cm tolerance
        bounding_volume.primitives.append(primitive)
        bounding_volume.primitive_poses.append(Pose(position=pose.position))
        pos_constraint.constraint_region = bounding_volume
        pos_constraint.weight = 0.8
        constraints.position_constraints.append(pos_constraint)

        # Orientation constraint
        ori_constraint = OrientationConstraint()
        ori_constraint.header.frame_id = 'base_link'
        ori_constraint.link_name = 'Left_Arm_Link8' if arm == 'left_arm' else 'Right_Arm_Link8'
        ori_constraint.orientation = pose.orientation
        ori_constraint.absolute_x_axis_tolerance = 0.1
        ori_constraint.absolute_y_axis_tolerance = 0.1
        ori_constraint.absolute_z_axis_tolerance = 0.1
        ori_constraint.weight = 0.8
        constraints.orientation_constraints.append(ori_constraint)

        goal_msg.request.goal_constraints.append(constraints)
        future = self.action_client.send_goal_async(goal_msg)
        rclpy.spin_until_future_complete(self, future)
        if future.result() and future.result().accepted:
            result_future = future.result().get_result_async()
            rclpy.spin_until_future_complete(self, result_future)
            if result_future.result().result.error_code.val == 1:
                self.get_logger().info(f"{arm} end-effector control succeeded")
                return True
        self.get_logger().error(f"{arm} end-effector control failed")
        return False

    def control_both_arms_end_effector(self, left_pose, right_pose):
        """Control end-effector poses for both arms"""
        success_left = self.control_arm_end_effector('left_arm', left_pose)
        success_right = self.control_arm_end_effector('right_arm', right_pose)
        return success_left and success_right

    def get_full_joint_state(self):
        """Get full joint states, return as an array"""
        if self.joint_state is None:
            self.get_logger().warn("No /joint_states data received")
            return None

        all_joints = (
            self.planning_groups['arms'] +
            self.planning_groups['body'] +
            self.planning_groups['neck']
        )
        joint_positions = {}
        for name, position in zip(self.joint_state.name, self.joint_state.position):
            if name in all_joints:
                joint_positions[name] = position

        if len(joint_positions) != len(set(all_joints)):
            self.get_logger().warn("Incomplete joint states")
            return None

        ordered_positions = [joint_positions[name] for name in sorted(set(all_joints))]
        return ordered_positions
    
    def get_arms_and_hand_state(self):
        """Get arm and hand joint states, return as an array with format: [14 arm joints + 6 left hand + 6 right hand]"""
        if self.joint_state is None:
            self.get_logger().warn("No /joint_states data received")
            return None

        # 只获取手臂关节（14个：左臂7个 + 右臂7个）
        arms_joints = self.planning_groups['arms']
        joint_positions = {}
        for name, position in zip(self.joint_state.name, self.joint_state.position):
            if name in arms_joints:
                joint_positions[name] = position

        if len(joint_positions) != len(set(arms_joints)):
            self.get_logger().warn("Incomplete arm joint states")
            return None

        # 获取前14个手臂关节位置（按字母顺序排序）
        ordered_arm_positions = [joint_positions[name] for name in sorted(set(arms_joints))]

        # 检查手部状态是否可用
        if self.left_hand_state is None or self.right_hand_state is None:
            self.get_logger().warn("Hand states not available")
            return None

        # 确保每只手有6个值，如果不足则用0填充，如果超过则截取前6个
        left_hand_values = list(self.left_hand_state)[:6]
        while len(left_hand_values) < 6:
            left_hand_values.append(1.0)

        right_hand_values = list(self.right_hand_state)[:6]
        while len(right_hand_values) < 6:
            right_hand_values.append(1.0)

        # 组合结果：前14个手臂关节 + 6个左手值 + 6个右手值
        result = ordered_arm_positions + left_hand_values + right_hand_values

        return result
    
    def get_head_image(self):
        """Get head camera image, return CV array and timestamp"""
        if self.head_rgb is None:
            self.get_logger().warn("No head RGB image received")
            return None
        return self.head_rgb
    
    def get_left_wrist_image(self):
        """Get left wrist camera image, return CV array and timestamp"""
        if self.left_wrist_rgb is None:
            self.get_logger().warn("No left wrist RGB image received")
            return None
        return self.left_wrist_rgb
    
    def get_right_wrist_image(self):
        """Get right wrist camera image, return CV array and timestamp"""
        if self.right_wrist_rgb is None:
            self.get_logger().warn("No right wrist RGB image received")
            return None
        return self.right_wrist_rgb

    def get_head_rgb(self):
        """Get head camera RGB image, return CV array and timestamp"""
        if self.head_rgb is None:
            self.get_logger().warn("No head RGB image received")
            return None, None
        return self.head_rgb, self.head_rgb_stamp

    def get_wrist_rgb(self):
        """Get wrist camera RGB images, return left/right CV arrays and timestamps"""
        if self.left_wrist_rgb is None or self.right_wrist_rgb is None:
            self.get_logger().warn("No wrist RGB images received")
            return None, None, None, None
        return (
            self.left_wrist_rgb, self.left_wrist_rgb_stamp,
            self.right_wrist_rgb, self.right_wrist_rgb_stamp
        )

    def get_head_depth(self):
        """Get head camera depth image, return CV array and timestamp"""
        if self.head_depth is None:
            self.get_logger().warn("No head depth image received")
            return None, None
        return self.head_depth, self.head_depth_stamp

    def get_wrist_depth(self):
        """Get wrist camera depth images, return left/right CV arrays and timestamps"""
        if self.left_wrist_depth is None or self.right_wrist_depth is None:
            self.get_logger().warn("No wrist depth images received")
            return None, None, None, None
        return (
            self.left_wrist_depth, self.left_wrist_depth_stamp,
            self.right_wrist_depth, self.right_wrist_depth_stamp
        )

    def get_arm_end_effector_pose(self):
        """Get arm end-effector poses, return arrays and timestamps"""
        if self.left_arm_pose is None or self.right_arm_pose is None:
            self.get_logger().warn("No arm end-effector poses received")
            return None, None, None, None

        left_pose = [
            self.left_arm_pose.pose.position.x,
            self.left_arm_pose.pose.position.y,
            self.left_arm_pose.pose.position.z,
            self.left_arm_pose.pose.orientation.x,
            self.left_arm_pose.pose.orientation.y,
            self.left_arm_pose.pose.orientation.z,
            self.left_arm_pose.pose.orientation.w
        ]
        right_pose = [
            self.right_arm_pose.pose.position.x,
            self.right_arm_pose.pose.position.y,
            self.right_arm_pose.pose.position.z,
            self.right_arm_pose.pose.orientation.x,
            self.right_arm_pose.pose.orientation.y,
            self.right_arm_pose.pose.orientation.z,
            self.right_arm_pose.pose.orientation.w
        ]
        return (
            left_pose, self.left_arm_pose_stamp,
            right_pose, self.right_arm_pose_stamp
        )

    def reset(self):
        """Reset robot to initial pose"""
        # 重置关节变化跟踪
        self.reset_joint_velocity_tracking()

        # initial_positions = {
        #     'arms': [0.0] * 14,  # 14 joints for both arms
        #     'body': [1.047, 2.618, -1.4835, 0],
        #     'neck': [0.0] * 2
        # }

        # data collection mode
        initial_positions = {
            'arms': [0.0] * 14,  # 14 joints for both arms
            'body': [math.radians(14), math.radians(70), math.radians(-32), 0.0],
            'neck': [-0.0, 0.5998822450637817]
        }

        success = True
        for group, values in initial_positions.items():
            if not self.control_joint_position(group, values):
                self.get_logger().error(f"Reset {group} failed")
                success = False
        if success:
            self.get_logger().info("Robot reset to initial pose successfully")
        return success

    def reset_ros2controller(self):
        """Reset robot to initial pose using ROS2 controllers"""
        # 重置关节变化跟踪
        self.reset_joint_velocity_tracking()

        # 定义初始位置 (数据收集模式)
        initial_positions = {
            'left_arm': [-1.158, 0.822, 1.341, -0.370, 0.131, -1.396, 0.200],   # 左臂7个关节
            'right_arm': [0.683, -0.673, -0.475, 0.505, -0.632, 1.396, 0.143],  # 右臂7个关节
            'body': [math.radians(18), math.radians(70), math.radians(-32), 0.0],
            'neck': [-0.0, math.radians(29)]
        }

        success = True

        # 使用ROS2控制器接口重置各个部分
        try:
            # 重置左臂
            if not self.control_left_arm_joint_position_ros2controller(initial_positions['left_arm']):
                self.get_logger().error("Reset left arm failed")
                success = False
            else:
                self.get_logger().info("Left arm reset to initial position")

            # 重置右臂
            if not self.control_right_arm_joint_position_ros2controller(initial_positions['right_arm']):
                self.get_logger().error("Reset right arm failed")
                success = False
            else:
                self.get_logger().info("Right arm reset to initial position")

            # 重置身体
            if not self.control_body_joint_position_ros2controller(initial_positions['body']):
                self.get_logger().error("Reset body failed")
                success = False
            else:
                self.get_logger().info("Body reset to initial position")

            # 重置颈部
            if not self.control_neck_joint_position_ros2controller(initial_positions['neck']):
                self.get_logger().error("Reset neck failed")
                success = False
            else:
                self.get_logger().info("Neck reset to initial position")

        except Exception as e:
            self.get_logger().error(f"Error during ROS2 controller reset: {e}")
            success = False

        if success:
            self.get_logger().info("Robot reset to initial pose successfully using ROS2 controllers")
        else:
            self.get_logger().error("Robot reset failed - some components could not reach initial position")

        return success

    def shutdown(self):
        """Clean up resources"""
        self.destroy_node()

    def close_gripper(self, gripper_name):
        """Close a gripper"""
        if gripper_name == "left":
            self.rs485_gripper_left.close_gripper()
        elif gripper_name == "right":
            self.rs485_gripper_right.close_gripper()
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")
    
    def open_gripper(self, gripper_name):
        """Open a gripper"""
        if gripper_name == "left":
            self.rs485_gripper_left.open_gripper()
        elif gripper_name == "right":
            self.rs485_gripper_right.open_gripper()
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")

    def move_gripper_to_position(self, gripper_name, position):
        """Move a gripper to a position"""
        if gripper_name == "left":
            self.rs485_gripper_left.move_to_position(position)
        elif gripper_name == "right":
            self.rs485_gripper_right.move_to_position(position)
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")

    def get_gripper_state(self, gripper_name):
        """Get a gripper state"""
        if gripper_name == "left":
            return self.rs485_gripper_left.get_status()
        elif gripper_name == "right":
            return self.rs485_gripper_right.get_status()
        else:
            self.get_logger().error(f"Invalid gripper name: {gripper_name}")

    def publish_gripper_states(self):
        """Publish gripper states as [left_gripper_position, right_gripper_position]"""
        try:
            # Get left gripper state
            left_state = self.get_gripper_state("left")
            left_position = 0.0  # Default value
            if left_state and 'position' in left_state:
                left_position = float(left_state['position'])

            # Get right gripper state
            right_state = self.get_gripper_state("right")
            right_position = 0.0  # Default value
            if right_state and 'position' in right_state:
                right_position = float(right_state['position'])

            # Create and publish the message
            msg = Float64MultiArray()
            msg.data = [left_position, right_position]
            self.gripper_state_pub.publish(msg)

        except Exception as e:
            self.get_logger().warn(f"Failed to publish gripper states: {e}")
