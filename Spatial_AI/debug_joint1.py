#!/usr/bin/env python3
import rclpy
from ymrobot import YMrobot
import time
import math

def test_joint1_control():
    """测试关节1的控制"""
    print("开始测试关节1控制...")
    
    # 初始化ROS和机器人
    rclpy.init()
    robot = YMrobot()
    time.sleep(2.0)
    
    try:
        print("测试1: 关节1设置为0弧度")
        robot.control_right_arm_joint_position_ros2controller([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(3.0)
        
        print("测试2: 关节1设置为0.1弧度 (约5.7度)")
        robot.control_right_arm_joint_position_ros2controller([0.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(3.0)
        
        print("测试3: 关节1设置为0.2弧度 (约11.5度)")
        robot.control_right_arm_joint_position_ros2controller([0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(3.0)
        
        print("测试4: 关节1设置为-0.1弧度 (约-5.7度)")
        robot.control_right_arm_joint_position_ros2controller([-0.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(3.0)
        
        print("测试5: 回到0位置")
        robot.control_right_arm_joint_position_ros2controller([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
        time.sleep(3.0)
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
    finally:
        robot.shutdown()
        rclpy.shutdown()

if __name__ == "__main__":
    test_joint1_control() 