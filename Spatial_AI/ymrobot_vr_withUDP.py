#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Pose, Point, Quaternion
from ymrobot import YMrobot
from sensor_msgs.msg import JointState
from std_msgs.msg import Float64MultiArray
import socket
import struct
import time
import math
import numpy as np
from datetime import datetime
import threading
from collections import deque
import logging

# 配置日志系统来抑制深层库的调试消息
def setup_logging():
    """配置日志系统，抑制不必要的调试消息"""
    # 设置根日志级别为WARNING，抑制INFO和DEBUG消息
    logging.getLogger().setLevel(logging.WARNING)
    
    # 特别抑制一些常见库的调试消息
    logging.getLogger('ymrobot').setLevel(logging.ERROR)
    logging.getLogger('rclpy').setLevel(logging.ERROR)
    logging.getLogger('ros2').setLevel(logging.ERROR)
    
    # 如果有其他特定的库产生调试消息，也可以在这里添加
    # 例如：logging.getLogger('some_library').setLevel(logging.ERROR)
    
    # 只保留我们自己的重要消息
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

# 在程序开始时调用日志配置
setup_logging()

class UDPRobotController(Node):
    def __init__(self, host='0.0.0.0', port=8080):
        super().__init__('udp_robot_controller')
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.robot = None
        self.rclpy_initialized = False
        self.test_alternative_index = True
        # 添加夹爪状态跟踪
        self.left_gripper_closed = False  # 跟踪左手夹爪是否已关闭
        self.right_gripper_closed = False  # 跟踪右手夹爪是否已关闭

        # 创建夹爪指令发布器
        self.gripper_cmd_pub = self.create_publisher(
            Float64MultiArray, '/gripper_commands', 10)
        self.get_logger().info("Gripper command publisher initialized")
        
    def initialize_robot(self):
        try:
            # ROS2已经在__init__中通过super().__init__初始化了
            self.robot = YMrobot()
            time.sleep(2.0)
            print("机器人初始化成功")
            self.get_logger().info("Robot initialized successfully")
            return True
        except Exception as e:
            print(f"机器人初始化失败: {e}")
            self.get_logger().error(f"Robot initialization failed: {e}")
            return False
    
    def shutdown_robot(self):
        if self.robot:
            self.robot.shutdown()
        if self.rclpy_initialized:
            rclpy.shutdown()
        print("机器人已关闭")
    
    def start_udp(self):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.host, self.port))
            self.socket.settimeout(1.0)
            self.running = True
            print(f"UDP接收器已启动，监听地址: {self.host}:{self.port}")
            print("等待数据...")
            return True
        except Exception as e:
            print(f"启动UDP接收器失败: {e}")
            return False
    
    def stop_udp(self):
        self.running = False
        if self.socket:
            self.socket.close()
            print("UDP接收器已停止")
    
    def parse_data_packet(self, data):
        try:
            if len(data) < 85:
                return None
            
            if data[0] != 0xAA or data[1] != 0xBB:
                return None
            
            data_length = data[2]
            if data_length != 16:
                return None
            
            torque_raw = struct.unpack('<h', data[3:5])[0]
            torque_value = torque_raw / 10.0
            
            angles = []
            offset = 5
            for i in range(16):
                angle_bytes = data[offset + i*4:offset + (i+1)*4]
                angle_value = struct.unpack('<f', angle_bytes)[0]
                angles.append(angle_value)
            
            return {
                'torque': torque_value,
                'angles': angles,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return None
    
    def control_left_arm(self, angles):
        if not self.robot:
            print("机器人未初始化")
            return
        
        try:
            left_angles = angles[0:8]  # 左手数据在前8个角度值
            
            #print(f"左手UDP原始角度数据: {left_angles}")
            
            joint1_rad = math.radians(left_angles[0])
            joint2_rad = math.radians(left_angles[1])
            joint3_rad = math.radians(left_angles[2])
            joint4_rad = math.radians(left_angles[3])
            joint5_rad = math.radians(left_angles[4])
            joint6_rad = math.radians(left_angles[5])
            joint7_rad = math.radians(left_angles[6])

            control_values = [joint1_rad, joint2_rad, joint3_rad, joint4_rad, joint5_rad, joint6_rad, joint7_rad]
            
            result = self.robot.control_left_arm_joint_position_ros2controller(control_values)
            #print(f"左手控制命令发送结果: {result}")
            
            print(f"控制左臂: [{control_values[0]:.3f}, {control_values[1]:.3f}, {control_values[2]:.3f}, {control_values[3]:.3f}, {control_values[4]:.3f}, {control_values[5]:.3f}, {control_values[6]:.3f}]")
            
        except Exception as e:
            print(f"控制左臂时出错: {e}")
    
    def control_right_arm(self, angles):
        if not self.robot:
            print("机器人未初始化")
            return
        
        try:
            right_angles = angles[8:16]  # 右手数据在后8个角度值
            
            #print(f"右手UDP原始角度数据: {right_angles}")
            
            joint1_rad = math.radians(right_angles[0])
            joint2_rad = math.radians(right_angles[1])
            joint3_rad = math.radians(right_angles[2])
            joint4_rad = math.radians(right_angles[3])
            joint5_rad = math.radians(right_angles[4])
            joint6_rad = math.radians(right_angles[5])
            joint7_rad = math.radians(right_angles[6])

            control_values = [joint1_rad, joint2_rad, joint3_rad, joint4_rad, joint5_rad, joint6_rad, joint7_rad]
            
            result = self.robot.control_right_arm_joint_position_ros2controller(control_values)
            #print(f"右手控制命令发送结果: {result}")
            
            print(f"控制右臂: [{control_values[0]:.3f}, {control_values[1]:.3f}, {control_values[2]:.3f}, {control_values[3]:.3f}, {control_values[4]:.3f}, {control_values[5]:.3f}, {control_values[6]:.3f}]")
            
        except Exception as e:
            print(f"控制右臂时出错: {e}")
    
    def control_gripper(self, left_gripper_value, right_gripper_value):
        if not self.robot:
            print("机器人未初始化")
            return
        try:
            # 左手夹爪控制 - 注意左右翻转
            left_gripper_cmd = int(255-(left_gripper_value / 100 * 255))
            # 只有当夹爪数值不为0时才发送命令
            if left_gripper_value != 0:
                self.robot.move_gripper_to_position("left", left_gripper_cmd)

            # 右手夹爪控制 - 注意左右翻转
            right_gripper_cmd = int(255-(right_gripper_value / 100 * 255))
            # 只有当夹爪数值不为0时才发送命令
            if right_gripper_value != 0:
                self.robot.move_gripper_to_position("right", right_gripper_cmd)

            # 发布夹爪指令信息
            self.publish_gripper_commands(left_gripper_cmd, right_gripper_cmd)

        except Exception as e:
            print(f"控制夹爪时出错: {e}")

    def publish_gripper_commands(self, left_cmd, right_cmd):
        """发布夹爪指令信息"""
        try:
            # 创建消息：[left_input_value, right_input_value, left_command, right_command]
            msg = Float64MultiArray()
            msg.data = [
                float(left_cmd),      # 左夹爪指令值 (0-255)
                float(right_cmd)      # 右夹爪指令值 (0-255)
            ]
            self.gripper_cmd_pub.publish(msg)

        except Exception as e:
            self.get_logger().warn(f"Failed to publish gripper commands: {e}")
    
    def run(self):
        if not self.initialize_robot():
            return
        
        if not self.start_udp():
            return
        
        packet_count = 0

        self.robot.reset_ros2controller()
        start_time = time.time()
        
        try:
            while self.running:
                try:
                    data, addr = self.socket.recvfrom(1024)
                    packet_count += 1
                    
                    parsed_data = self.parse_data_packet(data)
                    
                    if parsed_data:
                        #print(f"解析的UDP数据 - 扭矩: {parsed_data['torque']:.1f}, 角度数组长度: {len(parsed_data['angles'])}")
                        #print(f"所有角度数据: {parsed_data['angles']}")
                        
                        # 控制左手
                        self.control_left_arm(parsed_data['angles'])
                        
                        # 控制右手
                        self.control_right_arm(parsed_data['angles'])
                        
                        # 夹爪控制 - 假设左手夹爪在第7个值，右手夹爪在第15个值
                        left_gripper_value = parsed_data['angles'][7]
                        right_gripper_value = parsed_data['angles'][15]
                        print(f"左手夹爪值: {left_gripper_value:.1f}, 右手夹爪值: {right_gripper_value:.1f}")
                        
                        # 调用夹爪控制方法
                        self.control_gripper(left_gripper_value, right_gripper_value)
                        
                        elapsed_time = time.time() - start_time
                        if elapsed_time >= 1.0:
                            fps = packet_count / elapsed_time
                            print(f"FPS: {fps:.1f}")
                            packet_count = 0
                            start_time = time.time()
                        
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"接收数据时出错: {e}")
                    
        except KeyboardInterrupt:
            print("\n用户中断，正在停止...")
        finally:
            self.stop_udp()
            self.shutdown_robot()

def main():
    # 确保日志配置在程序开始时就被设置
    setup_logging()

    # 初始化ROS2
    rclpy.init()

    try:
        controller = UDPRobotController(host='0.0.0.0', port=8080)
        controller.run()
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        # 清理ROS2资源
        if 'controller' in locals():
            controller.destroy_node()
        rclpy.shutdown()

if __name__ == "__main__":
    main()
