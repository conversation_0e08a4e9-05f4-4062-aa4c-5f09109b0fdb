# API 参考文档

## 概述

本文档详细介绍夹爪控制系统的所有 API 接口，包括抽象基类、具体实现、控制器、线程池管理组件等模块的使用方法。

## 核心架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    夹爪控制系统架构                           │
├─────────────────────────────────────────────────────────────┤
│  应用层: main.py, examples/                                 │
├─────────────────────────────────────────────────────────────┤
│  控制层: GripperController, MultiGripperController          │
├─────────────────────────────────────────────────────────────┤
│  实现层: GripperCAN, GripperRS485                           │
├─────────────────────────────────────────────────────────────┤
│  基类层: GripperBase (抽象基类)                              │
├─────────────────────────────────────────────────────────────┤
│  组件层: ThreadPoolManager, StatusManager, PerformanceMonitor │
├─────────────────────────────────────────────────────────────┤
│  通信层: CAN总线, RS485串口                                  │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块

### 1. 夹爪抽象基类 (gripper_base.py)

#### GripperBase 类

所有夹爪实现的抽象基类，定义了统一的接口和便捷方法。

```python
from src.gripper_base import GripperBase, GripperType, GripperState, GripperFault
```

##### 枚举类型

```python
class GripperType(Enum):
    CAN = "can"
    RS485 = "rs485"

class GripperState(Enum):
    TARGET_REACHED = 0x00  # 到达目标位置
    MOVING = 0x01          # 正在移动
    STALLED = 0x02         # 堵转
    OBJECT_DROPPED = 0x03  # 物体掉落

class GripperFault(Enum):
    NO_FAULT = 0x00        # 无故障
    OVERTEMP = 0x01        # 过温
    OVERSPEED = 0x02       # 超速
    INIT_FAULT = 0x03      # 初始化故障
    LIMIT_DETECTION = 0x04 # 限位检测
```

##### 抽象方法（子类必须实现）

```python
@abstractmethod
def connect(self) -> bool
```

- **功能**: 连接到夹爪设备
- **返回**: 连接成功返回 True，失败返回 False

```python
@abstractmethod
def disconnect(self) -> bool
```

- **功能**: 断开夹爪连接
- **返回**: 断开成功返回 True，失败返回 False

```python
@abstractmethod
def send_command(self, pos_cmd: int, force_cmd: int = 0xFF,
                vel_cmd: int = 0xFF, acc_cmd: int = 0xFF,
                dec_cmd: int = 0xFF, timeout: float = 5.0,
                wait_for_completion: bool = True,
                position_tolerance: int = 10) -> Optional[Dict[str, Any]]
```

- **功能**: 发送控制指令到夹爪
- **参数**:
  - `pos_cmd`: 目标位置 (0=闭合，0xFF=张开)
  - `force_cmd`: 力矩 (0-0xFF)
  - `vel_cmd`: 速度 (0-0xFF)
  - `acc_cmd`: 加速度 (0-0xFF)
  - `dec_cmd`: 减速度 (0-0xFF)
  - `timeout`: 超时时间（秒）
  - `wait_for_completion`: 是否等待夹爪到达目标位置
  - `position_tolerance`: 位置容差（允许的位置误差）
- **返回**: 接收到的状态字典（成功）或 None（超时/失败）

```python
@abstractmethod
def get_status(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]
```

- **功能**: 获取夹爪当前状态
- **参数**: `timeout` - 超时时间（秒）
- **返回**: 状态字典或 None

```python
@abstractmethod
def parse_status(self, data: bytes) -> Optional[Dict[str, Any]]
```

- **功能**: 解析状态数据
- **参数**: `data` - 原始状态数据
- **返回**: 解析后的状态字典

```python
@abstractmethod
def close(self)
```

- **功能**: 关闭夹爪连接和资源

##### 便捷方法（基类已实现）

```python
def open_gripper(self, force: int = 0xFF, timeout: float = 5.0) -> Optional[Dict[str, Any]]
```

- **功能**: 打开夹爪
- **参数**:
  - `force`: 力矩 (0-0xFF)
  - `timeout`: 超时时间
- **返回**: 状态字典或 None

```python
def close_gripper(self, force: int = 0xFF, timeout: float = 5.0) -> Optional[Dict[str, Any]]
```

- **功能**: 关闭夹爪
- **参数**:
  - `force`: 力矩 (0-0xFF)
  - `timeout`: 超时时间
- **返回**: 状态字典或 None

```python
def move_to_position(self, position: int, force: int = 0xFF,
                    timeout: float = 5.0) -> Optional[Dict[str, Any]]
```

- **功能**: 移动到指定位置
- **参数**:
  - `position`: 目标位置 (0-0xFF, 0=关闭, 0xFF=打开)
  - `force`: 力矩 (0-0xFF)
  - `timeout`: 超时时间
- **返回**: 状态字典或 None

##### 状态检查方法

```python
def is_moving(self) -> bool
```

- **功能**: 检查夹爪是否正在移动
- **返回**: True if moving, False otherwise

```python
def has_fault(self) -> bool
```

- **功能**: 检查夹爪是否有故障
- **返回**: True if fault exists, False otherwise

```python
def get_position(self) -> Optional[int]
```

- **功能**: 获取当前位置
- **返回**: 当前位置值或 None

```python
def get_fault_code(self) -> Optional[int]
```

- **功能**: 获取故障代码
- **返回**: 故障代码或 None

```python
def set_status_callback(self, callback: Callable[[Dict[str, Any]], None])
```

- **功能**: 设置状态回调函数
- **参数**: `callback` - 回调函数，接收状态字典作为参数

```python
def set_query_interval(self, interval: float)
```

- **功能**: 设置状态查询间隔
- **参数**: `interval` - 查询间隔（秒）

```python
def wait_for_target(self, timeout: float = 10.0, position_tolerance: int = 10) -> bool
```

- **功能**: 等待夹爪到达目标位置
- **参数**:
  - `timeout`: 超时时间（秒）
  - `position_tolerance`: 位置容差
- **返回**: 是否成功到达目标位置

```python
def emergency_stop(self) -> bool
```

- **功能**: 紧急停止
- **返回**: 停止成功返回 True

```python
def get_gripper_type(self) -> GripperType
```

- **功能**: 获取夹爪类型
- **返回**: 夹爪类型枚举

### 2. CAN 夹爪实现 (Gripper_can.py)

#### GripperCAN 类

CAN 协议夹爪的具体实现，基于 GripperBase 抽象基类。

```python
from src.Gripper_can import GripperCAN

gripper = GripperCAN(node_id=1, channel=0, device_type=VCI_USBCAN2)
```

##### 构造函数参数

- `node_id`: CAN 节点 ID (默认 1)
- `channel`: CAN 通道 (默认 0)
- `device_type`: 设备类型 (默认 VCI_USBCAN2)

##### 特有方法

```python
def _open_device(self)
```

- **功能**: 打开 CAN 设备并初始化
- **异常**: 打开失败时抛出异常

```python
def _start_recv_thread(self)
```

- **功能**: 启动 CAN 消息接收线程

```python
def _send_target_position_query(self)
```

- **功能**: 发送目标位置指令，用于高频获取状态回调

```python
def parse_status(self, data: bytes) -> Optional[Dict[str, Any]]
```

- **功能**: 解析 CAN 状态数据
- **参数**: `data` - 8 字节 CAN 数据
- **返回**: 解析后的状态字典

```python
def is_at_position(self, target_position, tolerance=5) -> bool
```

- **功能**: 检查夹爪是否在指定位置
- **参数**:
  - `target_position`: 目标位置
  - `tolerance`: 位置容差
- **返回**: True if at position, False otherwise

```python
def wait_for_position(self, target_position, timeout=5.0, tolerance=5) -> bool
```

- **功能**: 等待夹爪到达指定位置
- **参数**:
  - `target_position`: 目标位置
  - `timeout`: 超时时间
  - `tolerance`: 位置容差
- **返回**: True if reached, False if timeout

```python
def get_target_position(self) -> int
```

- **功能**: 获取当前目标位置
- **返回**: 目标位置 (0-0xFF)

```python
def get_target_parameters(self) -> Dict[str, int]
```

- **功能**: 获取所有目标参数
- **返回**: 目标参数字典

### 3. RS485 夹爪实现 (Gripper_485.py)

#### GripperRS485 类

RS485 串口协议夹爪的具体实现，基于 GripperBase 抽象基类。

```python
from src.Gripper_485 import GripperRS485

gripper = GripperRS485(port='/dev/ttyUSB0', node_id=1, baudrate=115200)
```

##### 构造函数参数

- `port`: 串口名称 (如 '/dev/ttyUSB0' 或 'COM3')
- `node_id`: 节点 ID (默认 1)
- `baudrate`: 波特率 (默认 115200)

##### 特有方法

```python
def _start_recv_thread(self)
```

- **功能**: 启动 RS485 消息接收线程

```python
def _recv_loop(self)
```

- **功能**: 接收线程主循环，处理接收到的数据

```python
def _send_status_query(self)
```

- **功能**: 发送状态查询指令

```python
def _build_command_frame(self, pos_cmd: int, force_cmd: int = 0xFF,
                        vel_cmd: int = 0xFF, acc_cmd: int = 0xFF,
                        dec_cmd: int = 0xFF) -> bytes
```

- **功能**: 构建命令帧
- **参数**: 位置、力矩、速度、加速度、减速度命令
- **返回**: 完整的命令帧字节序列

```python
def checksum(self, buf: bytes) -> int
```

- **功能**: 计算校验和
- **参数**: `buf` - 数据缓冲区
- **返回**: 校验和值

```python
def parse_status(self, status_frame: bytes) -> Optional[Dict[str, Any]]
```

- **功能**: 解析 RS485 状态帧
- **参数**: `status_frame` - 12 字节状态帧
- **返回**: 解析后的状态字典

### 4. 夹爪控制器 (gripper_controller.py)

#### GripperController 类

单夹爪控制器，提供统一的接口和额外的管理功能。

```python
from src.gripper_controller import GripperController

# 使用已创建的夹爪实例
controller = GripperController(gripper, name="gripper1")
```

##### 构造函数参数

- `gripper`: 夹爪实例 (GripperBase 子类实例)
- `name`: 夹爪名称 (默认 "gripper")

##### 控制方法

```python
def connect(self) -> bool
```

- **功能**: 连接夹爪
- **返回**: 连接成功返回 True

```python
def disconnect(self) -> bool
```

- **功能**: 断开夹爪连接
- **返回**: 断开成功返回 True

```python
def open_gripper(self, **kwargs) -> Optional[Dict[str, Any]]
```

- **功能**: 打开夹爪
- **参数**: 传递给底层夹爪实现的参数
- **返回**: 状态字典或 None

```python
def close_gripper(self, **kwargs) -> Optional[Dict[str, Any]]
```

- **功能**: 关闭夹爪
- **参数**: 传递给底层夹爪实现的参数
- **返回**: 状态字典或 None

```python
def move_to_position(self, position: int, **kwargs) -> Optional[Dict[str, Any]]
```

- **功能**: 移动到指定位置
- **参数**:
  - `position`: 目标位置
  - `**kwargs`: 其他参数
- **返回**: 状态字典或 None

```python
def get_status_statistics(self) -> Dict[str, Any]
```

- **功能**: 获取状态统计信息
- **返回**: 统计信息字典

```python
def clear_status_log(self)
```

- **功能**: 清空状态历史记录

#### MultiGripperController 类

多夹爪协调控制器。

```python
from gripper_controller import MultiGripperController, GripperType

multi = MultiGripperController()
```

##### 夹爪管理方法

```python
def add_gripper(self, name: str, gripper_type: GripperType, **kwargs) -> bool
```

- **功能**: 添加夹爪
- **参数**:
  - `name` - 夹爪名称
  - `gripper_type` - 夹爪类型
  - `**kwargs` - 夹爪参数
- **返回**: 成功返回 True

```python
def remove_gripper(self, name: str) -> bool
```

- **功能**: 移除夹爪
- **参数**: `name` - 夹爪名称
- **返回**: 成功返回 True

```python
def get_gripper(self, name: str) -> Optional[GripperController]
```

- **功能**: 获取指定夹爪
- **参数**: `name` - 夹爪名称
- **返回**: 夹爪控制器或 None

```python
def get_all_grippers(self) -> Dict[str, GripperController]
```

- **功能**: 获取所有夹爪
- **返回**: 夹爪字典

##### 协调控制方法

```python
def execute_parallel(self, commands: Dict[str, Callable]) -> Dict[str, Any]
```

- **功能**: 并行执行命令
- **参数**: `commands` - 命令字典 {夹爪名: 命令函数}
- **返回**: 执行结果字典

```python
def synchronize_positions(self, positions: Dict[str, int], timeout: float = 5.0) -> bool
```

- **功能**: 同步位置控制
- **参数**:
  - `positions` - 位置字典 {夹爪名: 目标位置}
  - `timeout` - 超时时间
- **返回**: 成功返回 True

```python
def emergency_stop(self)
```

- **功能**: 紧急停止所有夹爪

```python
def close_all(self)
```

- **功能**: 关闭所有夹爪连接

#### 便捷创建函数

```python
def create_can_gripper(node_id: int = 1, channel: int = 0, **kwargs) -> GripperController
```

- **功能**: 创建 CAN 夹爪控制器
- **参数**: CAN 参数
- **返回**: 夹爪控制器实例

```python
def create_rs485_gripper(port: str, node_id: int = 1, baudrate: int = 115200, **kwargs) -> GripperController
```

- **功能**: 创建 RS485 夹爪控制器
- **参数**: RS485 参数
- **返回**: 夹爪控制器实例

```python
def create_gripper(gripper_type: GripperType, **kwargs) -> GripperController
```

- **功能**: 通用夹爪创建函数
- **参数**: 夹爪类型和参数
- **返回**: 夹爪控制器实例

### 5. 日志系统 (logger_config.py)

#### LoggerConfig 类

日志配置管理类。

```python
from logger_config import LoggerConfig, get_gripper_logger, get_test_logger
```

##### 主要方法

```python
@classmethod
def get_logger(cls, name: str = 'default', log_dir: Optional[str] = None,
               log_level: int = logging.INFO, console_level: int = logging.INFO,
               file_level: int = logging.DEBUG, log_format: Optional[str] = None,
               date_format: Optional[str] = None) -> logging.Logger
```

- **功能**: 获取或创建 logger 实例
- **参数**: 详细的日志配置参数
- **返回**: logger 实例

##### 预设配置函数

```python
def get_gripper_logger() -> logging.Logger
```

- **功能**: 获取夹爪专用 logger
- **返回**: 配置好的 logger 实例

```python
def get_test_logger() -> logging.Logger
```

- **功能**: 获取测试专用 logger
- **返回**: 配置好的 logger 实例

```python
def get_logger(name: str = 'custom', **kwargs) -> logging.Logger
```

- **功能**: 获取自定义 logger
- **参数**: logger 名称和配置参数
- **返回**: logger 实例

##### 全局控制方法

```python
@classmethod
def set_global_level(cls, level: int)
```

- **功能**: 设置所有 logger 的级别
- **参数**: `level` - 日志级别

```python
@classmethod
def disable_console_output(cls)
```

- **功能**: 禁用所有 logger 的控制台输出

```python
@classmethod
def enable_console_output(cls, level: int = logging.INFO)
```

- **功能**: 启用控制台输出
- **参数**: `level` - 控制台输出级别

## 状态数据格式

### 状态字典结构

所有夹爪返回的状态数据都使用统一的字典格式：

```python
{
    'fault_code': int,    # 故障码 (0=无故障)
    'state': int,         # 状态码 (0=到达目标, 1=移动中, 2=堵转, 3=物体掉落)
    'position': int,      # 当前位置 (0-255)
    'velocity': int,      # 当前速度 (0-255)
    'force': int,         # 当前力矩 (0-255)
    'reserved1': int,     # 保留字段1
    'reserved2': int,     # 保留字段2
    'reserved3': int      # 保留字段3
}
```

### 状态码含义

| 状态码 | 含义           |
| ------ | -------------- |
| 0      | 已达到目标位置 |
| 1      | 夹爪移动中     |
| 2      | 夹爪堵转       |
| 3      | 物体掉落       |

### 故障码含义

| 故障码 | 含义         |
| ------ | ------------ |
| 0      | 无故障       |
| 1      | 过温警报     |
| 2      | 超速警报     |
| 3      | 初始化失败   |
| 4      | 超限检测警报 |

## 使用示例

### 基本使用

```python
from gripper_controller import create_can_gripper
from logger_config import get_gripper_logger

logger = get_gripper_logger()

# 创建夹爪
gripper = create_can_gripper(node_id=1, channel=0)

# 设置回调
def status_callback(status):
    logger.info(f"状态更新: {status}")

gripper.set_status_callback(status_callback)

# 控制夹爪
gripper.open_gripper()
gripper.wait_for_completion()

gripper.close_gripper(force_cmd=0x80)
gripper.wait_for_completion()

# 位置控制
gripper.set_position_percent(50.0)
gripper.wait_for_completion()

# 获取状态
position = gripper.get_position_percent()
logger.info(f"当前位置: {position}%")

# 关闭
gripper.close()
```

### 多夹爪协调

```python
from gripper_controller import MultiGripperController, GripperType

multi = MultiGripperController()

# 添加夹爪
multi.add_gripper("left", GripperType.CAN, node_id=1, channel=0)
multi.add_gripper("right", GripperType.RS485, port='/dev/ttyUSB0', node_id=1)

# 同步控制
positions = {"left": 0x80, "right": 0x80}
success = multi.synchronize_positions(positions, timeout=5.0)

# 并行执行
commands = {
    "left": lambda g: g.open_gripper(),
    "right": lambda g: g.close_gripper()
}
results = multi.execute_parallel(commands)

# 关闭
multi.close_all()
```

### 自定义日志

```python
from logger_config import LoggerConfig
import logging

# 创建自定义logger
custom_logger = LoggerConfig.get_logger(
    name='my_app',
    log_dir='./my_logs',
    log_level=logging.DEBUG,
    console_level=logging.WARNING
)

custom_logger.info("自定义日志消息")
```

## 错误处理

### 常见异常

- `ValueError`: 不支持的夹爪类型或无效参数
- `ConnectionError`: 硬件连接失败
- `TimeoutError`: 操作超时
- `RuntimeError`: 运行时错误

### 错误处理示例

```python
try:
    gripper = create_can_gripper(node_id=1, channel=0)
    result = gripper.open_gripper()
    if result is None:
        logger.warning("指令执行超时")
except ValueError as e:
    logger.error(f"参数错误: {e}")
except ConnectionError as e:
    logger.error(f"连接失败: {e}")
except Exception as e:
    logger.error(f"未知错误: {e}")
finally:
    if 'gripper' in locals():
        gripper.close()
```

## 性能注意事项

1. **线程安全**: 所有 API 都是线程安全的
2. **资源管理**: 使用完毕后及时调用`close()`方法
3. **回调函数**: 状态回调函数应尽快返回，避免阻塞
4. **日志级别**: 生产环境建议使用 INFO 级别，调试时使用 DEBUG 级别
5. **超时设置**: 根据实际硬件响应时间调整超时参数

---

_最后更新: 2025-07-10_
