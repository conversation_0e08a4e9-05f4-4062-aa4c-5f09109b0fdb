# 脚本使用说明

## setup_serial_permissions.sh

这个脚本用于永久解决串口设备权限问题，让您无需每次都使用sudo运行夹爪程序。

### 功能

1. **创建udev规则**：自动为串口设备设置适当权限
2. **用户组管理**：将用户添加到dialout组
3. **权限验证**：检查设置是否正确
4. **设备检查**：显示当前串口设备状态

### 使用方法

#### 1. 基本使用（推荐）

```bash
# 为当前用户设置权限
sudo ./scripts/setup_serial_permissions.sh
```

#### 2. 为指定用户设置权限

```bash
# 为特定用户设置权限
sudo ./scripts/setup_serial_permissions.sh username
```

#### 3. 仅检查设备状态

```bash
# 检查当前串口设备
sudo ./scripts/setup_serial_permissions.sh --check
```

#### 4. 显示帮助信息

```bash
# 显示使用说明
./scripts/setup_serial_permissions.sh --help
```

### 脚本执行过程

1. **检查权限**：确保以root权限运行
2. **备份规则**：备份现有udev规则（如果存在）
3. **创建规则**：创建新的udev规则文件
4. **添加用户**：将用户添加到dialout组
5. **重载规则**：重新加载udev规则
6. **验证设置**：检查配置是否正确

### 支持的设备

脚本会为以下类型的串口设备设置权限：

- **USB转串口设备**：`/dev/ttyUSB*`
- **USB ACM设备**：`/dev/ttyACM*`
- **传统串口**：`/dev/ttyS*`
- **特定芯片**：
  - FTDI芯片 (VID:0403, PID:6001)
  - CH340/CH341芯片 (VID:1a86, PID:7523)
  - CP210x芯片 (VID:10c4, PID:ea60)

### 执行示例

```bash
$ sudo ./scripts/setup_serial_permissions.sh
========================================
    夹爪串口权限设置脚本
========================================

[INFO] 开始为用户 chenzhouyi 设置串口权限...
[INFO] 创建udev规则文件: /etc/udev/rules.d/50-gripper-serial.rules
[SUCCESS] udev规则文件创建成功
[INFO] 将用户 chenzhouyi 添加到 dialout 组
[SUCCESS] 用户 chenzhouyi 已添加到 dialout 组
[INFO] 重新加载udev规则...
[SUCCESS] udev规则重新加载成功
[INFO] 触发udev事件...
[SUCCESS] udev事件触发成功

[INFO] 检查当前设备状态:
  /dev/ttyUSB0: crw-rw-rw- 1 root dialout 188, 0 Jul 15 16:15 /dev/ttyUSB0

[INFO] 验证设置...
[SUCCESS] 用户 chenzhouyi 已在 dialout 组中
[SUCCESS] udev规则文件存在
[SUCCESS] 设置验证完成

========================================
[SUCCESS] 串口权限设置完成！
========================================

[WARNING] 重要提醒:
1. 用户 chenzhouyi 需要重新登录才能使组权限生效
2. 如果设备已连接，请重新插拔一次以应用新规则
3. 现在可以不使用sudo运行夹爪程序了

[INFO] 测试命令: python3 main.py
```

### 完成后的操作

1. **重新登录**：用户需要重新登录以使组权限生效
2. **重新插拔设备**：如果串口设备已连接，请重新插拔一次
3. **测试程序**：现在可以直接运行 `python3 main.py` 而无需sudo

### 故障排除

#### 权限仍然被拒绝

1. 确保用户已重新登录
2. 检查设备是否重新插拔
3. 运行检查命令：`sudo ./scripts/setup_serial_permissions.sh --check`

#### 找不到设备

1. 确保设备已正确连接
2. 检查设备是否被系统识别：`lsusb`
3. 查看内核消息：`dmesg | tail`

#### 规则不生效

1. 手动重载规则：
   ```bash
   sudo udevadm control --reload-rules
   sudo udevadm trigger
   ```
2. 重启系统（最后手段）

### 文件位置

- **udev规则文件**：`/etc/udev/rules.d/50-gripper-serial.rules`
- **备份文件**：`/etc/udev/rules.d/50-gripper-serial.rules.backup.YYYYMMDD_HHMMSS`

### 卸载

如果需要移除设置：

```bash
# 删除udev规则文件
sudo rm /etc/udev/rules.d/50-gripper-serial.rules

# 从dialout组移除用户（可选）
sudo gpasswd -d username dialout

# 重新加载规则
sudo udevadm control --reload-rules
sudo udevadm trigger
```
