#!/usr/bin/env python3
"""
夹爪控制演示程序
使用抽象基类和控制器架构，支持CAN和RS485两种通信方式的夹爪控制
"""

import time
import os
import logging
from src.gripper_controller import (
    create_can_gripper, 
    create_rs485_gripper, 
    create_gripper,
    MultiGripperController
)
from src.gripper_base import GripperType
from common.logger_config import get_logger

# 配置日志
logger = get_logger(
    name='main_demo',
    log_dir=os.path.join(os.getcwd(), "logs/main"),
    log_level=logging.DEBUG,
    console_level=logging.INFO,
    file_level=logging.DEBUG
)


def check_permissions():
    """检查运行权限"""
    # if os.geteuid() != 0:
    #     logger.error("❌ 夹爪控制需要root权限才能访问设备")
    #     logger.info("请使用以下命令重新运行:")
    #     logger.info("sudo python3 main.py")
    #     return False
    return True


def demo_can_gripper():
    """CAN夹爪演示"""
    logger.info("=" * 50)
    logger.info("CAN夹爪演示开始")
    logger.info("=" * 50)

    try:
        # 创建CAN夹爪控制器
        logger.info("创建CAN夹爪控制器")
        gripper_controller = create_can_gripper(node_id=1, channel=0)

        # 设置状态回调
        # def status_callback(status):
        #     logger.info(f"[CAN状态] 位置=0x{status['position']:02X}, "
        #                f"状态={status.get('state_description', 'N/A')}")

        # gripper_controller.set_status_callback(status_callback)

        # 基本控制演示
        logger.info("开始CAN夹爪基本控制演示")

        # 测试打开夹爪
        logger.info("🔓 打开CAN夹爪...")
        status = gripper_controller.open_gripper()
        if status:
            logger.info(f"✅ 打开成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 打开失败")

        time.sleep(2)

        # 测试关闭夹爪
        logger.info("🔒 闭合CAN夹爪...")
        status = gripper_controller.close_gripper()
        if status:
            logger.info(f"✅ 关闭成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 关闭失败")
        time.sleep(2)

        # 测试中间位置
        logger.info("📍 移动到中间位置...")
        status = gripper_controller.move_to_position(0x80)  # 中间位置
        if status:
            logger.info(f"✅ 移动成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 移动失败")
        time.sleep(2)

        # 精确位置控制演示
        logger.info("开始精确位置控制演示")
        positions = [0x20, 0x40, 0x60, 0x80, 0xA0, 0xC0, 0xE0]
        
        for pos in positions:
            logger.info(f"📍 移动到位置 0x{pos:02X}...")
            status = gripper_controller.move_to_position(pos)
            if status:
                actual_pos = status.get('position', 0)
                logger.info(f"✅ 到达位置: 目标=0x{pos:02X}, 实际=0x{actual_pos:02X}")
            else:
                logger.error(f"❌ 移动到位置 0x{pos:02X} 失败")
            time.sleep(1)

        logger.info("✅ CAN夹爪演示完成")
        
                # 测试打开夹爪
        logger.info("🔓 打开CAN夹爪...")
        status = gripper_controller.open_gripper()
        if status:
            logger.info(f"✅ 打开成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 打开失败")
            
        logger.info("🔒 闭合CAN夹爪...")
        status = gripper_controller.close_gripper()
        if status:
            logger.info(f"✅ 关闭成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 关闭失败")

    except Exception as e:
        logger.error(f"CAN夹爪演示失败: {e}")
        logger.info("可能的原因:")
        logger.info("- 夹爪未连接或未上电")
        logger.info("- CAN设备驱动问题")
        logger.info("- 节点ID配置错误")
        logger.info("建议运行: sudo python3 debug/scan_can_bus.py 来检查设备")
    finally:
        try:
            gripper_controller.close()
            logger.info("CAN夹爪资源已释放")
        except:
            pass

    logger.info("CAN夹爪演示结束")
    logger.info("=" * 50)


def demo_rs485_gripper():
    """RS485夹爪演示"""
    logger.info("=" * 50)
    logger.info("RS485夹爪演示开始")
    logger.info("=" * 50)

    try:
        # 创建RS485夹爪控制器
        logger.info("创建RS485夹爪控制器")
        gripper_controller = create_rs485_gripper(port='/dev/ttyUSB1', node_id=1)

        # 设置状态回调
        # def status_callback(status):
        #     logger.info(f"[RS485状态] 位置=0x{status['position']:02X}, "
        #                f"状态={status.get('state_description', 'N/A')}")

        # gripper_controller.set_status_callback(status_callback)

        # 基本控制演示
        logger.info("开始RS485夹爪基本控制演示")

        # 测试打开夹爪
        logger.info("🔓 打开RS485夹爪...")
        status = gripper_controller.open_gripper()
        if status:
            logger.info(f"✅ 打开成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 打开失败")


        # 测试关闭夹爪
        logger.info("🔒 闭合RS485夹爪...")
        status = gripper_controller.close_gripper()
        position = gripper_controller.get_position()
        logger.info(f"当前夹爪位置: {position}")
        
        if status:
            logger.info(f"✅ 关闭成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 关闭失败")

        # 测试位置控制
        logger.info("📍 移动到中间位置...")
        status = gripper_controller.move_to_position(0x80)
        if status:
            logger.info(f"✅ 移动成功: {status.get('state_description', 'N/A')}")
        else:
            logger.error("❌ 移动失败")

        logger.info("✅ RS485夹爪演示完成")

    except Exception as e:
        logger.error(f"RS485夹爪演示失败: {e}")
        logger.info("可能的原因:")
        logger.info("- 串口设备未连接")
        logger.info("- 串口权限问题")
        logger.info("- 节点ID配置错误")
        logger.info("- 波特率不匹配")
    finally:
        try:
            gripper_controller.close()
            logger.info("RS485夹爪资源已释放")
        except:
            pass

    logger.info("RS485夹爪演示结束")
    logger.info("=" * 50)



def demo_multi_grippers():
    """多夹爪协同演示"""
    logger.info("=" * 50)
    logger.info("多夹爪协同演示开始")
    logger.info("=" * 50)

    try:
        # 创建多夹爪控制器
        multi_controller = MultiGripperController()

        # 添加CAN夹爪
        logger.info("添加CAN夹爪...")
        # can_gripper = create_can_gripper(node_id=1, channel=0)
        # multi_controller.add_gripper("can_gripper", can_gripper.gripper)

        # 添加RS485夹爪（如果可用）
        try:
            logger.info("尝试添加RS485夹爪...")
            rs485_gripper_left = create_rs485_gripper(port='/dev/ttyUSB0', node_id=1)
            rs485_gripper_right = create_rs485_gripper(port='/dev/ttyUSB1', node_id=1)
            multi_controller.add_gripper("rs485_gripper_left", rs485_gripper_left.gripper)
            multi_controller.add_gripper("rs485_gripper_right", rs485_gripper_right.gripper)
            logger.info("✅ RS485夹爪添加成功")
        except Exception as e:
            logger.warning(f"RS485夹爪添加失败: {e}")

        # 连接所有夹爪
        logger.info("连接所有夹爪...")
        connect_results = multi_controller.connect_all()
        for name, result in connect_results.items():
            logger.info(f"夹爪 {name}: {'连接成功' if result else '连接失败'}")

        # 并行控制演示
        logger.info("开始并行控制演示...")
        
        # 同时打开所有夹爪
        commands = {}
        for name in multi_controller.list_grippers():
            commands[name] = lambda g: g.open_gripper()
        
        logger.info("同时打开所有夹爪...")
        results = multi_controller.execute_parallel(commands)
        for name, result in results.items():
            logger.info(f"夹爪 {name}: {'打开成功' if result else '打开失败'}")

        time.sleep(2)

        # 同时关闭所有夹爪
        commands = {}
        for name in multi_controller.list_grippers():
            commands[name] = lambda g: g.close_gripper()
        
        logger.info("同时关闭所有夹爪...")
        results = multi_controller.execute_parallel(commands)
        for name, result in results.items():
            logger.info(f"夹爪 {name}: {'关闭成功' if result else '关闭失败'}")

        # 同步位置控制
        logger.info("同步位置控制演示...")
        positions = {}
        for name in multi_controller.list_grippers():
            positions[name] = 0x80  # 中间位置

        success = multi_controller.synchronize_positions(positions, timeout=5.0)
        logger.info(f"同步位置控制: {'成功' if success else '失败'}")

        logger.info("✅ 多夹爪协同演示完成")

    except Exception as e:
        logger.error(f"多夹爪协同演示失败: {e}")
    finally:
        try:
            multi_controller.close_all()
            logger.info("所有夹爪资源已释放")
        except:
            pass

    logger.info("多夹爪协同演示结束")
    logger.info("=" * 50)


def main():
    """主演示函数"""
    logger.info("🤖 夹爪控制演示程序")
    logger.info("使用抽象基类和控制器架构，支持CAN和RS485夹爪")

    # 检查权限
    if not check_permissions():
        return

    try:
        logger.info("\n" + "="*60)
        logger.info("夹爪控制演示")
        logger.info("="*60)
        logger.info("1. CAN夹爪演示")
        logger.info("2. RS485夹爪演示")
        logger.info("3. 多夹爪协同演示")
        logger.info("4. 高频获取当前位置测试 (RS485)")
        logger.info("0. 退出")

        choice = input("\n请选择演示项目 (0-4): ").strip()

        if choice == "1":
            demo_can_gripper()
        elif choice == "2":
            demo_rs485_gripper()
        elif choice == "3":
            demo_multi_grippers()
        elif choice == "4":
            demo_high_frequency_position_test()
        elif choice == "0":
            logger.info("用户选择退出")
        else:
            logger.error(f"❌ 无效选择: {choice}")

    except KeyboardInterrupt:
        logger.warning("\n⚠️ 用户中断演示")
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
    finally:
        logger.info("🏁 演示程序结束")


if __name__ == '__main__':
    main()
