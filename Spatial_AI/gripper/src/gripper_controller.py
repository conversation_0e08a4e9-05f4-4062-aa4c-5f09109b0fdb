#!/usr/bin/env python3
"""
夹爪控制器类
用于管理多个夹爪，提供便捷的创建函数和统一的控制接口
"""

import os
import sys
import threading
import time
from typing import Dict, List, Optional, Any, Callable, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加父目录到Python路径以导入logger_config
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from common.logger_config import get_logger

from .gripper_base import GripperBase, GripperType


class GripperController:
    """
    夹爪控制器类
    用于管理单个或多个夹爪，提供统一的控制接口
    """
    
    def __init__(self, gripper: GripperBase, name: str = "gripper"):
        """
        初始化夹爪控制器
        :param gripper: 夹爪实例
        :param name: 夹爪名称
        """
        self.gripper = gripper
        self.name = name
        self.logger = get_logger(
            name=f'gripper_controller_{name}',
            log_dir=os.path.join(os.getcwd(), "logs/gripper_controller"),
            log_level=10,  # DEBUG
            console_level=20,  # INFO
            file_level=10   # DEBUG
        )
        
        self.logger.info(f"初始化夹爪控制器: {name}")
    
    def connect(self) -> bool:
        """连接夹爪"""
        self.logger.info(f"连接夹爪: {self.name}")
        return self.gripper.connect()
    
    def disconnect(self) -> bool:
        """断开夹爪连接"""
        self.logger.info(f"断开夹爪连接: {self.name}")
        return self.gripper.disconnect()
    
    def open_gripper(self, **kwargs) -> Optional[Dict[str, Any]]:
        """打开夹爪"""
        return self.gripper.open_gripper(**kwargs)
    
    def close_gripper(self, **kwargs) -> Optional[Dict[str, Any]]:
        """关闭夹爪"""
        return self.gripper.close_gripper(**kwargs)
    
    def move_to_position(self, position: int, **kwargs) -> Optional[Dict[str, Any]]:
        """移动到指定位置"""
        return self.gripper.move_to_position(position, **kwargs)
    
    def send_command(self, **kwargs) -> Optional[Dict[str, Any]]:
        """发送指令"""
        return self.gripper.send_command(**kwargs)
    
    def get_status(self, **kwargs) -> Optional[Dict[str, Any]]:
        """获取状态"""
        return self.gripper.get_status(**kwargs)
    
    def set_status_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """设置状态回调"""
        self.gripper.set_status_callback(callback)
    
    def is_moving(self) -> bool:
        """检查是否正在移动"""
        return self.gripper.is_moving()
    
    def has_fault(self) -> bool:
        """检查是否有故障"""
        return self.gripper.has_fault()
    
    def get_position(self) -> Optional[int]:
        """获取当前位置"""
        return self.gripper.get_position()
    
    def emergency_stop(self) -> bool:
        """紧急停止"""
        return self.gripper.emergency_stop()
    
    def close(self):
        """关闭夹爪"""
        self.gripper.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


class MultiGripperController:
    """
    多夹爪控制器类
    用于管理多个夹爪的协调控制
    """
    
    def __init__(self):
        """初始化多夹爪控制器"""
        self.grippers: Dict[str, GripperController] = {}
        self.logger = get_logger(
            name='multi_gripper_controller',
            log_dir=os.path.join(os.getcwd(), "logs/gripper_controller"),
            log_level=10,  # DEBUG
            console_level=20,  # INFO
            file_level=10   # DEBUG
        )
        
        self.logger.info("初始化多夹爪控制器")
    
    def add_gripper(self, name: str, gripper: GripperBase) -> GripperController:
        """
        添加夹爪
        :param name: 夹爪名称
        :param gripper: 夹爪实例
        :return: 夹爪控制器
        """
        controller = GripperController(gripper, name)
        self.grippers[name] = controller
        self.logger.info(f"添加夹爪: {name}")
        return controller
    
    def remove_gripper(self, name: str) -> bool:
        """
        移除夹爪
        :param name: 夹爪名称
        :return: 移除成功返回True
        """
        if name in self.grippers:
            self.grippers[name].close()
            del self.grippers[name]
            self.logger.info(f"移除夹爪: {name}")
            return True
        return False
    
    def get_gripper(self, name: str) -> Optional[GripperController]:
        """
        获取夹爪控制器
        :param name: 夹爪名称
        :return: 夹爪控制器或None
        """
        return self.grippers.get(name)
    
    def list_grippers(self) -> List[str]:
        """
        列出所有夹爪名称
        :return: 夹爪名称列表
        """
        return list(self.grippers.keys())
    
    def connect_all(self) -> Dict[str, bool]:
        """
        连接所有夹爪
        :return: 连接结果字典
        """
        results = {}
        for name, gripper in self.grippers.items():
            results[name] = gripper.connect()
            self.logger.info(f"连接夹爪 {name}: {'成功' if results[name] else '失败'}")
        return results
    
    def disconnect_all(self) -> Dict[str, bool]:
        """
        断开所有夹爪连接
        :return: 断开结果字典
        """
        results = {}
        for name, gripper in self.grippers.items():
            results[name] = gripper.disconnect()
            self.logger.info(f"断开夹爪 {name}: {'成功' if results[name] else '失败'}")
        return results
    
    def execute_parallel(self, commands: Dict[str, Callable]) -> Dict[str, Any]:
        """
        并行执行命令
        :param commands: 命令字典，键为夹爪名称，值为要执行的函数
        :return: 执行结果字典
        """
        results = {}
        
        with ThreadPoolExecutor(max_workers=len(commands)) as executor:
            # 提交所有任务
            future_to_name = {}
            for name, command in commands.items():
                if name in self.grippers:
                    future = executor.submit(command, self.grippers[name])
                    future_to_name[future] = name
                else:
                    results[name] = None
                    self.logger.warning(f"夹爪 {name} 不存在")
            
            # 收集结果
            for future in as_completed(future_to_name):
                name = future_to_name[future]
                try:
                    results[name] = future.result()
                    self.logger.info(f"夹爪 {name} 命令执行完成")
                except Exception as e:
                    results[name] = None
                    self.logger.error(f"夹爪 {name} 命令执行失败: {e}")
        
        return results
    
    def synchronize_positions(self, positions: Dict[str, int], 
                            timeout: float = 10.0) -> bool:
        """
        同步多个夹爪到指定位置
        :param positions: 位置字典，键为夹爪名称，值为目标位置
        :param timeout: 超时时间
        :return: 所有夹爪都到达目标位置返回True
        """
        self.logger.info(f"同步夹爪位置: {positions}")
        
        # 并行发送移动命令
        commands = {}
        for name, position in positions.items():
            commands[name] = lambda g, pos=position: g.move_to_position(pos, timeout=timeout)
        
        results = self.execute_parallel(commands)
        
        # 检查所有结果
        success = all(result is not None for result in results.values())
        self.logger.info(f"同步位置{'成功' if success else '失败'}")
        return success
    
    def emergency_stop_all(self) -> Dict[str, bool]:
        """
        紧急停止所有夹爪
        :return: 停止结果字典
        """
        self.logger.warning("紧急停止所有夹爪")
        results = {}
        for name, gripper in self.grippers.items():
            results[name] = gripper.emergency_stop()
        return results
    
    def close_all(self):
        """关闭所有夹爪"""
        self.logger.info("关闭所有夹爪")
        for gripper in self.grippers.values():
            gripper.close()
        self.grippers.clear()


# 便捷创建函数
def create_can_gripper(node_id: int = 1, channel: int = 0, **kwargs) -> GripperController:
    """
    创建CAN夹爪控制器
    :param node_id: 节点ID
    :param channel: CAN通道
    :param kwargs: 其他参数
    :return: 夹爪控制器
    """
    from .Gripper_can import GripperCAN
    gripper = GripperCAN(node_id=node_id, channel=channel, **kwargs)
    return GripperController(gripper, f"can_{node_id}")


def create_rs485_gripper(port: str, node_id: int = 1, 
                        baudrate: int = 115200, **kwargs) -> GripperController:
    """
    创建RS485夹爪控制器
    :param port: 串口名称
    :param node_id: 节点ID
    :param baudrate: 波特率
    :param kwargs: 其他参数
    :return: 夹爪控制器
    """
    from .Gripper_485 import GripperRS485
    gripper = GripperRS485(port=port, node_id=node_id, baudrate=baudrate, **kwargs)
    return GripperController(gripper, f"rs485_{node_id}")


def create_gripper(gripper_type: GripperType, **kwargs) -> GripperController:
    """
    通用夹爪创建函数
    :param gripper_type: 夹爪类型
    :param kwargs: 创建参数
    :return: 夹爪控制器
    """
    if gripper_type == GripperType.CAN:
        return create_can_gripper(**kwargs)
    elif gripper_type == GripperType.RS485:
        return create_rs485_gripper(**kwargs)
    else:
        raise ValueError(f"不支持的夹爪类型: {gripper_type}")
