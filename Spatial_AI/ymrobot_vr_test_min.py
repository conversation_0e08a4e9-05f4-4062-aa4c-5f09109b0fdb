#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Po<PERSON>, Point, Quaternion
from ymrobot import Y<PERSON><PERSON>bot
from sensor_msgs.msg import JointState
import math
import time
import numpy as np

if __name__ == '__main__':
    rclpy.init()

    robot = YMrobot()
    time.sleep(2.0)  # Wait for action to complete

    try:
        #  robot.move_gripper_to_position("left", 0)
        #  time.sleep(0.1)
        #  robot.move_gripper_to_position("left", 100)
        #  time.sleep(0.1)
        #  robot.move_gripper_to_position("left", 0)
        #  time.sleep(0.1)
        #  robot.move_gripper_to_position("left", 100)
        #  time.sleep(0.1)


        robot.control_right_arm_joint_position_ros2controller([0.3, 0.0, 0.5, 0.0, 0.0, 0.0, 0.0])

    except KeyboardInterrupt:
        pass
    finally:
        robot.shutdown()
        rclpy.shutdown()