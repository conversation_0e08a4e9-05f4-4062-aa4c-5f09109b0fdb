#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Pose, Point, Quaternion
from ymrobot import YMrobot
from sensor_msgs.msg import JointState
import socket
import struct
import time
import math
import numpy as np
from datetime import datetime
import threading
from collections import deque

class UDPRobotController:
    def __init__(self, host='0.0.0.0', port=8080):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.robot = None
        self.rclpy_initialized = False
        self.test_alternative_index = True
        # 添加夹爪状态跟踪
        self.gripper_closed = False  # 跟踪夹爪是否已关闭
        
    def initialize_robot(self):
        try:
            if not self.rclpy_initialized:
                rclpy.init()
                self.rclpy_initialized = True
            
            self.robot = YMrobot()
            time.sleep(2.0)
            print("机器人初始化成功")
            return True
        except Exception as e:
            print(f"机器人初始化失败: {e}")
            return False
    
    def shutdown_robot(self):
        if self.robot:
            self.robot.shutdown()
        if self.rclpy_initialized:
            rclpy.shutdown()
        print("机器人已关闭")
    
    def start_udp(self):
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.host, self.port))
            self.socket.settimeout(1.0)
            self.running = True
            print(f"UDP接收器已启动，监听地址: {self.host}:{self.port}")
            print("等待数据...")
            return True
        except Exception as e:
            print(f"启动UDP接收器失败: {e}")
            return False
    
    def stop_udp(self):
        self.running = False
        if self.socket:
            self.socket.close()
            print("UDP接收器已停止")
    
    def parse_data_packet(self, data):
        try:
            if len(data) < 85:
                return None
            
            if data[0] != 0xAA or data[1] != 0xBB:
                return None
            
            data_length = data[2]
            if data_length != 16:
                return None
            
            torque_raw = struct.unpack('<h', data[3:5])[0]
            torque_value = torque_raw / 10.0
            
            angles = []
            offset = 5
            for i in range(16):
                angle_bytes = data[offset + i*4:offset + (i+1)*4]
                angle_value = struct.unpack('<f', angle_bytes)[0]
                angles.append(angle_value)
            
            return {
                'torque': torque_value,
                'angles': angles,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            return None
    
    def control_right_arm(self, angles):
        if not self.robot:
            print("机器人未初始化")
            return
        
        try:
            right_angles = angles[8:16]
            
            print(f"UDP原始角度数据: {right_angles}")
            print(f"关节1原始角度: {right_angles[0]:.1f}°")
            
            joint1_rad = math.radians(right_angles[0])
            joint2_rad = math.radians(right_angles[1])
            joint3_rad = math.radians(right_angles[2])
            joint4_rad = math.radians(right_angles[3])
            joint5_rad = math.radians(right_angles[4])
            joint6_rad = math.radians(right_angles[5])
            joint7_rad = math.radians(right_angles[6])

            control_values = [joint1_rad, joint2_rad, joint3_rad, joint4_rad, joint5_rad, joint6_rad, joint7_rad]
            
            result = self.robot.control_right_arm_joint_position_ros2controller(control_values)
            print(f"控制命令发送结果: {result}")
            
            print(f"控制右臂: [{control_values[0]:.3f}, {control_values[1]:.3f}, {control_values[2]:.3f}, {control_values[3]:.3f}, {control_values[4]:.3f}, {control_values[5]:.3f}, {control_values[6]:.3f}]")
            
        except Exception as e:
            print(f"控制右臂时出错: {e}")
    
    def control_gripper(self, gripper_value):
        if not self.robot:
            print("机器人未初始化")
            return
        try:
            gripper_cmd = int(255-(gripper_value / 100 * 255))
            self.robot.move_gripper_to_position("left", gripper_cmd)
            # try:
            #     print(f"夹爪控制调试 - 当前值: {gripper_value:.1f}, 当前状态: {'关闭' if self.gripper_closed else '打开'}")
                
            #     # 根据夹爪值判断应该执行的操作
            #     if gripper_value > 90:
            #         # 夹爪值大于90，应该关闭夹爪
            #         print(f"夹爪值 {gripper_value:.1f} > 90，需要关闭夹爪")
            #         if not self.gripper_closed:
            #             print("执行关闭夹爪操作...")
            #             self.robot.close_gripper("left")
            #             self.gripper_closed = True
            #             print(f"夹爪关闭 - 触发值: {gripper_value:.1f}")
            #         else:
            #             print("夹爪已经关闭，无需重复操作")
            #     else:
            #         # 夹爪值小于等于90，应该打开夹爪
            #         print(f"夹爪值 {gripper_value:.1f} <= 90，需要打开夹爪")
            #         if self.gripper_closed:
            #             print("执行打开夹爪操作...")
            #             self.robot.open_gripper("left")
            #             self.gripper_closed = False
            #             print(f"夹爪打开 - 触发值: {gripper_value:.1f}")
            #         else:
            #             print("夹爪已经打开，无需重复操作")
                
        except Exception as e:
            print(f"控制夹爪时出错: {e}")
    
    def run(self):
        if not self.initialize_robot():
            return
        
        if not self.start_udp():
            return
        
        packet_count = 0
        start_time = time.time()
        
        try:
            while self.running:
                try:
                    data, addr = self.socket.recvfrom(1024)
                    packet_count += 1
                    
                    parsed_data = self.parse_data_packet(data)
                    
                    if parsed_data:
                        print(f"解析的UDP数据 - 扭矩: {parsed_data['torque']:.1f}, 角度数组长度: {len(parsed_data['angles'])}")
                        print(f"所有角度数据: {parsed_data['angles']}")
                        
                        self.control_right_arm(parsed_data['angles'])
                        
                        gripper_value = parsed_data['angles'][15]
                        print(f"夹爪值: {gripper_value:.1f}")
                        
                        # 调用夹爪控制方法
                        self.control_gripper(gripper_value)
                        
                        elapsed_time = time.time() - start_time
                        if elapsed_time >= 1.0:
                            fps = packet_count / elapsed_time
                            print(f"FPS: {fps:.1f}")
                            packet_count = 0
                            start_time = time.time()
                        
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"接收数据时出错: {e}")
                    
        except KeyboardInterrupt:
            print("\n用户中断，正在停止...")
        finally:
            self.stop_udp()
            self.shutdown_robot()

def main():
    print("UDP机器人控制器")
    print("根据UDP接收的角度数据控制机器人右臂和夹爪")
    print("按 Ctrl+C 停止程序")
    print()
    
    controller = UDPRobotController(host='0.0.0.0', port=8080)
    
    try:
        controller.run()
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
