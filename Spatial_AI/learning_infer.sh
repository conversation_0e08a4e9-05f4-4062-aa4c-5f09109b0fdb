#!/bin/bash
# Save as start_data_collection.sh
# Usage: chmod +x start_data_collection.sh && ./start_data_collection.sh

SESSION="learning_infer"

# Start a new tmux session
tmux new-session -d -s $SESSION

# Window 1: udp2joint
tmux send-keys -t $SESSION "ros2 launch udp2joint run_learning.launch.py" C-m
sleep 1

# Window 2: Orbbec Gemini2L camera
tmux new-window -t $SESSION -n "gemini2L"
tmux send-keys -t $SESSION:1 "ros2 launch orbbec_camera gemini2L.launch.py" C-m
sleep 1

# Window 3: Realsense multi-camera
tmux new-window -t $SESSION -n "realsense"
tmux send-keys -t $SESSION:2 "ros2 launch realsense2_camera rs_multi_camera_launch.py" C-m
sleep 1

# Window 4: Python script in Spatial_AI folder
tmux new-window -t $SESSION -n "gripper"
tmux send-keys -t $SESSION:3 "cd /home/<USER>/Spatial_AI && python3 ymrobot_zy_gripper.py" C-m

# Attach to tmux
tmux attach-session -t $SESSION
