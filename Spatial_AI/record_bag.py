from pynput import keyboard
import subprocess
import os

# ==== 配置 ====
TOPICS = ["/camera/color/image_raw", "/camera1/camera1/color/image_raw", "/camera2/camera2/color/image_raw",
          "/left_arm_position_controller/commands", "/right_arm_position_controller/commands", 
          "/left_gripper_position", "/right_gripper_position", "/gripper_commands", "/gripper_states",
          "/joint_states"]   # 你要录制的 topic 列表
OUTPUT_DIR = "/home/<USER>/Spatial_AI/record20250827"  # 保存路径

# ==== 状态变量 ====
recording = False
process = None
counter = 1

# ==== 获取下一个 bag 文件名 ====
def get_next_bag_name():
    global counter
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    # 自动递增
    bag_name = os.path.join(OUTPUT_DIR, f"bag_{counter:04d}")
    counter += 1
    return bag_name

# ==== 开始录制 ====
def start_recording():
    global process
    bag_path = get_next_bag_name()
    topics_str = " ".join(TOPICS)
    # 用 bash -i -c 确保加载 ROS 2 环境
    cmd = f"ros2 bag record -o {bag_path} {topics_str}"
    process = subprocess.Popen(cmd, shell=True)
    print(f"开始录制: {bag_path}")

# ==== 停止录制 ====
def stop_recording():
    global process
    if process:
        process.terminate()
        process.wait()
        print("停止录制")
        process = None

# ==== 键盘监听回调 ====
def on_press(key):
    global recording
    try:
        if key == keyboard.Key.space:  # 空格切换
            recording = not recording
            if recording:
                start_recording()
            else:
                stop_recording()
        elif key.char == 'd':           # d 键退出
            print("退出程序")
            if recording:
                stop_recording()
            return False
    except AttributeError:
        pass  # 特殊键忽略

# ==== 主程序 ====
if __name__ == "__main__":
    print("按空格键开始/停止录制，按 'd' 退出程序。")
    with keyboard.Listener(on_press=on_press) as listener:
        listener.join()
