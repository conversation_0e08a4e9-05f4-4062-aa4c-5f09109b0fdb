#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import <PERSON><PERSON>, Point, Quaternion
from ymrobot import YMrobot
from sensor_msgs.msg import JointState
import math
import time
import numpy as np


key_frames = [
            [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],  # 初始位置
            [0.5, 0.0, 0.0, 1.0, 0.0, 0.5, 0.0],  # 抬臂
            [0.5, 0.5, 0.0, 1.0, 0.0, 0.5, 0.8],  # 挥动1
            [0.5, 0.2, 0.0, 1.0, 0.0, 0.5, 0.4],  # 过渡
            [0.5, 0.0, 0.0, 1.0, 0.0, 0.5, 0.0],  # 回中
            [0.5, -0.5, 0.0, 1.0, 0.0, 0.5, -0.8],  # 挥动2
            [0.5, -0.2, 0.0, 1.0, 0.0, 0.5, -0.4],  # 过渡
            [0.5, 0.0, 0.0, 1.0, 0.0, 0.5, 0.0],  # 回中
            [0.5, 0.5, 0.0, 1.0, 0.0, 0.5, 0.8],  # 挥动3
            [0.5, 0.2, 0.0, 1.0, 0.0, 0.5, 0.4],  # 过渡
            [0.5, 0.0, 0.0, 1.0, 0.0, 0.5, 0.0],  # 回中
            [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]  # 回到初始
        ]

def interpolate_frames(frames, total_steps):
    interpolated = []
    num_segments = len(frames) - 1
    steps_per_segment = total_steps // num_segments

    for i in range(num_segments):
        start = np.array(frames[i])
        end = np.array(frames[i + 1])
        for t in range(steps_per_segment):
            ratio = t / steps_per_segment
            pose = (1 - ratio) * start + ratio * end
            interpolated.append(pose.tolist())

    # 补足因整除丢失的帧
    while len(interpolated) < total_steps:
        interpolated.append(frames[-1])
    return interpolated

if __name__ == '__main__':
    rclpy.init()

    robot = YMrobot()
    time.sleep(2.0)  # Wait for action to complete

    total_time = 3.0
    frequency = 100  # Hz
    total_steps = int(total_time * frequency)

    interpolated_poses = interpolate_frames(key_frames, total_steps)
    print(f"Interpolated {len(interpolated_poses)} poses...")

    try:
        # rate = robot.create_rate(50.0)  # 100 Hz
        # start_time = time.time()
        # for i, pose in enumerate(interpolated_poses):
        #     robot.control_right_arm_joint_position_ros2controller(pose)
        #     rate.sleep()
        #     # time.sleep(1.0 / frequency)
        # print(f"Execution done in {time.time() - start_time:.2f}s")

        neck_init = [0 , math.radians(32)]
        robot.control_neck_joint_position_ros2controller(neck_init)


        body_init= [math.radians(12), math.radians(70), math.radians(-42), 0]
        robot.control_body_joint_position_ros2controller(body_init)


        #  Example: Control gripper
        # robot.close_gripper("left")
        # robot.close_gripper("right")
        # time.sleep(1.0)
        # robot.open_gripper("left")
        # robot.open_gripper("right")



        # Example: Control right arm joints
        # right_arm_joints = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        # robot.control_right_arm_joint_position(right_arm_joints)

        # # Example: Control both arms' end-effectors
        # left_pose = Pose(position=Point(x=0.40, y=0.28, z=0.85), orientation=Quaternion(x=-0.0343, y=0.1710, z=-0.0670, w=0.9819)) # 末端斜向下 20°，偏右 15°
        # right_pose = Pose(position=Point(x=0.32, y=-0.28, z=0.83), orientation=Quaternion(x=0.0, y=0.2588, z=0.0, w=0.9659))
        # self.robot.control_both_arms_end_effector(left_pose, right_pose)

        # # Example: Get joint states
        # joint_states = robot.get_full_joint_state()
        # print("joint_states",joint_states)

        # # Example: Get head RGB
        # head_rgb, head_rgb_stamp = robot.get_head_rgb()
        # if head_rgb is not None:
        #     print(f"Head RGB shape: {head_rgb.shape}")
        # print(f"Head RGB timestamp: {head_rgb_stamp}")

        # # Example: Reset
        # self.robot.reset()
    except KeyboardInterrupt:
        pass
    finally:
        robot.shutdown()
        rclpy.shutdown()