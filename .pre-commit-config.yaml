# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

default_language_version:
    python: python3.10

exclude: "tests/artifacts/.*\\.safetensors$"

repos:
  ##### Meta #####
  - repo: meta
    hooks:
      - id: check-useless-excludes
      - id: check-hooks-apply

   ##### General Code Quality & Formatting #####
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
        args: ['--maxkb=1024']
      - id: debug-statements
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-yaml
      - id: check-toml
      - id: end-of-file-fixer
      - id: trailing-whitespace

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      - id: ruff-format
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  - repo: https://github.com/adhtruong/mirrors-typos
    rev: v1.34.0
    hooks:
      - id: typos
        args: [--force-exclude]

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
    -   id: pyupgrade
        args: [--py310-plus]

  ##### Markdown Quality #####
  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.6.2
    hooks:
      - id: prettier
        name: Format Markdown with Prettier
        types_or: [markdown, mdx]
        args: [--prose-wrap=preserve]

  ##### Security #####
  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.27.2
    hooks:
      - id: gitleaks

  - repo: https://github.com/woodruffw/zizmor-pre-commit
    rev: v1.11.0
    hooks:
      - id: zizmor

  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.6
    hooks:
    - id: bandit
      args: ["-c", "pyproject.toml"]
      additional_dependencies: ["bandit[toml]"]

  # TODO(Steven): Uncomment when ready to use
  ##### Static Analysis & Typing #####
  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.16.0
  #   hooks:
  #     - id: mypy
  #       args: [--python-version=3.10]

  ##### Docstring Checks #####
  # - repo: https://github.com/akaihola/darglint2
  #   rev: v1.8.2
  #   hooks:
  #     - id: darglint2
  #       args: ["--docstring-style", "google", "-v", "2"]
  #       exclude: ^tests/.*$

  # - repo: https://github.com/econchick/interrogate
  #   rev: 1.7.0
  #   hooks:
  #     - id: interrogate
  #       args: ["-vv", "--config=pyproject.toml"]
