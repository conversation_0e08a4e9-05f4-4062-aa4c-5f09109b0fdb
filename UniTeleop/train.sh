# python lerobot/scripts/train.py \
#     --policy.type=act \
#     --policy.device=cuda \
#     --batch_size=16 \
#     --num_workers=8 \
#     --save_freq=1000 \
#     --eval_freq=0 \
#     --dataset.repo_id=Pi-robot/a2d_test \
#     --output_dir=outputs/train/a2d_test \
#     --job_name=a2d_grasp_bottle \
#     --wandb.enable=true \


# python lerobot/scripts/train.py \
#     --policy.type=act \
#     --policy.device=cuda \
#     --batch_size=2 \
#     --num_workers=8 \
#     --save_freq=1000 \
#     --eval_freq=0 \
#     --dataset.repo_id=Pi-robot/place_blue_bottle_long_task \
#     --output_dir=outputs/train/place_blue_bottle_long_task \
#     --job_name=place_blue_bottle_long_task \
#     --wandb.enable=true \
## act Dinov2
# python lerobot/scripts/train.py \
#     --batch_size=64 \
#     --num_workers=8 \
#     --steps=200000 \
#     --save_freq=2000 \
#     --eval_freq=0 \
#     --policy.type=act \
#     --policy.action_shift=1 \
#     --dataset.root=/mnt/data/dataset/Pi-robot/place_blue_bottle_split \
#     --dataset.repo_id=Pi-robot/place_blue_bottle_split \
#     --dataset.video_backend=pyav \
#     --env.type=aloha \
#     --output_dir=/mnt/data/model_weights/act/place_blue_bottle_split_dinov2_vitl14 \
#     --wandb.enable=true \
## dp resnet18
# python lerobot/scripts/train.py \
#     --batch_size=256 \
#     --num_workers=8 \
#     --steps=200000 \
#     --save_freq=2000 \
#     --eval_freq=0 \
#     --policy.type=diffusion \
#     --dataset.root=/mnt/data/dataset/Pi-robot/place_blue_bottle_split \
#     --dataset.repo_id=Pi-robot/place_blue_bottle_split \
#     --dataset.video_backend=pyav \
#     --env.type=aloha \
#     --output_dir=/mnt/data/model_weights/diffusion/place_blue_bottle_split \
#     --wandb.enable=true \

python lerobot/scripts/train.py --config train_config/mae_act_dino_l14_place_bule_bottle.json