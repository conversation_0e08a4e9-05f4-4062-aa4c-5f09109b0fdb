// fmt: off
// flake8: noqa
// !/usr/bin/env python

//  Copyright 2024 The HuggingFace Inc. team.
//  All rights reserved.

//  Licensed under the Apache License, Version 2.0 (the "License");
//  you may not use this file except in compliance with the License.
//  You may obtain a copy of the License at

//      http://www.apache.org/licenses/LICENSE-2.0

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  See the License for the specific language governing permissions and
//  limitations under the License.
syntax = "proto3";

package async_inference;

// AsyncInference: from Robot perspective
// Robot send observations to & executes action received from a remote Policy server
service AsyncInference {
  // Robot -> Policy to share observations with a remote inference server
  // Policy -> Robot to share actions predicted for given observations
  rpc SendObservations(stream Observation) returns (Empty);
  rpc StreamActions(Empty) returns (stream Action);
  rpc SendPolicyInstructions(PolicySetup) returns (Empty);
  rpc Ready(Empty) returns (Empty);
}

enum TransferState {
    TRANSFER_UNKNOWN = 0;
    TRANSFER_BEGIN = 1;
    TRANSFER_MIDDLE = 2;
    TRANSFER_END = 3;
}

// Messages
message Observation {
  // sent by Robot, to remote Policy
  TransferState transfer_state = 1;
  bytes data = 2;
}

message Action {
  // sent by remote Policy, to Robot
  TransferState transfer_state = 1;
  bytes data = 2;
}

message PolicySetup {
  // sent by Robot to remote server, to init Policy
  TransferState transfer_state = 1;
  bytes data = 2;
}

message Empty {}