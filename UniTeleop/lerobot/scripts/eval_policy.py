
import logging
import time
from dataclasses import asdict
from pprint import pformat

import torch
import numpy as np
from torchvision.transforms import v2

from lerobot.configs import parser
from lerobot.common.robot_devices.control_configs import (
    ControlPipelineConfig,
        OfflineEvalConfig,
        OnlineEvalConfig
)
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
from lerobot.common.policies.factory import make_policy, make_policy_config, get_policy_class
from lerobot.common.utils.plot_utils import calc_mse_for_single_trajectory
from lerobot.common.robot_devices.robots.utils import Robot, make_robot_from_config
from lerobot.common.robot_devices.utils import busy_wait, safe_disconnect
from lerobot.common.utils.utils import has_method, init_logging, log_say
from lerobot.common.policies.serve import ExternalRobotInferenceClient

# import debugpy; debugpy.connect(('127.0.0.1', 5678))

def preprocess_observation(observation, device):

    image_transforms = v2.Compose([
                    v2.Resize((16 * 14, 22 * 14)),
                ])
    for name in observation:
        if "image" in name:
            observation[name] = image_transforms(observation[name])
            observation[name] = observation[name].type(torch.float32) / 255
            observation[name] = observation[name].permute(2, 0, 1).contiguous()
        observation[name] = observation[name].unsqueeze(0)
        observation[name] = observation[name].to(device)
    return observation

def offline_eval(cfg: OfflineEvalConfig):
    dataset = LeRobotDataset(cfg.repo_id, root=cfg.root, episodes=cfg.episode_index)
    # Load pretrained policy
    policy = None if cfg.policy is None else make_policy(cfg.policy, ds_meta=dataset.meta)
    mse = calc_mse_for_single_trajectory(policy, dataset, cfg.modality_keys, 
                                         episode_index=cfg.episode_index, steps=cfg.steps)
    logging.info(f"MSE: {mse}")


@safe_disconnect
def online_eval(robot: Robot, cfg: OnlineEvalConfig):
    inference_time_s = 60
    fps = cfg.fps
    ACTION_HORIZON = 32
    ds_meta = None
    # dataset = LeRobotDataset(cfg.repo_id, root=cfg.root)
    # ds_meta = dataset.meta
    policy = None if cfg.policy is None else make_policy(cfg.policy, ds_meta=ds_meta)
    robot.reset_arm()
    time.sleep(1)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    # exit()

    for _ in range(inference_time_s * fps):

        # Read the follower state and access the frames from the cameras
        observation = robot.capture_observation()
        if "serve" not in cfg.policy.type:
            start_time = time.perf_counter()
            observation = preprocess_observation(observation, device)
            observation = {
            key: observation[key].to(device, non_blocking=device.type == "cuda") for key in observation
            }
            with torch.inference_mode():
                action_hat = policy.select_action(observation)
            
             # Remove batch dimension
            action = action_hat.squeeze(0).to("cpu").numpy()
            # robot.send_action(action)
            dt_s = time.perf_counter() - start_time
            busy_wait(1 / fps - dt_s)
            
        else:
            action_hat = policy.select_action(observation)

            action_diff_len = action_hat.shape[0] - ACTION_HORIZON
            # use mid frame
            for i in range(min(action_hat.shape[0], ACTION_HORIZON)):
                start_time = time.perf_counter()
                action = action_hat[i]
                assert action.shape == (16,), action.shape
                if action[14] >= 0.35: action[14] = 1.0
                if action[15] >= 0.3: action[15] = 1.0
                robot.send_action(action)
                # print(action)
                dt_s = time.perf_counter() - start_time
                busy_wait(1 / fps - dt_s)

    robot.reset_arm()


@parser.wrap()
def eval(cfg: ControlPipelineConfig):
    init_logging()
    logging.info(pformat(asdict(cfg)))
    if isinstance(cfg.control, OfflineEvalConfig):
        offline_eval(cfg.control)
    elif isinstance(cfg.control, OnlineEvalConfig):
        robot = make_robot_from_config(cfg.robot)
        if not robot.is_connected:
            robot.connect()
        online_eval(robot, cfg.control)
    else:
        raise ValueError(f"Unknown control config: {cfg.control}")


if __name__ == "__main__":
    eval()