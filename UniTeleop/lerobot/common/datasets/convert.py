import torch

def agilex_convert_data_to_single_arm(data: dict, is_right_arm: bool=True):
    if is_right_arm:
        data["observation.state"] = data["observation.state"][7:]
        data["action"] = data["action"][:, 7:]
    else:
        data["observation.state"] = data["observation.state"][:7]
        data["action"] = data["action"][:, :7]
    return data

def make_convert_from_config(convert_type: str | None):
    if convert_type == "agilex_single_arm":
        return agilex_convert_data_to_single_arm
    else:
        return None