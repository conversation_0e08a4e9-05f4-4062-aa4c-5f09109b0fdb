#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import collections
from dataclasses import dataclass, field
from typing import Any, Callable, Sequence

import torch
from torchvision.transforms import v2
from torchvision.transforms.v2 import Transform
from torchvision.transforms.v2 import functional as F  # noqa: N812


class MAEMaskTransform(Transform):
    """Masked Autoencoder (MAE) style random masking for images.
    
    Randomly masks a specified percentage of image patches by setting them to zero.
    This forces models to learn robust visual representations that are more adaptable
    to occlusions and viewpoint changes.
    
    Args:
        mask_ratio: Percentage of patches to mask (0.0-1.0). Default is 0.5 (50% masking).
        patch_size: Size of the patches to divide the image into. Default is 16.
        fill_value: Value to fill masked patches with. Default is 0.
        mask_strategy: Strategy for masking. Currently only "random" is supported.
    """
    
    def __init__(
        self, 
        mask_ratio: float = 0.5, 
        patch_size: int = 16,
        fill_value: float = 0.0,
        mask_strategy: str = "random"
    ) -> None:
        super().__init__()
        if mask_ratio < 0.0 or mask_ratio > 1.0:
            raise ValueError(f"mask_ratio must be between 0.0 and 1.0, got {mask_ratio}")
        
        self.mask_ratio = mask_ratio
        self.patch_size = patch_size
        self.fill_value = fill_value
        self.mask_strategy = mask_strategy
        
    def _create_mask(self, B: int, H: int, W: int, device: torch.device) -> torch.Tensor:
        """Create a random binary mask of shape (B, H, W)."""
        # Calculate number of patches
        num_patches_h = H // self.patch_size
        num_patches_w = W // self.patch_size
        num_patches = num_patches_h * num_patches_w
        
        # Create a mask for each image in the batch (1=keep, 0=mask)
        mask = torch.ones((B, num_patches_h, num_patches_w), device=device)
        
        # Calculate number of patches to mask
        num_masked = int(self.mask_ratio * num_patches)
        
        # For each image in the batch, randomly choose patches to mask
        for i in range(B):
            # Get random indices of patches to mask
            indices = torch.randperm(num_patches, device=device)[:num_masked]
            # Convert flat indices to 2D coordinates
            mask_h = indices // num_patches_w
            mask_w = indices % num_patches_w
            # Set mask values to 0 for masked patches
            mask[i, mask_h, mask_w] = 0
            
        # Reshape mask to match image patches
        # First, reshape to (B, num_patches_h, 1, num_patches_w, 1)
        mask = mask.reshape(B, num_patches_h, 1, num_patches_w, 1)
        # Then, repeat to match patch size in both dimensions
        mask = mask.repeat(1, 1, self.patch_size, 1, self.patch_size)
        # Finally, reshape to (B, H, W)
        mask = mask.reshape(B, num_patches_h * self.patch_size, num_patches_w * self.patch_size)
        
        return mask
        
    def forward(self, img: torch.Tensor) -> torch.Tensor:
        """
        Args:
            img: Input tensor of shape (B, C, H, W) or (C, H, W)
            
        Returns:
            Masked image tensor with the same shape as input
        """
        if not isinstance(img, torch.Tensor):
            raise TypeError(f"img should be a Tensor, got {type(img)}")
            
        # 保存输入图像的原始数据类型
        original_dtype = img.dtype
            
        # Handle both batched and non-batched inputs
        if img.ndim == 3:
            # Add batch dimension
            img = img.unsqueeze(0)
            batched = False
        else:
            batched = True
            
        B, C, H, W = img.shape
        
        # Check if dimensions are divisible by patch_size
        if H % self.patch_size != 0 or W % self.patch_size != 0:
            error_msg = f"Image dimensions ({H}, {W}) must be divisible by patch_size {self.patch_size}. "
            error_msg += f"Please ensure images are resized to dimensions that are multiples of {self.patch_size} "
            error_msg += "before applying MAE masking. Consider adding a Resize transform before MAEMask."
            raise ValueError(error_msg)
        
        # Create mask
        mask = self._create_mask(B, H, W, img.device)
        
        # Expand mask to match channels
        mask = mask.unsqueeze(1).expand(-1, C, -1, -1)
        
        # Apply mask
        masked_img = img * mask + self.fill_value * (1 - mask)
        
        # Remove batch dimension if input was not batched
        if not batched:
            masked_img = masked_img.squeeze(0)
        
        # 确保输出与输入的数据类型一致
        masked_img = masked_img.to(dtype=original_dtype)
            
        return masked_img
    
    def extra_repr(self) -> str:
        return f"mask_ratio={self.mask_ratio}, patch_size={self.patch_size}, fill_value={self.fill_value}"


class RandomSubsetApply(Transform):
    """Apply a random subset of N transformations from a list of transformations.

    Args:
        transforms: list of transformations.
        p: represents the multinomial probabilities (with no replacement) used for sampling the transform.
            If the sum of the weights is not 1, they will be normalized. If ``None`` (default), all transforms
            have the same probability.
        n_subset: number of transformations to apply. If ``None``, all transforms are applied.
            Must be in [1, len(transforms)].
        random_order: apply transformations in a random order.
    """

    def __init__(
        self,
        transforms: Sequence[Callable],
        p: list[float] | None = None,
        n_subset: int | None = None,
        random_order: bool = False,
    ) -> None:
        super().__init__()
        if not isinstance(transforms, Sequence):
            raise TypeError("Argument transforms should be a sequence of callables")
        if p is None:
            p = [1] * len(transforms)
        elif len(p) != len(transforms):
            raise ValueError(
                f"Length of p doesn't match the number of transforms: {len(p)} != {len(transforms)}"
            )

        if n_subset is None:
            n_subset = len(transforms)
        elif not isinstance(n_subset, int):
            raise TypeError("n_subset should be an int or None")
        elif not (1 <= n_subset <= len(transforms)):
            raise ValueError(f"n_subset should be in the interval [1, {len(transforms)}]")

        self.transforms = transforms
        total = sum(p)
        self.p = [prob / total for prob in p]
        self.n_subset = n_subset
        self.random_order = random_order

        self.selected_transforms = None

    def forward(self, *inputs: Any) -> Any:
        needs_unpacking = len(inputs) > 1

        selected_indices = torch.multinomial(torch.tensor(self.p), self.n_subset)
        if not self.random_order:
            selected_indices = selected_indices.sort().values

        self.selected_transforms = [self.transforms[i] for i in selected_indices]

        for transform in self.selected_transforms:
            outputs = transform(*inputs)
            inputs = outputs if needs_unpacking else (outputs,)

        return outputs

    def extra_repr(self) -> str:
        return (
            f"transforms={self.transforms}, "
            f"p={self.p}, "
            f"n_subset={self.n_subset}, "
            f"random_order={self.random_order}"
        )


class SharpnessJitter(Transform):
    """Randomly change the sharpness of an image or video.

    Similar to a v2.RandomAdjustSharpness with p=1 and a sharpness_factor sampled randomly.
    While v2.RandomAdjustSharpness applies — with a given probability — a fixed sharpness_factor to an image,
    SharpnessJitter applies a random sharpness_factor each time. This is to have a more diverse set of
    augmentations as a result.

    A sharpness_factor of 0 gives a blurred image, 1 gives the original image while 2 increases the sharpness
    by a factor of 2.

    If the input is a :class:`torch.Tensor`,
    it is expected to have [..., 1 or 3, H, W] shape, where ... means an arbitrary number of leading dimensions.

    Args:
        sharpness: How much to jitter sharpness. sharpness_factor is chosen uniformly from
            [max(0, 1 - sharpness), 1 + sharpness] or the given
            [min, max]. Should be non negative numbers.
    """

    def __init__(self, sharpness: float | Sequence[float]) -> None:
        super().__init__()
        self.sharpness = self._check_input(sharpness)

    def _check_input(self, sharpness):
        if isinstance(sharpness, (int, float)):
            if sharpness < 0:
                raise ValueError("If sharpness is a single number, it must be non negative.")
            sharpness = [1.0 - sharpness, 1.0 + sharpness]
            sharpness[0] = max(sharpness[0], 0.0)
        elif isinstance(sharpness, collections.abc.Sequence) and len(sharpness) == 2:
            sharpness = [float(v) for v in sharpness]
        else:
            raise TypeError(f"{sharpness=} should be a single number or a sequence with length 2.")

        if not 0.0 <= sharpness[0] <= sharpness[1]:
            raise ValueError(f"sharpnesss values should be between (0., inf), but got {sharpness}.")

        return float(sharpness[0]), float(sharpness[1])

    def make_params(self, flat_inputs: list[Any]) -> dict[str, Any]:
        sharpness_factor = torch.empty(1).uniform_(self.sharpness[0], self.sharpness[1]).item()
        return {"sharpness_factor": sharpness_factor}

    def transform(self, inpt: Any, params: dict[str, Any]) -> Any:
        sharpness_factor = params["sharpness_factor"]
        return self._call_kernel(F.adjust_sharpness, inpt, sharpness_factor=sharpness_factor)


@dataclass
class ImageTransformConfig:
    """
    For each transform, the following parameters are available:
      weight: This represents the multinomial probability (with no replacement)
            used for sampling the transform. If the sum of the weights is not 1,
            they will be normalized.
      type: The name of the class used. This is either a class available under torchvision.transforms.v2 or a
            custom transform defined here.
      kwargs: Lower & upper bound respectively used for sampling the transform's parameter
            (following uniform distribution) when it's applied.
    """

    weight: float = 1.0
    type: str = "Identity"
    kwargs: dict[str, Any] = field(default_factory=dict)


@dataclass
class ImageTransformsConfig:
    """
    These transforms are all using standard torchvision.transforms.v2
    You can find out how these transformations affect images here:
    https://pytorch.org/vision/0.18/auto_examples/transforms/plot_transforms_illustrations.html
    We use a custom RandomSubsetApply container to sample them.
    """

    # Set this flag to `true` to enable transforms during training
    enable: bool = False
    # This is the maximum number of transforms (sampled from these below) that will be applied to each frame.
    # It's an integer in the interval [1, number_of_available_transforms].
    max_num_transforms: int = 7  # 增加最大变换数量，以适应新增的变换
    # By default, transforms are applied in Torchvision's suggested order (shown below).
    # Set this to True to apply them in a random order.
    random_order: bool = False
    tfs: dict[str, ImageTransformConfig] = field(
        default_factory=lambda: {
            "brightness": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"brightness": (0.8, 1.2)},
            ),
            "contrast": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"contrast": (0.8, 1.2)},
            ),
            "saturation": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"saturation": (0.5, 1.5)},
            ),
            "hue": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"hue": (-0.05, 0.05)},
            ),
            "sharpness": ImageTransformConfig(
                weight=1.0,
                type="SharpnessJitter",
                kwargs={"sharpness": (0.5, 1.5)},
            ),
            # 新增的变换配置
            "perspective": ImageTransformConfig(
                weight=1.0,
                type="RandomPerspective",
                kwargs={"distortion_scale": 0.5},
            ),
            "affine": ImageTransformConfig(
                weight=1.0,
                type="RandomAffine",
                kwargs={"degrees": 10, "translate": (0.1, 0.1), "scale": (0.9, 1.1)},
            ),
            "blur": ImageTransformConfig(
                weight=1.0,
                type="GaussianBlur",
                kwargs={"kernel_size": (9, 9), "sigma": (0.1, 2.0)},
            ),
            "resize": ImageTransformConfig(
                weight=1.0,
                type="Resize",
                kwargs={"size": (16 * 14, 22 * 14)},
            ),
            "mae_mask": ImageTransformConfig(
                weight=0.0,  # 默认不启用，用户可以通过配置开启
                type="MAEMask",
                kwargs={
                    "mask_ratio": 0.5,  # 默认掩码率50%
                    "patch_size": 14,   # 默认patch大小14x14
                    "fill_value": 0.0,  # 默认用0填充掩码区域
                },
            ),
        }
    )


def make_transform_from_config(cfg: ImageTransformConfig):
    if cfg.type == "Identity":
        return v2.Identity(**cfg.kwargs)
    elif cfg.type == "ColorJitter":
        return v2.ColorJitter(**cfg.kwargs)
    elif cfg.type == "SharpnessJitter":
        return SharpnessJitter(**cfg.kwargs)
    elif cfg.type == "MAEMask":
        return MAEMaskTransform(**cfg.kwargs)
    elif cfg.type == "RandomPerspective":
        return v2.RandomPerspective(**cfg.kwargs)
    elif cfg.type == "RandomAffine":
        return v2.RandomAffine(**cfg.kwargs)
    elif cfg.type == "GaussianBlur":
        return v2.GaussianBlur(**cfg.kwargs)
    elif cfg.type == "Resize":
        return v2.Resize(**cfg.kwargs)
    else:
        raise ValueError(f"Transform '{cfg.type}' is not valid.")


class ImageTransforms(Transform):
    """A class to compose image transforms based on configuration."""

    def __init__(self, cfg: ImageTransformsConfig) -> None:
        super().__init__()
        self._cfg = cfg

        self.weights = []
        self.transforms = {}
        self.resize_transform = None
        
        # 单独处理resize变换，确保它总是被应用且在其他变换之前
        if "resize" in cfg.tfs and cfg.tfs["resize"].weight > 0.0:
            self.resize_transform = make_transform_from_config(cfg.tfs["resize"])
        
        for tf_name, tf_cfg in cfg.tfs.items():
            # 跳过resize，因为我们已经单独处理了
            if tf_name == "resize":
                continue
                
            if tf_cfg.weight <= 0.0:
                continue

            self.transforms[tf_name] = make_transform_from_config(tf_cfg)
            self.weights.append(tf_cfg.weight)

        n_subset = min(len(self.transforms), cfg.max_num_transforms)
        if n_subset == 0 or not cfg.enable:
            self.tf = v2.Identity()
        else:
            self.tf = RandomSubsetApply(
                transforms=list(self.transforms.values()),
                p=self.weights,
                n_subset=n_subset,
                random_order=cfg.random_order,
            )

    def forward(self, *inputs: Any) -> Any:
        # 记录原始输入的数据类型
        original_dtypes = [x.dtype if isinstance(x, torch.Tensor) else None for x in inputs]
        
        # 首先应用resize变换（如果有）
        if self.resize_transform is not None:
            if len(inputs) == 1:
                inputs = (self.resize_transform(inputs[0]),)
            else:
                # 处理多输入情况
                inputs = tuple(self.resize_transform(x) for x in inputs)
        
        # 然后应用其他随机选择的变换
        outputs = self.tf(*inputs)
        
        # 将输出转换回原始数据类型
        if isinstance(outputs, torch.Tensor) and len(original_dtypes) > 0 and original_dtypes[0] is not None:
            outputs = outputs.to(dtype=original_dtypes[0])
        elif isinstance(outputs, tuple):
            outputs_list = list(outputs)
            for i, (output, dtype) in enumerate(zip(outputs_list, original_dtypes)):
                if isinstance(output, torch.Tensor) and dtype is not None:
                    outputs_list[i] = output.to(dtype=dtype)
            outputs = tuple(outputs_list)
            
        return outputs
