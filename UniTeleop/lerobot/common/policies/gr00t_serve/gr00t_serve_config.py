from dataclasses import dataclass, field

from lerobot.configs.policies import PreTrainedConfig


@PreTrainedConfig.register_subclass("gr00t_serve")
@dataclass
class Gr00tServeConfig():
    """Configuration for the Serve policy."""

    # The host and port of the server to connect to.
    host: str = "js2.blockelite.cn"
    port: int = 15758

    action_dim: int = 16
    language_instruction: str = "Place the blue bottle into the empty space in the box."
    img_size: tuple[int, int] = (480, 640)
    observation_mapping: dict[str, str] = None
    modality_keys: list[str] = field(default_factory=lambda: ['left_arm', 'right_arm', 'left_gripper', 'right_gripper'])
    
    @property
    def type(self) -> str:
        return "gr00t_serve"

    def __post_init__(self):
        if self.observation_mapping is None:
            self.observation_mapping = {
                "video.hand_right_color": "observation.images.hand_right_color",
                "video.hand_left_color": "observation.images.hand_left_color",
                "video.head_color": "observation.images.head_color",
                "state.left_arm": "observation.state[0:7]",
                "state.left_gripper": "observation.state[14:15]",
                "state.right_arm": "observation.state[8:15]",
                "state.right_gripper": "observation.state[15:16]",
            }