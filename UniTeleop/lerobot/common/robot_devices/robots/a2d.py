#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import time
import os
from dataclasses import replace, field, dataclass
from typing import List, Tuple, Dict, Union, Optional

import numpy as np
import torch
import cv2
from scipy.spatial.transform import Rotation as R

import pink
import qpsolvers
import pinocchio as pin
from pinocchio.visualize import MeshcatVisualizer
from pink import solve_ik
from pink.tasks import FrameTask, PostureTask

from a2d_sdk.robot import RobotDds as Robot
from a2d_sdk.robot import MotionController
from a2d_sdk.robot import CosineCamera as A2DCamera

ARM_RESET_POSITION = [-1.074169635772705, 0.6115003228187561, 0.2802993357181549, -1.2832905054092407, 0.729724109172821, 1.495517373085022, -0.18664519488811493, 1.0692312717437744, -0.606038510799408, -0.2755005955696106, 1.2892932891845703, -0.7303174138069153, -1.492480993270874, 0.18664519488811493]
BODY_RESET_POSITION = [0.3697020709514618, 0.5230002999305725]

# 0.5230002817482688, 0.369737548828125

@dataclass
class RobotController:
    """A2D robot controller using motion controller for robot control.
    
    Provides a simplified interface for controlling the A2D robot through the
    MotionController provided by the A2D SDK.
    """
    # 初始化运动控制器
    _motion_controller: MotionController = field(default_factory=MotionController)
    
    def get_motion_status(self, timestamp=None):
        """获取运动控制状态
        
        Args:
            timestamp: 可选的时间戳(纳秒)，如果提供则返回最接近该时间戳的状态
                     如果不提供则返回最新状态
                      
        Returns:
            包含运控状态信息的字典，如果没有状态则返回None
        """
        return self._motion_controller.get_motion_control_status(timestamp)
    
    def reactive_control(self, robot_action, robot_link="base_link", lifetime=0.5):
        """创建反应式控制命令
        
        Args:
            robot_action: 控制动作字典
            robot_link: 机器人链接名称
            lifetime: 命令生命周期(秒)
            
        Returns:
            ReactiveControl消息
        """
        # 使用当前时间作为默认时间戳
        infer_timestamp = time.time_ns()
        self._motion_controller.create_reactive_control(
            infer_timestamp, robot_action, robot_link, lifetime
        )
    
    def trajectory_tracking_control(self, infer_timestamp, robot_states, robot_actions, 
                                  robot_link="base_link", trajectory_reference_time=1.0):
        """创建轨迹跟踪控制命令
        
        Args:
            infer_timestamp: Timestamp in nanoseconds
            robot_states: robot joint states by group when infer_timestamp
            robot_actions: List of action dictionaries for trajectory
                - robot_action['part']['action_data']
                - robot_action['part']['control_type']
            robot_link: Link name of the robot
            trajectory_reference_time: Reference time for trajectory
            
        Returns:
            TrajectoryTrackingControl消息
        """
        self._motion_controller.create_trajectory_tracking(
            infer_timestamp, robot_states, robot_actions, 
            robot_link, trajectory_reference_time
        )
    
    def control_head_yaw(self, angle, lifetime=5.0):
        """控制头部 yaw 角
        
        Args:
            angle: 头部 yaw 角度 (弧度)
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'head': {
                'control_type': 'ABS_JOINT',
                'action_data': [angle, 0.0]  # [yaw, pitch]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_waist_lift(self, position, lifetime=5.0):
        """控制腰部升降
        
        Args:
            position: 腰部升降位置 (米)
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'waist': {
                'control_type': 'ABS_JOINT',
                'action_data': [0.0, position * 100.0]  # [pitch, lift_cm]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_waist_pitch(self, angle, lifetime=5.0):
        """控制腰部俯仰
        
        Args:
            angle: 腰部俯仰角度 (弧度)
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'waist': {
                'control_type': 'ABS_JOINT',
                'action_data': [angle, 0.0]  # [pitch, lift_cm]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_right_gripper(self, position, lifetime=5.0):
        """控制右夹爪
        
        Args:
            position: 夹爪位置 (弧度/米)
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'gripper': {
                'control_type': 'ABS_JOINT',
                'action_data': [0.0, position]  # [left, right]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_left_gripper(self, position, lifetime=5.0):
        """控制左夹爪
        
        Args:
            position: 夹爪位置 (弧度/米)
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'gripper': {
                'control_type': 'ABS_JOINT',
                'action_data': [position, 0.0]  # [left, right]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_arm_joints(self, arm_side, joint_positions, lifetime=5.0):
        """使用关节角度控制机械臂
        
        Args:
            arm_side: 'left' 或 'right' 表示控制哪一侧的手臂
            joint_positions: 关节角度列表 (7个关节的角度, 弧度)
            lifetime: 命令生命周期(秒)
        """
        if arm_side not in ['left', 'right']:
            raise ValueError("arm_side must be 'left' or 'right'")
        
        if len(joint_positions) != 7:
            raise ValueError("joint_positions must contain 7 values for the 7 arm joints")
        
        robot_action = {
            f'{arm_side}_arm': {
                'control_type': 'ABS_JOINT',
                'action_data': joint_positions
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_end_effector_pose(self, left_pose=None, right_pose=None, lifetime=10.0):
        """使用末端位姿控制左右臂
        
        Args:
            left_pose: 左臂末端位姿, 格式为 EndEffectorPose 或字典 {
                'position': {'x': float, 'y': float, 'z': float},
                'orientation': {'x': float, 'y': float, 'z': float, 'w': float}
            }
            right_pose: 右臂末端位姿, 格式与 left_pose 相同
            lifetime: 命令生命周期(秒)
        """
        robot_action = {}
        
        if left_pose:
            # 从位姿字典中提取位置和方向
            pos = left_pose['position'] if isinstance(left_pose, dict) else left_pose.position
            ori = left_pose['orientation'] if isinstance(left_pose, dict) else left_pose.orientation
            
            # 将四元数转换为欧拉角
            quat = [ori['x'], ori['y'], ori['z'], ori['w']]
            euler = R.from_quat(quat).as_euler('xyz')
            
            # 创建左臂控制动作
            robot_action['left_arm'] = {
                'control_type': 'ABS_POSE',
                'action_data': [pos['x'], pos['y'], pos['z'], euler[0], euler[1], euler[2]]
            }
        
        if right_pose:
            # 从位姿字典中提取位置和方向
            pos = right_pose['position'] if isinstance(right_pose, dict) else right_pose.position
            ori = right_pose['orientation'] if isinstance(right_pose, dict) else right_pose.orientation
            
            # 将四元数转换为欧拉角
            quat = [ori['x'], ori['y'], ori['z'], ori['w']]
            euler = R.from_quat(quat).as_euler('xyz')
            
            # 创建右臂控制动作
            robot_action['right_arm'] = {
                'control_type': 'ABS_POSE',
                'action_data': [pos['x'], pos['y'], pos['z'], euler[0], euler[1], euler[2]]
            }
        
        if not robot_action:
            raise ValueError("At least one of left_pose or right_pose must be provided")
        
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def emergency_stop(self):
        """全身急停"""
        # 创建MotionStop消息并发布到/wbc/motion_stop主题
        from genie_msgs_pb.msg.MotionStop_pb2 import MotionStop
        
        # 创建MotionStop消息
        motion_stop_msg = MotionStop()
        motion_stop_msg.header.stamp.sec = 0
        motion_stop_msg.header.stamp.nanosec = 0
        motion_stop_msg.header.frame_id = ""
        motion_stop_msg.input_type = 54  # 从示例中获取的值
        
        # 使用MotionController的节点发布消息
        motion_stop_pub = self._motion_controller.node_.create_publisher(
            "/wbc/motion_stop", MotionStop, self._motion_controller.qos
        )
        motion_stop_pub.publish(motion_stop_msg)
    
    def clear_stop(self):
        """清除急停状态"""
        # 需要创建一个SetControlMode消息并发布到/wbc/set_control_mode主题
        from genie_msgs_pb.msg.SetControlMode_pb2 import SetControlMode
        
        # 创建SetControlMode消息
        control_mode_msg = SetControlMode()
        control_mode_msg.header.stamp.sec = 0
        control_mode_msg.header.stamp.nanosec = 0
        control_mode_msg.header.frame_id = ""
        control_mode_msg.input_type = 54  # 从示例中获取的值
        control_mode_msg.control_mode = 1  # SERVO模式
        
        # 使用MotionController的节点发布消息
        set_control_mode_pub = self._motion_controller.node_.create_publisher(
            "/wbc/set_control_mode", SetControlMode, self._motion_controller.qos
        )
        set_control_mode_pub.publish(control_mode_msg)
    
    def joint_position_control(self, control_group, joint_positions=None, head_yaw=None, 
                           waist_lift=None, waist_pitch=None, lifetime=5.0):
        """直接发布关节位置控制命令
        
        Args:
            control_group: 控制组ID
                1: 头部yaw; 2: 头部pitch; 4: 左臂; 8: 右臂
                16: 腰部升降; 32: 腰部俯仰; 64: 左工具; 128: 右工具
            joint_positions: 关节位置列表（当控制手臂或工具时使用）
            head_yaw: 头部yaw角（当control_group=1时使用）
            waist_lift: 腰部升降位置（当control_group=16时使用）
            waist_pitch: 腰部俯仰角（当control_group=32时使用）
            lifetime: 命令生命周期(秒)
        """
        from genie_msgs_pb.msg.JointPositionControl_pb2 import JointPositionControl
        
        # 创建关节位置控制消息
        joint_control_msg = JointPositionControl()
        joint_control_msg.header.stamp.sec = 0
        joint_control_msg.header.stamp.nanosec = 0
        joint_control_msg.header.frame_id = ""
        joint_control_msg.lifetime = lifetime
        joint_control_msg.control_group = control_group
        
        # 根据control_group设置相应的字段
        if control_group == 1 and head_yaw is not None:  # 头部yaw
            joint_control_msg.head_yaw_joint_position = head_yaw
        elif control_group == 16 and waist_lift is not None:  # 腰部升降
            joint_control_msg.waist_lift_joint_position = waist_lift
        elif control_group == 32 and waist_pitch is not None:  # 腰部俯仰
            joint_control_msg.waist_pitch_joint_position = waist_pitch
        elif control_group in [4, 8, 64, 128, 256]:  # 手臂或工具
            if joint_positions:
                if control_group == 4:  # 左臂
                    joint_control_msg.left_arm_joint_positions.extend(joint_positions)
                elif control_group == 8:  # 右臂
                    joint_control_msg.right_arm_joint_positions.extend(joint_positions)
                elif control_group == 64:  # 左工具
                    joint_control_msg.left_tool_joint_positions.extend(joint_positions)
                elif control_group == 128 or control_group == 256:  # 右工具
                    joint_control_msg.right_tool_joint_positions.extend(joint_positions)
        
        # 使用MotionController的节点发布消息
        joint_position_pub = self._motion_controller.node_.create_publisher(
            "/wbc/joint_position_control", JointPositionControl, self._motion_controller.qos
        )
        joint_position_pub.publish(joint_control_msg)
    
    def end_effector_pose_control(self, left_pose=None, right_pose=None, lifetime=10.0):
        """直接发布末端位姿控制命令
        
        Args:
            left_pose: 左臂末端位姿, 格式为 EndEffectorPose 或字典 {
                'position': {'x': float, 'y': float, 'z': float},
                'orientation': {'x': float, 'y': float, 'z': float, 'w': float}
            }
            right_pose: 右臂末端位姿, 格式与 left_pose 相同
            lifetime: 命令生命周期(秒)
        """
        from genie_msgs_pb.msg.EndEffectorPoseControl_pb2 import EndEffectorPoseControl
        from geometry_msgs_pb.msg.Pose_pb2 import Pose
        
        # 创建末端位姿控制消息
        pose_control_msg = EndEffectorPoseControl()
        pose_control_msg.header.stamp.sec = 0
        pose_control_msg.header.stamp.nanosec = 0
        pose_control_msg.header.frame_id = "base_link"
        pose_control_msg.lifetime = lifetime
        
        # 设置控制组
        control_group = 0
        if left_pose:
            control_group |= 4  # 添加左臂控制
        if right_pose:
            control_group |= 8  # 添加右臂控制
        pose_control_msg.control_group = control_group
        
        # 设置左臂位姿
        if left_pose:
            left_pose_msg = Pose()
            
            # 设置位置
            pos = left_pose['position'] if isinstance(left_pose, dict) else left_pose.position
            left_pose_msg.position.x = pos['x']
            left_pose_msg.position.y = pos['y']
            left_pose_msg.position.z = pos['z']
            
            # 设置方向
            ori = left_pose['orientation'] if isinstance(left_pose, dict) else left_pose.orientation
            left_pose_msg.orientation.x = ori['x']
            left_pose_msg.orientation.y = ori['y']
            left_pose_msg.orientation.z = ori['z']
            left_pose_msg.orientation.w = ori['w']
            
            pose_control_msg.left_end_effector_pose.CopyFrom(left_pose_msg)
        
        # 设置右臂位姿
        if right_pose:
            right_pose_msg = Pose()
            
            # 设置位置
            pos = right_pose['position'] if isinstance(right_pose, dict) else right_pose.position
            right_pose_msg.position.x = pos['x']
            right_pose_msg.position.y = pos['y']
            right_pose_msg.position.z = pos['z']
            
            # 设置方向
            ori = right_pose['orientation'] if isinstance(right_pose, dict) else right_pose.orientation
            right_pose_msg.orientation.x = ori['x']
            right_pose_msg.orientation.y = ori['y']
            right_pose_msg.orientation.z = ori['z']
            right_pose_msg.orientation.w = ori['w']
            
            pose_control_msg.right_end_effector_pose.CopyFrom(right_pose_msg)
        
        # 使用MotionController的节点发布消息
        end_effector_pub = self._motion_controller.node_.create_publisher(
            "/wbc/end_effector_pose_control", EndEffectorPoseControl, self._motion_controller.qos
        )
        end_effector_pub.publish(pose_control_msg)
    
    def reset_arm_position(self, arm_side="both"):
        """重置机械臂到默认位置
        
        Args:
            arm_side: 'left', 'right', 或 'both' (默认)
        """
        if arm_side not in ['left', 'right', 'both']:
            raise ValueError("arm_side must be 'left', 'right', or 'both'")
        
        # ARM_RESET_POSITION 包含左右臂关节角度，前7个为左臂，后7个为右臂
        if arm_side == 'left' or arm_side == 'both':
            self.control_arm_joints('left', ARM_RESET_POSITION[:7])
        
        if arm_side == 'right' or arm_side == 'both':
            self.control_arm_joints('right', ARM_RESET_POSITION[7:])
    
    def control_head(self, yaw=None, pitch=None, lifetime=5.0):
        """控制头部角度
        
        Args:
            yaw: 头部水平转动角度 (弧度)，None表示不改变
            pitch: 头部俯仰角度 (弧度)，None表示不改变
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'head': {
                'control_type': 'ABS_JOINT',
                'action_data': [yaw if yaw is not None else 0.0, 
                                pitch if pitch is not None else 0.0]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_waist(self, pitch=None, lift=None, lifetime=5.0):
        """控制腰部位置
        
        Args:
            pitch: 腰部俯仰角度 (弧度)，None表示不改变
            lift: 腰部升降位置 (米)，None表示不改变
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'waist': {
                'control_type': 'ABS_JOINT',
                'action_data': [pitch if pitch is not None else 0.0, 
                                (lift * 100.0) if lift is not None else 0.0]  # [pitch, lift_cm]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def control_gripper(self, left=None, right=None, lifetime=5.0):
        """控制双侧夹爪
        
        Args:
            left: 左夹爪位置 (弧度/米)，None表示不改变
            right: 右夹爪位置 (弧度/米)，None表示不改变
            lifetime: 命令生命周期(秒)
        """
        robot_action = {
            'gripper': {
                'control_type': 'ABS_JOINT',
                'action_data': [left if left is not None else 0.0, 
                                right if right is not None else 0.0]
            }
        }
        self.reactive_control(robot_action, lifetime=lifetime)
    
    def look_at_position(self, x, y, z, lifetime=5.0):
        """控制头部看向指定的空间位置
        
        Args:
            x, y, z: 目标位置坐标 (米)
            lifetime: 命令生命周期(秒)
        """
        # 获取当前状态
        status = self.get_motion_status()
        if not status:
            return
        
        # 获取头部和目标位置
        if 'head_frame' not in status['frames']:
            # 如果没有头部位置，则无法计算
            return
        
        head_pos = status['frames']['head_frame']['position']
        
        # 计算朝向目标的向量
        dx = x - head_pos['x']
        dy = y - head_pos['y']
        dz = z - head_pos['z']
        
        # 计算头部所需的yaw和pitch角度
        # 注意：这是简化的计算，实际应用中可能需要考虑机器人头部的坐标系和限制
        yaw = np.arctan2(dy, dx)
        pitch = np.arctan2(dz, np.sqrt(dx*dx + dy*dy))
        
        # 控制头部
        self.control_head(yaw=yaw, pitch=pitch, lifetime=lifetime)
    
    def get_arm_ee_pose(self, arm_side=None, quaternion=False):
        """获取机械臂末端位姿
        
        Args:
            arm_side: 'left', 'right', 或 None (默认，返回两臂位姿)
            quaternion: 是否返回四元数
        Returns:
            如果 arm_side 为 'left' 或 'right'，返回numpy数组 [x, y, z, qx, qy, qz, qw]
            
            如果 arm_side 为 None，返回numpy数组 [left_x, left_y, left_z, left_qx, left_qy, left_qz, left_qw,
                                              right_x, right_y, right_z, right_qx, right_qy, right_qz, right_qw]
            
            如果没有获取到位姿信息，相应的值为 None
        """
        if arm_side not in ['left', 'right', None]:
            raise ValueError("arm_side must be 'left', 'right', or None")
        
        # 获取当前状态
        status = self.get_motion_status()
        if not status:
            # 如果无法获取状态，返回None
            if arm_side:
                return None
            return {'left': None, 'right': None}
        
        # 提取末端位姿
        left_ee_pose = None
        right_ee_pose = None
        
        # 左臂末端
        if 'arm_left_link7' in status['frames']:
            frame = status['frames']['arm_left_link7']
            pos = frame['position']
            quat = frame['orientation']['quaternion']
            
            left_ee_pose = np.array([
                pos['x'], pos['y'], pos['z'],
                quat['x'], quat['y'], quat['z'], quat['w']
            ])
        
        # 右臂末端
        if 'arm_right_link7' in status['frames']:
            frame = status['frames']['arm_right_link7']
            pos = frame['position']
            quat = frame['orientation']['quaternion']
            
            right_ee_pose = np.array([
                pos['x'], pos['y'], pos['z'],
                quat['x'], quat['y'], quat['z'], quat['w']
            ])
        if not quaternion:
            left_ee_pose = np.concatenate([left_ee_pose[:3], R.from_quat(left_ee_pose[3:]).as_euler("xyz", degrees=False)])
            right_ee_pose = np.concatenate([right_ee_pose[:3], R.from_quat(right_ee_pose[3:]).as_euler("xyz", degrees=False)])
        # 根据请求返回相应的数据
        if arm_side == 'left':
            return left_ee_pose
        elif arm_side == 'right':
            return right_ee_pose
        else:
            return np.concatenate([left_ee_pose, right_ee_pose])


class DualArmKinematics:
    def __init__(self, urdf_path: str, display: bool = False):
        mesh_dir = os.path.dirname(urdf_path)
        model, collision_model, visual_model = pin.buildModelsFromUrdf(
            urdf_path, mesh_dir
        )
        self.model = model
        self.data = self.model.createData()
        self.q0 = pin.neutral(self.model)
        self.q0[:2] = BODY_RESET_POSITION
        self.q0[2:9] = ARM_RESET_POSITION[:7]
        self.q0[17:24] = ARM_RESET_POSITION[7:]
        self.left_ee_name = "left_base_link"
        self.right_ee_name = "right_base_link"
        # pink configuration
        self.pink_configuration = pink.Configuration(self.model, self.data, self.q0)
        # pink tasks
        # variable joint
        left_ee_task = FrameTask(self.left_ee_name, position_cost=50.0, orientation_cost=10.0, lm_damping=1e-5)
        right_ee_task = FrameTask(self.right_ee_name, position_cost=50.0, orientation_cost=10.0, lm_damping=1e-5)
        # fixed joint
        # body_lift_task = FrameTask("joint_lift_body", position_cost=0.0, orientation_cost=0.0)
        # body_pitch_task = FrameTask("link-pitch_body", position_cost=0.0, orientation_cost=0.0)
        # arm_task = FrameTask("link-arm", position_cost=0.0, orientation_cost=0.0)
        # pink posture task 
        posture_task = PostureTask(cost=1e-3)
        self.pink_tasks = [left_ee_task, right_ee_task, posture_task]
        for task in self.pink_tasks:
            task.set_target_from_configuration(self.pink_configuration)
        self.left_ee_id = self.model.getFrameId(self.left_ee_name)
        self.right_ee_id = self.model.getFrameId(self.right_ee_name)
        self.display = display
        self.viz = None
        if display:
            try:
                from pinocchio.visualize import MeshcatVisualizer
                self.viz = MeshcatVisualizer(model, collision_model, visual_model)
                self.viz.initViewer(open=True)
                self.viz.loadViewerModel()
                self.viz.display(self.q0)
                self.viz.displayVisuals(True)
            except ImportError as err:
                print(
                    "Error while initializing the viewer. "
                    "It seems you should install Python meshcat"
                )
                print(err)
    
    def print_neutral_configuration(self):
        """
        Print the neutral configuration (q0) and corresponding joint names.
        """
        print("Neutral Configuration (q0):")
        print(f"{'Joint Name':<30} {'Value':<15}")
        print("-" * 45)
        
        # Handle floating base (first 7 DoFs for position + quaternion)
        if self.model.joints[1].nq == 7:  # Check if floating base (FreeFlyer)
            print(f"{'base_position_x':<30} {self.q0[0]:<15.6f}")
            print(f"{'base_position_y':<30} {self.q0[1]:<15.6f}")
            print(f"{'base_position_z':<30} {self.q0[2]:<15.6f}")
            print(f"{'base_quaternion_x':<30} {self.q0[3]:<15.6f}")
            print(f"{'base_quaternion_y':<30} {self.q0[4]:<15.6f}")
            print(f"{'base_quaternion_z':<30} {self.q0[5]:<15.6f}")
            print(f"{'base_quaternion_w':<30} {self.q0[6]:<15.6f}")
            start_idx = 7
        else:
            start_idx = 0
        
        # Print remaining joints
        for i, name in enumerate(self.model.names[1:], start=start_idx):  # Skip universe joint
            if i < len(self.q0):
                print(f"ID:{i:<30} {name:<30} {self.q0[i]:<15.6f}")
        
    def forward_kinematics(self, q=None, display=True):
        if q is None:
            q = self.q0
        pin.forwardKinematics(self.model, self.data, q)
        pin.updateFramePlacements(self.model, self.data)

        Tl = self.data.oMf[self.left_ee_id]
        Tr = self.data.oMf[self.right_ee_id]
        if self.viz is not None and display:
            self.viz.display(q)
        return Tl, Tr
    
    def inverse_kinematics(self, T_left_goal, T_right_goal, dt, joint_positions=None, fix_base=True):
        """
        Inverse kinematics for a single arm (partial joint optimization).
        
        Args:
            T_left_goal: pin.SE3 target transform for left arm.
            T_right_goal: pin.SE3 target transform for right arm.
            dt: time step.
            joint_positions: joint positions to optimize.
        
        Returns:
            target_joint_pos: optimized joint positions.
        """
        self.pink_tasks[0].set_target(T_left_goal)
        self.pink_tasks[1].set_target(T_right_goal)
        # Update Pink's robot configuration with the current joint positions
        # Solve the inverse kinematics problem
        try:
            velocity = solve_ik(
                self.pink_configuration,
                self.pink_tasks,
                dt,
                solver="osqp",
            )
            # fix the base joint
            if fix_base:
                velocity[:2] = [0., 0.]
            self.pink_configuration.integrate_inplace(velocity, dt)
            target_joint_pos = self.pink_configuration.q
        except Exception as e:
            print(f"IK failed: {e}")
            target_joint_pos = joint_positions
        return target_joint_pos

def convert_rotation_to_euler(se3: pin.SE3):
    translation = se3.translation
    rotation = R.from_matrix(se3.rotation).as_euler("xyz", degrees=False)
    return np.concatenate([translation, rotation])

class A2DRobot:
    """
    Wrapper for a2d_sdk.robot.RobotDds that provides a standardized interface
    for controlling the A2D robot.
    """

    def __init__(self, config=None, **kwargs):
        """
        Initialize the A2D robot.
        
        Args:
            config: Robot configuration (optional)
            **kwargs: Additional arguments to pass to the configuration
        """
        if config is None:
            self.config = kwargs
        else:
            # Overwrite config arguments using kwargs
            self.config = replace(config, **kwargs)

        self.robot_type = "a2d"
        self.cameras = {}
        if hasattr(self.config, "cameras"):
            self.cameras = self.config.cameras
        
        self.robot = None
        self.robot_control = None
        self.camera_group = None
        self.is_connected = False
        self.kinematics = DualArmKinematics(self.config.urdf_path, self.config.display)
        self.camera_group_shape = (self.config.cameras["camera_group"].height, self.config.cameras["camera_group"].width)
        self.only_arm: bool = True
        self.logs = {}
        self.last_states = {}
        self.last_images = {}
    
    @property
    def camera_features(self) -> dict:
        cam_ft = {}
        for cam_key in self.cameras["camera_group"].camera_group:
            cam_key = cam_key.split("/")[-1]
            image_shape = self.camera_group_shape
            if "color" in cam_key:
                key = f"observation.images.{cam_key}"
                cam_ft[key] = {
                    "shape": (image_shape[0], image_shape[1], 3),
                    "names": ["height", "width", "channels"],
                    "info": None,
                    "dtype": "video",
                }
            elif "depth" in cam_key:
                key = f"observation.images.{cam_key}"
                if "head" in cam_key:
                    image_shape = (720, 1280)
                cam_ft[key] = {
                    "shape": (image_shape[0], image_shape[1], 1),
                    "names": ["height", "width", "channels"],
                    "info": None,
                    "dtype": "image",
                }
        return cam_ft

    @property
    def motor_features(self) -> dict:
        # Define standard motor names for A2D robot
        action_names = [
            "left_shoulder_roll", "left_shoulder_pitch", "left_shoulder_yaw", 
            "left_elbow_pitch", "left_wrist_roll", "left_wrist_pitch", "left_wrist_yaw",
            "right_shoulder_roll", "right_shoulder_pitch", "right_shoulder_yaw", 
            "right_elbow_pitch", "right_wrist_roll", "right_wrist_pitch", "right_wrist_yaw",
            "left_gripper", "right_gripper",
        ]
        if not self.only_arm:
            action_names.extend([
            # Hand joints (if using hand instead of gripper)
            "left_hand_thumb_joint1", "left_hand_thumb_joint2", "left_hand_thumb_joint3",
            "left_hand_index_joint1", "left_hand_index_joint2", "left_hand_index_joint3",
            "right_hand_thumb_joint1", "right_hand_thumb_joint2", "right_hand_thumb_joint3",
            "right_hand_index_joint1", "right_hand_index_joint2", "right_hand_index_joint3",
            # Head and waist
            "head_yaw", "head_pitch",
            "waist_pitch", "waist_height",
            # Wheel
            "wheel_linear", "wheel_angular"
            ])
        state_names = action_names.copy()
        
        return {
            "action": {
                "dtype": "float32",
                "shape": (len(action_names),),
                "names": action_names,
            },
            "observation.state": {
                "dtype": "float32",
                "shape": (len(state_names),),
                "names": state_names,
            },
            "observation.force": {
                "dtype": "float32",
                "shape": (12,),
                "names": ["left_force_x", "left_force_y", "left_force_z", "left_torque_x", "left_torque_y", "left_torque_z",
                          "right_force_x", "right_force_y", "right_force_z", "right_torque_x", "right_torque_y", "right_torque_z"],
            },
            "observation.ee_pose": {
                "dtype": "float32",
                "shape": (12,),
                "names": ["left_ee_x", "left_ee_y", "left_ee_z", "left_ee_rx", "left_ee_ry", "left_ee_rz",
                          "right_ee_x", "right_ee_y", "right_ee_z", "right_ee_rx", "right_ee_ry", "right_ee_rz"],
            }
        }
    
    @property
    def features(self):
        return {**self.motor_features, **self.camera_features}

    def reset_arm(self) -> None:
        """Reset the arm to the initial position."""
        self.robot.reset(arm_positions=ARM_RESET_POSITION)

    def connect(self) -> None:
        """Connect to the A2D robot."""
        self.robot = Robot()
        self.robot_control = RobotController()
        time.sleep(0.5)
        self.is_connected = True

        # Connect cameras if available
        self.camera_group = A2DCamera(self.cameras["camera_group"].camera_group)
        time.sleep(0.5)
        
        if not self.is_connected:
            print("Could not connect to the robot or cameras.")
            raise ConnectionError()

    def teleop_step(self, record_data=False) -> Optional[Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]]:
        """
        Execute a teleoperation step.
        
        Args:
            record_data: Whether to record data during teleoperation
            
        Returns:
            If record_data=True, returns tuple of (observation_dict, action_dict)
            Otherwise returns None
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
            
        # This method would need to be implemented based on teleoperation needs
        # For now, just capture current state
        obs_dict = self.capture_observation()
        
        # No action is taken in this method without additional implementation
        action_dict = {"action": obs_dict["observation.state"]}  # Placeholder
        
        if not record_data:
            return None
        
        return obs_dict, action_dict

    def get_state(self) -> Dict[str, float]:
        """
        Get the current state of the robot.
        
        Returns:
            Dictionary containing robot state information
        """
        before_read_t = time.perf_counter()

        state_values = np.zeros((34,))
        
        # Get all joint states
        # body_pose_joints = self.robot.body_pose_joint_states()
        head_joints, head_timestamp = self.robot.head_joint_states()
        waist_joints, waist_timestamp = self.robot.waist_joint_states()
        arm_joints, arm_timestamp = self.robot.arm_joint_states()
        gripper_states, gripper_timestamp = self.robot.gripper_states()
        hand_joints, hand_timestamp = self.robot.hand_joint_states()
        hand_forces = self.robot.hand_force_states()
        
        # Store states in a dictionary
        # 0-13: left arm & right arm joints
        # 14-15: left & right gripper
        # 16-27: left & right hand joints
        # 28-29: head yaw & pitch
        # 30-31: waist pitch & height
        # 32-33: wheel linear & angular
        if (arm_joints is not None) and len(arm_joints) == 14:
            state_values[:14] = arm_joints
        else:
            # Fill with zeros or default values if arm_joints is None or wrong length
            state_values[:14] = self.robot.arm_initial_joint_position
        if gripper_states:
            state_values[14:16] = np.array(gripper_states) / 120.0
        else:
            state_values[14:16] = [0, 0]
        if hand_joints:
            state_values[16:28] = hand_joints
        else:
            state_values[16:28] = self.robot.hand_initial_joint_position
        if None not in head_joints:
            state_values[28:30] = head_joints
        else:
            state_values[28:30] = self.robot.head_initial_joint_position
        if None not in waist_joints:
            state_values[30:32] = waist_joints
        else:
            state_values[30:32] = self.robot.waist_initial_joint_position
        state_values[32:34] = [0, 0]

        if self.only_arm:
            state_values = state_values[:16]
                    # Get end-effector pose from robot controller if available
        ee_pose = None
        try:
            ee_pose = self.robot_control.get_arm_ee_pose()
        except Exception as e:
            print(f"Warning: Failed to get arm end-effector pose: {e}")
            # Create default zero-filled pose array
        
        state = {
            "observation.state": torch.from_numpy(state_values).to(dtype=torch.float32),
            "observation.force": torch.as_tensor(hand_forces).to(dtype=torch.float32),
            "observation.ee_pose": torch.from_numpy(ee_pose).to(dtype=torch.float32),
        }
        
        self.logs["read_state_dt_s"] = time.perf_counter() - before_read_t
        return state

    def capture_observation(self) -> Dict[str, torch.Tensor]:
        """
        Capture the current robot observation.
        
        Returns:
            Dictionary containing sensor observations
        """
        # Get robot state
        before_read_t = time.perf_counter()
        state = self.get_state()
        self.logs["read_pos_dt_s"] = time.perf_counter() - before_read_t
        
        # Populate output dictionary with state
        obs_dict = state
        
        # Capture images from cameras if any
        images = {}
        if self.camera_group:
            for cam_key in self.cameras["camera_group"].camera_group:
                before_camread_t = time.perf_counter()
                name = cam_key.split("/")[-1]
                images[name], timestamp = self.camera_group.get_latest_image(cam_key)
                if images[name] is not None:
                    if (images[name].shape[:2] != self.camera_group_shape) and "depth" not in name:
                        # Resize image to match expected shape
                        images[name] = cv2.resize(images[name], self.camera_group_shape[::-1])
                    images[name] = torch.from_numpy(images[name])
                else:
                    images[name] = self.last_images[name]
                
                self.logs[f"async_read_camera_{name}_dt_s"] = time.perf_counter() - before_camread_t
                if "depth" in name:
                    images[name] = images[name].to(dtype=torch.uint16)
                obs_dict[f"observation.images.{name}"] = images[name]
        
        self.last_images = images
        
        return obs_dict

    def send_action(self, action: torch.Tensor) -> torch.Tensor:
        """
        Send an action to the robot.
        
        Args:
            action: Tensor containing action values
            
        Returns:
            The action tensor that was sent
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        if self.only_arm:
            action_list = action[:16].tolist()
        else:
            action_list = action.tolist()
        
        # Parse the action tensor based on expected format
        # Implementation depends on how actions are structured
        # This is a placeholder - you would need to adapt to your specific action space
        before_write_t = time.perf_counter()
        
        # Example of interpreting action components (adjust as needed)
        if len(action_list) >= 14:
            # First 14 elements control arm joints
            arm_positions = action_list[:14]
            self.robot.move_arm(arm_positions)
            
        if (len(action_list) >= 16) and (self.config.eef_type == "gripper"):
            # Next 2 elements control gripper
            gripper_positions = action_list[14:16]
            self.robot.move_gripper(gripper_positions)
            # self.robot.move_gripper([gripper_positions[0] / 120.0, gripper_positions[1] / 120.0])
        if (len(action_list) >= 28) and (self.config.eef_type == "hand"):
            # Next 12 elements control hand joints
            hand_positions = action_list[16:28]
            self.robot.move_hand(hand_positions)
            
        if len(action_list) >= 30:
            # Next 2 elements control head joints [yaw, pitch]
            head_positions = action_list[28:30]
            self.robot.move_head(head_positions)
            
        if len(action_list) >= 32:
            # Next 2 elements control waist [pitch, height]
            waist_positions = action_list[30:32]
            self.robot.move_waist(waist_positions)
            
        if len(action_list) >= 34:
            # Last 2 elements control wheel [linear, angular]
            wheel_linear = action_list[32]
            wheel_angular = action_list[33]
            self.robot.move_wheel(wheel_linear, wheel_angular)
        
        self.logs["write_action_dt_s"] = time.perf_counter() - before_write_t
        
        return action

    def move_arm_joints(self, positions: List[float]) -> None:
        """
        Move the arm joints to the specified positions.
        
        Args:
            positions: List of 14 joint positions in radians
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_arm(positions)
        
    def move_head(self, positions: List[float]) -> None:
        """
        Move the head to the specified position.
        
        Args:
            positions: List of [yaw, pitch] in radians
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_head(positions)
        
    def move_waist(self, positions: List[float]) -> None:
        """
        Move the waist to the specified position.
        
        Args:
            positions: List of [pitch, height] in [radians, cm]
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_waist(positions)
        
    def move_head_and_waist(self, head_pos: List[float], waist_pos: List[float]) -> None:
        """
        Move head and waist simultaneously.
        
        Args:
            head_pos: List of [yaw, pitch] in radians
            waist_pos: List of [pitch, height] in [radians, cm]
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_head_and_waist(head_pos, waist_pos)
        
    def move_gripper(self, positions: List[float]) -> None:
        """
        Move the grippers to the specified positions.
        
        Args:
            positions: List of [left, right] in range [0,1] or [35,120]
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_gripper(positions)
        
    def move_hand(self, positions: List[float]) -> None:
        """
        Move the hand joints to the specified positions.
        
        Args:
            positions: List of 12 joint positions in radians
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_hand(positions)
        
    def move_hand_as_gripper(self, positions: List[float]) -> None:
        """
        Control the hand as a gripper.
        
        Args:
            positions: List of positions in range [0,1]
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_hand_as_gripper(positions)
        
    def move_wheel(self, linear: float, angular: float) -> None:
        """
        Control the wheel movement.
        
        Args:
            linear: Linear velocity
            angular: Angular velocity
        """
        if not self.is_connected:
            raise ConnectionError("Robot is not connected")
        
        self.robot.move_wheel(linear, angular)
        
    def print_logs(self) -> None:
        """Print logged information about robot operations."""
        for key, value in self.logs.items():
            print(f"{key}: {value}")
            
    def disconnect(self) -> None:
        """Disconnect from the robot."""
        # A2D SDK may have a specific disconnect method, if so it would be called here
        
        # Disconnect cameras if available
        if self.camera_group:
            self.camera_group.close()
        if self.robot is not None:
            self.robot.shutdown()
        self.is_connected = False
        print("Disconnected from the robot")
        time.sleep(0.5)
        
    def __del__(self):
        """Clean up resources when the object is deleted."""
        self.disconnect()