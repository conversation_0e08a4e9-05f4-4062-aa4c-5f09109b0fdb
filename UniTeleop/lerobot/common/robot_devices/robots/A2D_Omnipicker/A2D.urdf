<?xml version="1.0"?>
<robot name="A2D">
  <link name="base_link">
    <inertial>
      <origin xyz="-0.0134486971267798 0.00152103941353612 -0.0320076754329413" rpy="0 0 0"/>
      <mass value="23.2085382238952"/>  <!-- 底盘质量约23.2kg -->
      <inertia
        ixx="0.386955641760608" ixy="0.000135417314593726" ixz="0.00669301645720665"
        iyy="0.366800407621218" iyz="0.000105713634260505" izz="0.546852919828475"/>
    </inertial>
    
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <mesh filename="package://meshes/base_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>  <!-- 灰色 -->
      </material>
    </visual>
    
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <mesh filename="package://meshes/base_link.STL"/>
      </geometry>
    </collision>
  </link>

  <!-- 升降机构link：控制整体高度的部分 -->
  <link name="link-up-down_body">
    <inertial>
      <origin xyz="0.0338351537064605 -0.00248044044451858 0.000838431095422365" rpy="0 0 0"/>
      <mass value="3.26275452360108"/>
      <inertia
        ixx="0.0128301877197417" ixy="8.24967996575627E-06" ixz="1.38254543337324E-05"
        iyy="0.006104110250297" iyz="-7.99392548994692E-06" izz="0.0111721084996867"/>
    </inertial>
    
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <mesh filename="package://meshes/link-up-down_body.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <mesh filename="package://meshes/link-up-down_body.STL"/>
      </geometry>
    </collision>
  </link>

  <!-- 升降关节：控制机器人整体高度，可上升30cm -->
  <joint name="joint_lift_body" type="prismatic">
    <origin xyz="0 0 0.6485" rpy="0 0 0"/>  <!-- 从底盘上方34.85cm处开始 -->
    <parent link="base_link"/>
    <child link="link-up-down_body"/>
    <axis xyz="0 0 1"/>                      <!-- 沿Z轴方向运动 -->
    <limit lower="0" upper="0.5" effort="1000" velocity="2.6100"/>  <!-- 升降范围0-50cm -->
  </joint>

  <!-- 身体俯仰link：可前后倾斜的躯干部分 -->
  <link name="link-pitch_body">
    <inertial>
      <origin xyz="0.193733558855111 -0.000484180179482319 -0.00124011422952511" rpy="0 0 0"/>
      <mass value="7.53199207979272"/>
      <inertia
        ixx="0.016546682835947" ixy="0.000274441382484697" ixz="0.00102757387076422"
        iyy="0.0445441227006132" iyz="-0.000236858278708531" izz="0.0402036337794082"/>
    </inertial>
    
    <visual>
      <origin xyz="0 0 0" rpy="3.14159 0 0"/>
      <geometry>
        <mesh filename="package://meshes/link-pitch_body.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    
    <collision>
      <origin xyz="0 0 0" rpy="3.14159 0 0"/>
      <geometry>
        <mesh filename="package://meshes/link-pitch_body.STL"/>
      </geometry>
    </collision>
  </link>

  <!-- 身体俯仰关节：控制机器人俯仰，可俯仰45度 -->
  <joint name="joint_body_pitch" type="revolute">
    <origin xyz="0.131 0 0" rpy="1.5708 -1.5708 0"/>  <!-- 从升降机构前方13.1cm处 -->
    <parent link="link-up-down_body"/>
    <child link="link-pitch_body"/>
    <axis xyz="0 0 -1"/>                              <!-- 绕Z轴负方向旋转 -->
    <limit lower="0" upper="1.047197551" effort="1000" velocity="2.6100"/>  <!-- 0-45度俯仰 -->
  </joint>

  <!-- 头部偏航link：可左右转动的头部 -->
  <link name="link-yaw_head">
    <inertial>
      <origin xyz="0.0446068140025754 0.00241753143645124 0.0528373754603559" rpy="0 0 0"/>
      <mass value="0.414342871921048"/>
      <inertia
        ixx="0.000158695728344024" ixy="-1.04590928109554E-06" ixz="-6.58530903139189E-06"
        iyy="0.000117742864399469" iyz="-2.65011299996072E-06" izz="0.000170947040827065"/>
    </inertial>
    
    <visual>
      <origin xyz="0 0 0" rpy="0 0 3.14159"/>
      <geometry>
        <mesh filename="package://meshes/link-yaw_head.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    
    <collision>
      <origin xyz="0 0 0" rpy="0 0 3.14159"/>
      <geometry>
        <mesh filename="package://meshes/link-yaw_head.STL"/>
      </geometry>
    </collision>
  </link>

  <!-- 头部偏航关节：控制头部左右转动，每边90度 -->
  <joint name="joint_head_yaw" type="fixed">
    <origin xyz="0.441 0 0" rpy="1.5708 0 1.5708"/>  <!-- 从躯干前方44.1cm处 -->
    <parent link="link-pitch_body"/>
    <child link="link-yaw_head"/>
    <axis xyz="0 0 1"/>                              <!-- 绕Z轴旋转 -->
    <limit lower="-1.5708" upper="1.5708" effort="50" velocity="1.0"/>  <!-- 左右各90度 -->
  </joint>

  <!-- 头部俯仰link：可上下点头的部分 -->
  <link name="link-pitch_head">
    <inertial>
      <origin xyz="0.0257009265816389 0.0316254080188787 -3.77390122592164E-05" rpy="0 0 0"/>
      <mass value="0.707063977171202"/>
      <inertia
        ixx="0.00205409595373573" ixy="0.000347730118005016" ixz="-2.13857752451204E-06"
        iyy="0.00200357281313449" iyz="5.16150600423908E-08" izz="0.00184999028444405"/>
    </inertial>
    
    <visual>
      <origin xyz="0 0 0" rpy="0 3.14159 0"/>
      <geometry>
        <mesh filename="package://meshes/link-pitch_head.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    
    <collision>
      <origin xyz="0 0 0" rpy="0 3.14159 0"/>
      <geometry>
        <mesh filename="package://meshes/link-pitch_head.STL"/>
      </geometry>
    </collision>
  </link>

  <!-- 头部俯仰关节：控制头部上下点头，每边45度 -->
  <joint name="joint_head_pitch" type="fixed">
    <origin xyz="-0.050238 0 0.060065" rpy="1.5708 0 0"/>
    <parent link="link-yaw_head"/>
    <child link="link-pitch_head"/>
    <axis xyz="0 0 1"/>
    <limit lower="-0.785398" upper="0.785398" effort="50" velocity="1.0"/>  <!-- 上下各45度 -->
  </joint>

  <!-- 机械臂link：固定在躯干上的机械臂 -->
  <link name="link-arm"/>

  <!-- 机械臂固定关节：将机械臂固定在躯干上 -->
  <joint name="joint_arm_mount" type="fixed">
    <origin xyz="0.305 0 0" rpy="-1.5708 0 -1.5708"/>  <!-- 从躯干前方30.5cm处固定 -->
    <parent link="link-pitch_body"/>
    <child link="link-arm"/>
    <axis xyz="0 0 1"/>
  </joint>

  <!-- 左臂固定关节：将左臂固定在机器人躯干上 -->
  <joint name="joint_left_arm_mount" type="fixed">
    <origin xyz="0 0.025 0" rpy="-1.5708 0 0"/>  <!-- 从link-arm向左偏移2.5cm -->
    <parent link="link-arm"/>
    <child link="base_link_l"/>
  </joint>

  <!-- 右臂固定关节：将右臂固定在机器人躯干上 -->
  <joint name="joint_right_arm_mount" type="fixed">
    <origin xyz="0 -0.025 0" rpy="1.5708 -3.14159 0"/>  <!-- 从link-arm向右偏移2.5cm -->
    <parent link="link-arm"/>
    <child link="base_link_r"/>
  </joint>

  <!-- 左臂的所有link和joint定义 -->
  <link
    name="base_link_l">
    <inertial>
      <origin
        xyz="0.00020641 -0.00057488 0.034396"
        rpy="0 0 0" />
      <mass
        value="0.37595" />
      <inertia
        ixx="0.00024335"
        ixy="1.2188E-06"
        ixz="2.4281E-06"
        iyy="0.00024039"
        iyz="-6.6932E-06"
        izz="0.00032195" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/base_link_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/base_link_l.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="Link1_l">
    <inertial>
      <origin
        xyz="-0.0014596 0.00049476 0.017089"
        rpy="0 0 0" />
      <mass
        value="0.43694" />
      <inertia
        ixx="0.00061894"
        ixy="-6.6923E-06"
        ixz="3.492E-06"
        iyy="0.00054909"
        iyz="1.2223E-06"
        izz="0.00026663" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link1_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link1_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint1_l"
    type="revolute">
    <origin
      xyz="0 0 0.188"
      rpy="3.1416 0 0" />
    <parent
      link="base_link_l" />
    <child
      link="Link1_l" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link2_l">
    <inertial>
      <origin
        xyz="-0.00043013 0.088101 -0.0065021"
        rpy="0 0 0" />
      <mass
        value="0.31992" />
      <inertia
        ixx="0.00033101"
        ixy="-4.1377E-06"
        ixz="9.5154E-07"
        iyy="0.00022098"
        iyz="-4.8041E-05"
        izz="0.00031472" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link2_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link2_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint2_l"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="Link1_l" />
    <child
      link="Link2_l" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-2.09"
      upper="2.09"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link3_l">
    <inertial>
      <origin
        xyz="0.00022593 0.0010417 0.034985"
        rpy="0 0 0" />
      <mass
        value="0.35545" />
      <inertia
        ixx="0.00081056"
        ixy="-2.4265E-06"
        ixz="9.353E-07"
        iyy="0.00083336"
        iyz="3.8681E-06"
        izz="0.00017128" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link3_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link3_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint3_l"
    type="revolute">
    <origin
      xyz="0 0.305 0"
      rpy="1.5708 1.5708 0" />
    <parent
      link="Link2_l" />
    <child
      link="Link3_l" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link4_l">
    <inertial>
      <origin
        xyz="2.1713E-06 0.071943 -0.0054645"
        rpy="0 0 0" />
      <mass
        value="0.1848" />
      <inertia
        ixx="0.00014344"
        ixy="-1.7659E-08"
        ixz="-4.6234E-09"
        iyy="9.0443E-05"
        iyz="-2.1918E-05"
        izz="0.00013811" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link4_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link4_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint4_l"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="Link3_l" />
    <child
      link="Link4_l" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-1.832595715"
      upper="1.483529864"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link5_l">
    <inertial>
      <origin
        xyz="-7.6715E-07 -6.7798E-05 -0.012879"
        rpy="0 0 0" />
      <mass
        value="0.1775" />
      <inertia
        ixx="0.00012838"
        ixy="1.6718E-09"
        ixz="6.959E-09"
        iyy="0.00013741"
        iyz="3.1692E-07"
        izz="6.2102E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link5_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link5_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint5_l"
    type="revolute">
    <origin
      xyz="0 0.1975 0"
      rpy="1.5708 0 -3.1416" />
    <parent
      link="Link4_l" />
    <child
      link="Link5_l" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="30"
      velocity="10" />
  </joint>
  <link
    name="Link6_l">
    <inertial>
      <origin
        xyz="2.2696E-06 -0.10227 -0.0029117"
        rpy="0 0 0" />
      <mass
        value="0.17825" />
      <inertia
        ixx="0.00013761"
        ixy="5.1473E-09"
        ixz="6.9927E-09"
        iyy="8.0777E-05"
        iyz="2.0192E-05"
        izz="0.00012814" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link6_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link6_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint6_l"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 3.1416" />
    <parent
      link="Link5_l" />
    <child
      link="Link6_l" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-1.745329252"
      upper="1.745329252"
      effort="30"
      velocity="10" />
  </joint>
  <link
    name="Link7_l">
    <inertial>
      <origin
        xyz="-0.00025629 9.064E-06 -0.017205"
        rpy="0 0 0" />
      <mass
        value="0.06158" />
      <inertia
        ixx="1.8213E-05"
        ixy="-8.6954E-08"
        ixz="1.0858E-07"
        iyy="1.8374E-05"
        iyz="3.2442E-08"
        izz="2.8547E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link7_l.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link7_l.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint7_l"
    type="revolute">
    <origin
      xyz="0 -0.181 0"
      rpy="-1.5708 0 -3.1416" />
    <parent
      link="Link6_l" />
    <child
      link="Link7_l" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="30"
      velocity="10" />
  </joint>

  <joint
    name="Joint_hand_l"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="Link7_l" />
    <child
      link="left_base_link" />
    <axis
      xyz="0 0 0" />
  </joint>

  <!-- 右臂的所有link和joint定义 -->
  <link
    name="base_link_r">
    <inertial>
      <origin
        xyz="0.00020641 -0.00057488 0.034396"
        rpy="0 0 0" />
      <mass
        value="0.37595" />
      <inertia
        ixx="0.00024335"
        ixy="1.2188E-06"
        ixz="2.4281E-06"
        iyy="0.00024039"
        iyz="-6.6932E-06"
        izz="0.00032195" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/base_link_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/base_link_r.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="Link1_r">
    <inertial>
      <origin
        xyz="0.0013193 0.00016098 0.018211"
        rpy="0 0 0" />
      <mass
        value="0.48414" />
      <inertia
        ixx="0.000726"
        ixy="2.4165E-06"
        ixz="-7.7713E-06"
        iyy="0.00066689"
        iyz="1.2143E-06"
        izz="0.00028434" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link1_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link1_r.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint1_r"
    type="revolute">
    <origin
      xyz="0 0 0.188"
      rpy="-3.1416 0 0" />
    <parent
      link="base_link_r" />
    <child
      link="Link1_r" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link2_r">
    <inertial>
      <origin
        xyz="0.00012037 -0.088102 -0.0062668"
        rpy="0 0 0" />
      <mass
        value="0.31993" />
      <inertia
        ixx="0.00033251"
        ixy="-1.2043E-06"
        ixz="-5.3938E-07"
        iyy="0.00022098"
        iyz="4.5816E-05"
        izz="0.00031325" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link2_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link2_r.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint2_r"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="Link1_r" />
    <child
      link="Link2_r" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-2.09"
      upper="2.09"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link3_r">
    <inertial>
      <origin
        xyz="-3.7545E-06 -0.00012549 -0.035846"
        rpy="0 0 0" />
      <mass
        value="0.34932" />
      <inertia
        ixx="0.00084957"
        ixy="-6.2047E-11"
        ixz="1.3496E-08"
        iyy="0.00082652"
        iyz="2.8949E-07"
        izz="0.00021145" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link3_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link3_r.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint3_r"
    type="revolute">
    <origin
      xyz="0 -0.305 0"
      rpy="1.5708 1.5708 0" />
    <parent
      link="Link2_r" />
    <child
      link="Link3_r" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link4_r">
    <inertial>
      <origin
        xyz="1.9102E-06 0.071943 -0.0054646"
        rpy="0 0 0" />
      <mass
        value="0.1848" />
      <inertia
        ixx="0.00014343"
        ixy="-1.7316E-08"
        ixz="-3.4456E-09"
        iyy="9.0442E-05"
        iyz="-2.1917E-05"
        izz="0.0001381" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link4_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link4_r.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint4_r"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="1.5708 0 -3.1416" />
    <parent
      link="Link3_r" />
    <child
      link="Link4_r" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-1.483529864"
      upper="1.832595715"
      effort="60"
      velocity="10" />
  </joint>
  <link
    name="Link5_r">
    <inertial>
      <origin
        xyz="4.0146E-06 -6.7798E-05 -0.012877"
        rpy="0 0 0" />
      <mass
        value="0.1775" />
      <inertia
        ixx="0.00012838"
        ixy="-3.4874E-09"
        ixz="6.9591E-09"
        iyy="0.00013741"
        iyz="3.1477E-07"
        izz="6.2102E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link5_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link5_r.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint5_r"
    type="revolute">
    <origin
      xyz="0 0.1975 0"
      rpy="1.5708 0 -3.1416" />
    <parent
      link="Link4_r" />
    <child
      link="Link5_r" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="30"
      velocity="10" />
  </joint>
  <link
    name="Link6_r">
    <inertial>
      <origin
        xyz="-2.4946E-06 -0.10227 -0.0029098"
        rpy="0 0 0" />
      <mass
        value="0.17825" />
      <inertia
        ixx="0.00013761"
        ixy="-2.7933E-11"
        ixz="6.9889E-09"
        iyy="8.0777E-05"
        iyz="2.0194E-05"
        izz="0.00012814" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link6_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link6_r.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="Joint6_r"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 3.1416" />
    <parent
      link="Link5_r" />
    <child
      link="Link6_r" />
    <axis
      xyz="0 0 -1" />
    <limit
      lower="-1.745329252"
      upper="1.745329252"
      effort="30"
      velocity="10" />
  </joint>
  <link
    name="Link7_r">
    <inertial>
      <origin
        xyz="0.0002265 0.00012031 -0.017205"
        rpy="0 0 0" />
      <mass
        value="0.06158" />
      <inertia
        ixx="1.8328E-05"
        ixy="-1.1354E-07"
        ixz="-7.7825E-08"
        iyy="1.8259E-05"
        iyz="-8.2338E-08"
        izz="2.8547E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link7_r.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/Link7_r.STL" />
      </geometry>
    </collision>
  </link>


  <joint
    name="Joint7_r"
    type="revolute">
    <origin
      xyz="0 -0.181 0"
      rpy="-1.5708 0 3.1416" />
    <parent
      link="Link6_r" />
    <child
      link="Link7_r" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.14"
      upper="3.14"
      effort="30"
      velocity="10" />
  </joint>

  <joint
    name="Joint_hand_r"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="Link7_r" />
    <child
      link="right_base_link" />
    <axis
      xyz="0 0 0" />
  </joint>
   <link
    name="left_base_link">
    <inertial>
      <origin
        xyz="-0.00013356 1.5301E-05 0.03322"
        rpy="0 0 0" />
      <mass
        value="0.23588" />
      <inertia
        ixx="8.1548E-05"
        ixy="-1.2617E-08"
        ixz="-2.05E-07"
        iyy="5.6921E-05"
        iyz="6.6903E-08"
        izz="7.1611E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/gripper_base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/gripper_base_link.STL" />
      </geometry>
    </collision>
    </link>
    <link
      name="left_narrow1_Link">
      <inertial>
        <origin
          xyz="0.0094685 0.0068806 6.5437E-05"
          rpy="0 0 0" />
        <mass
          value="0.025428" />
        <inertia
          ixx="1.9574E-06"
          ixy="-4.2911E-07"
          ixz="-3.7111E-11"
          iyy="2.3919E-06"
          iyz="-3.008E-10"
          izz="1.5501E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow1_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow1_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_narrow1_joint"
      type="revolute">
      <origin
        xyz="0 -0.0195 0.0565"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="left_base_link" />
      <child
        link="left_narrow1_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_narrow3_Link">
      <inertial>
        <origin
          xyz="0.0088027 -0.007035 1.6424E-05"
          rpy="0 0 0" />
        <mass
          value="0.0040132" />
        <inertia
          ixx="1.0307E-07"
          ixy="5.7851E-08"
          ixz="9.5801E-11"
          iyy="1.1385E-07"
          iyz="-2.81E-11"
          izz="1.7009E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow3_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow3_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_narrow3_joint"
      type="revolute">
      <origin
        xyz="0.030852 0.018551 0"
        rpy="0 0 0" />
      <parent
        link="left_narrow1_Link" />
      <child
        link="left_narrow3_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_narrow4_Link">
      <inertial>
        <origin
          xyz="0.012508 -0.0079729 9.4339E-05"
          rpy="0 0 0" />
        <mass
          value="0.018029" />
        <inertia
          ixx="1.1403E-06"
          ixy="5.5159E-07"
          ixz="-4.0096E-13"
          iyy="2.5704E-06"
          iyz="1.0951E-12"
          izz="2.3252E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow4_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow4_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_narrow4_joint"
      type="revolute">
      <origin
        xyz="0.018118 -0.01574 0"
        rpy="0 0 0" />
      <parent
        link="left_narrow3_Link" />
      <child
        link="left_narrow4_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_narrow2_Link">
      <inertial>
        <origin
          xyz="0.014869 -0.0036066 0.00029307"
          rpy="0 0 0" />
        <mass
          value="0.022591" />
        <inertia
          ixx="4.3916E-06"
          ixy="1.114E-07"
          ixz="-4.9655E-12"
          iyy="4.737E-06"
          iyz="1.7121E-11"
          izz="6.0445E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow2_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow2_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_narrow2_joint"
      type="revolute">
      <origin
        xyz="0 -0.021633 0.07387"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="left_base_link" />
      <child
        link="left_narrow2_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_wide1_Link">
      <inertial>
        <origin
          xyz="0.0095051 -0.0068479 6.8268E-05"
          rpy="0 0 0" />
        <mass
          value="0.025428" />
        <inertia
          ixx="1.9565E-06"
          ixy="4.2798E-07"
          ixz="2.3844E-10"
          iyy="2.3928E-06"
          iyz="-1.4454E-10"
          izz="1.5501E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide1_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide1_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_wide1_joint"
      type="revolute">
      <origin
        xyz="0 0.0195 0.0565"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="left_base_link" />
      <child
        link="left_wide1_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_wide3_Link">
      <inertial>
        <origin
          xyz="0.0088027 0.007035 -1.6424E-05"
          rpy="0 0 0" />
        <mass
          value="0.0040132" />
        <inertia
          ixx="1.0307E-07"
          ixy="-5.7851E-08"
          ixz="-9.58E-11"
          iyy="1.1385E-07"
          iyz="-2.81E-11"
          izz="1.7009E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide3_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide3_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_wide3_joint"
      type="revolute">
      <origin
        xyz="0.030852 -0.018551 0"
        rpy="0 0 0" />
      <parent
        link="left_wide1_Link" />
      <child
        link="left_wide3_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_wide4_Link">
      <inertial>
        <origin
          xyz="0.016206 0.0094593 4.7668E-05"
          rpy="0 0 0" />
        <mass
          value="0.035835" />
        <inertia
          ixx="8.5056E-06"
          ixy="-1.1363E-06"
          ixz="-4.2908E-11"
          iyy="1.1235E-05"
          iyz="-2.9251E-11"
          izz="4.6309E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide4_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide4_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_wide4_joint"
      type="revolute">
      <origin
        xyz="0.018118 0.01574 0"
        rpy="0 0 0" />
      <parent
        link="left_wide3_Link" />
      <child
        link="left_wide4_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="left_wide2_Link">
      <inertial>
        <origin
          xyz="0.016268 0.0040555 0.00030323"
          rpy="0 0 0" />
        <mass
          value="0.025142" />
        <inertia
          ixx="5.887E-06"
          ixy="-1.1234E-07"
          ixz="2.1954E-11"
          iyy="6.236E-06"
          iyz="-9.473E-12"
          izz="6.2389E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide2_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide2_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="left_wide2_joint"
      type="revolute">
      <origin
        xyz="0 0.021633 0.07387"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="left_base_link" />
      <child
        link="left_wide2_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>




    <link
    name="right_base_link">
    <inertial>
      <origin
        xyz="-0.00013356 1.5301E-05 0.03322"
        rpy="0 0 0" />
      <mass
        value="0.23588" />
      <inertia
        ixx="8.1548E-05"
        ixy="-1.2617E-08"
        ixz="-2.05E-07"
        iyy="5.6921E-05"
        iyz="6.6903E-08"
        izz="7.1611E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/gripper_base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://meshes/gripper_base_link.STL" />
      </geometry>
    </collision>
    </link>
    <link
      name="right_narrow1_Link">
      <inertial>
        <origin
          xyz="0.0094685 0.0068806 6.5437E-05"
          rpy="0 0 0" />
        <mass
          value="0.025428" />
        <inertia
          ixx="1.9574E-06"
          ixy="-4.2911E-07"
          ixz="-3.7111E-11"
          iyy="2.3919E-06"
          iyz="-3.008E-10"
          izz="1.5501E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow1_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow1_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_narrow1_joint"
      type="revolute">
      <origin
        xyz="0 -0.0195 0.0565"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="right_base_link" />
      <child
        link="right_narrow1_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_narrow3_Link">
      <inertial>
        <origin
          xyz="0.0088027 -0.007035 1.6424E-05"
          rpy="0 0 0" />
        <mass
          value="0.0040132" />
        <inertia
          ixx="1.0307E-07"
          ixy="5.7851E-08"
          ixz="9.5801E-11"
          iyy="1.1385E-07"
          iyz="-2.81E-11"
          izz="1.7009E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow3_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow3_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_narrow3_joint"
      type="revolute">
      <origin
        xyz="0.030852 0.018551 0"
        rpy="0 0 0" />
      <parent
        link="right_narrow1_Link" />
      <child
        link="right_narrow3_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_narrow4_Link">
      <inertial>
        <origin
          xyz="0.012508 -0.0079729 9.4339E-05"
          rpy="0 0 0" />
        <mass
          value="0.018029" />
        <inertia
          ixx="1.1403E-06"
          ixy="5.5159E-07"
          ixz="-4.0096E-13"
          iyy="2.5704E-06"
          iyz="1.0951E-12"
          izz="2.3252E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow4_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow4_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_narrow4_joint"
      type="revolute">
      <origin
        xyz="0.018118 -0.01574 0"
        rpy="0 0 0" />
      <parent
        link="right_narrow3_Link" />
      <child
        link="right_narrow4_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_narrow2_Link">
      <inertial>
        <origin
          xyz="0.014869 -0.0036066 0.00029307"
          rpy="0 0 0" />
        <mass
          value="0.022591" />
        <inertia
          ixx="4.3916E-06"
          ixy="1.114E-07"
          ixz="-4.9655E-12"
          iyy="4.737E-06"
          iyz="1.7121E-11"
          izz="6.0445E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow2_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/narrow2_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_narrow2_joint"
      type="revolute">
      <origin
        xyz="0 -0.021633 0.07387"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="right_base_link" />
      <child
        link="right_narrow2_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_wide1_Link">
      <inertial>
        <origin
          xyz="0.0095051 -0.0068479 6.8268E-05"
          rpy="0 0 0" />
        <mass
          value="0.025428" />
        <inertia
          ixx="1.9565E-06"
          ixy="4.2798E-07"
          ixz="2.3844E-10"
          iyy="2.3928E-06"
          iyz="-1.4454E-10"
          izz="1.5501E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide1_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide1_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_wide1_joint"
      type="revolute">
      <origin
        xyz="0 0.0195 0.0565"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="right_base_link" />
      <child
        link="right_wide1_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_wide3_Link">
      <inertial>
        <origin
          xyz="0.0088027 0.007035 -1.6424E-05"
          rpy="0 0 0" />
        <mass
          value="0.0040132" />
        <inertia
          ixx="1.0307E-07"
          ixy="-5.7851E-08"
          ixz="-9.58E-11"
          iyy="1.1385E-07"
          iyz="-2.81E-11"
          izz="1.7009E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide3_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide3_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_wide3_joint"
      type="revolute">
      <origin
        xyz="0.030852 -0.018551 0"
        rpy="0 0 0" />
      <parent
        link="right_wide1_Link" />
      <child
        link="right_wide3_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_wide4_Link">
      <inertial>
        <origin
          xyz="0.016206 0.0094593 4.7668E-05"
          rpy="0 0 0" />
        <mass
          value="0.035835" />
        <inertia
          ixx="8.5056E-06"
          ixy="-1.1363E-06"
          ixz="-4.2908E-11"
          iyy="1.1235E-05"
          iyz="-2.9251E-11"
          izz="4.6309E-06" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide4_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide4_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_wide4_joint"
      type="revolute">
      <origin
        xyz="0.018118 0.01574 0"
        rpy="0 0 0" />
      <parent
        link="right_wide3_Link" />
      <child
        link="right_wide4_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>
    <link
      name="right_wide2_Link">
      <inertial>
        <origin
          xyz="0.016268 0.0040555 0.00030323"
          rpy="0 0 0" />
        <mass
          value="0.025142" />
        <inertia
          ixx="5.887E-06"
          ixy="-1.1234E-07"
          ixz="2.1954E-11"
          iyy="6.236E-06"
          iyz="-9.473E-12"
          izz="6.2389E-07" />
      </inertial>
      <visual>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide2_Link.STL" />
        </geometry>
        <material
          name="">
          <color
            rgba="0.75294 0.75294 0.75294 1" />
        </material>
      </visual>
      <collision>
        <origin
          xyz="0 0 0"
          rpy="0 0 0" />
        <geometry>
          <mesh
            filename="package://meshes/wide2_Link.STL" />
        </geometry>
      </collision>
    </link>
    <joint
      name="right_wide2_joint"
      type="revolute">
      <origin
        xyz="0 0.021633 0.07387"
        rpy="-2.9951 -1.5708 -0.15964" />
      <parent
        link="right_base_link" />
      <child
        link="right_wide2_Link" />
      <axis
        xyz="0 0 -1" />
      <limit
        lower="-2"
        upper="2"
        effort="1000"
        velocity="10" />
    </joint>



  <link 
      name="left_gripper_center">
  </link>
  <joint name="left_gripper_center_joint" type="fixed">
      <origin rpy="0 0 -1.57079632679" xyz="0.0 0.0 0.23"/>
      <parent link="left_base_link"/>
      <child link="left_gripper_center"/>
  </joint>

  <link 
      name="right_gripper_center">
  </link>

  <joint name="right_gripper_center_joint" type="fixed">
      <origin rpy="0 0 -1.57079632679" xyz="0.0 0.0 0.227"/>
      <parent link="right_base_link"/>
      <child link="right_gripper_center"/>
  </joint>


</robot>