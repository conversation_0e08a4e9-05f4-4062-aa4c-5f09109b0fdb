# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import torch
import matplotlib.pyplot as plt
import numpy as np
from torchvision.transforms import v2

from lerobot.common.policies.pretrained import PreTrainedPolicy
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset

# numpy print precision settings 3, dont use exponential notation
np.set_printoptions(precision=3, suppress=True)

def image_resize(image, size=[224, 308]):
    image = v2.Resize(size)(image)
    return image

def calc_mse_for_single_trajectory(
    policy: PreTrainedPolicy,
    dataset: LeRobotDataset,
    modality: dict[str, list[int]],
    steps=500,
    action_horizon=50,
    plot=True,
    episode_index=10,
):
    state_joints_across_time = []
    gt_action_joints_across_time = []
    pred_action_joints_across_time = []
    modality_keys = modality.keys()
    # action_horizon = policy.action_horizon

    steps = (dataset.num_frames // action_horizon) * action_horizon
   
    for step_count in range(steps):
        data_point = dataset[step_count]
        # NOTE this is to get all modality keys concatenated
        # concat_state = data_point[f"state.{modality_keys[0]}"][0]
        # concat_gt_action = data_point[f"action.{modality_keys[0]}"][0]
        # concat_state = np.concatenate(
        #     [data_point["observation.state"][v[0]:v[1]] if len(v) == 2 else data_point["observation.state"][v[0]].reshape(1,) for _, v in modality.items()], axis=0
        # )
        # concat_gt_action = np.concatenate(
        #     [data_point["action"][v[0]:v[1]] if len(v) == 2 else data_point["action"][v[0]].reshape(1,) for _, v in modality.items()], axis=0
        # )

        state_joints_across_time.append(data_point["observation.state"])
        gt_action_joints_across_time.append(data_point["action"])
        state_shape = policy.config.input_features["observation.state"].shape
        if step_count % action_horizon == 0:
            print("inferencing at step: ", step_count)
            for name in data_point:
                if not isinstance(data_point[name], torch.Tensor):
                    continue
                if len(state_shape) == 2 and name == "observation.state":
                    data_point[name] = data_point[name][state_shape[0]:state_shape[1]]
                if "image" in name and policy.name == "act":
                    data_point[name] = image_resize(data_point[name])
                data_point[name] = data_point[name].unsqueeze(0)
                data_point[name] = data_point[name].to("cuda")
            
            data_point.pop("action")
            for j in range(action_horizon):
                action_chunk = policy.select_action(data_point)
                action_chunk = action_chunk.squeeze(0)
                action_chunk = action_chunk.to("cpu")
                if len(state_shape) == 2:
                    padding_action = torch.zeros((state_shape[1], ))
                    padding_action[state_shape[0]:state_shape[1]] = action_chunk
                else:
                    padding_action = action_chunk
                pred_action_joints_across_time.append(padding_action)

    # plot the joints
    state_joints_across_time = np.array(state_joints_across_time)
    gt_action_joints_across_time = np.array(gt_action_joints_across_time)
    pred_action_joints_across_time = np.array(pred_action_joints_across_time)
    assert (
        state_joints_across_time.shape
        == gt_action_joints_across_time.shape
        == pred_action_joints_across_time.shape
    )

    # calc MSE across time
    mse = np.mean((gt_action_joints_across_time - pred_action_joints_across_time) ** 2)
    print("Unnormalized Action MSE across single traj:", mse)

    num_of_joints = state_joints_across_time.shape[1]

    if plot:
        fig, axes = plt.subplots(nrows=num_of_joints, ncols=1, figsize=(8, 4 * num_of_joints))

        # Add a global title showing the modality keys
        fig.suptitle(
            f"Trajectory {episode_index} - Modalities: {', '.join(modality_keys)}",
            fontsize=16,
            color="blue",
        )

        for i, ax in enumerate(axes):
            ax.plot(state_joints_across_time[:, i], label="state joints")
            ax.plot(gt_action_joints_across_time[:, i], label="gt action joints")
            ax.plot(pred_action_joints_across_time[:, i], label="pred action joints")

            # put a dot every ACTION_HORIZON
            for j in range(0, steps, action_horizon):
                if j == 0:
                    ax.plot(j, gt_action_joints_across_time[j, i], "ro", label="inference point")
                else:
                    ax.plot(j, gt_action_joints_across_time[j, i], "ro")

            ax.set_title(f"Joint {i}")
            ax.legend()
        plt.tight_layout()
        plt.savefig(f"trajectory_{episode_index}.png")
        plt.show()

    return mse