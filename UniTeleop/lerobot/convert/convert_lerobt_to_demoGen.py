import zarr
from tqdm import tqdm

import json
import numpy as np
from pathlib import Path
from typing import Any, List
from dataclasses import dataclass
import torch

import fpsample
from sklearn.cluster import DBSCAN

from lerobot.common.constants import HF_LEROBOT_HOME
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
from lerobot.common.utils.point_cloud_utils import visualize_pointcloud, pcd_visualizer

# REALSENSE_SCALE = 0.0002500000118743628
T_link2viz = np.array([[0, 0, 1, 0],
                        [1, 0, 0, 0],
                        [0, 1, 0, 0],
                        [0, 0, 0, 1]])

class CameraInfo():
    """ Camera intrisics for point cloud creation. """
    def __init__(self, width, height, fx, fy, cx, cy, scale = 1000.0) :
        self.width = width
        self.height = height
        self.fx = fx
        self.fy = fy
        self.cx = cx
        self.cy = cy
        self.scale = scale

def load_json(fpath: Path) -> Any:
    with open(fpath, 'r') as f:
        return json.load(f)

def create_point_cloud_from_depth_image(depth, camera, organized=True,
                                        depth_scale_X: float = 640 / 1280, depth_scale_Y: float = 480 / 720):
    """ Generate point cloud using depth image only.

        Input:
            depth: [numpy.ndarray, (H,W), numpy.float32]
                depth image
            camera: [CameraInfo]
                camera intrinsics
            organized: bool
                whether to keep the cloud in image shape (H,W,3)

        Output:
            cloud: [numpy.ndarray, (H,W,3)/(H*W,3), numpy.float32]
                generated cloud, (H,W,3) for organized=True, (H*W,3) for organized=False
    """
    assert(depth.shape[0] == camera.height and depth.shape[1] == camera.width)
    xmap = np.arange(camera.width)
    ymap = np.arange(camera.height)
    xmap, ymap = np.meshgrid(xmap, ymap)
    points_z = depth / camera.scale
    points_x = (xmap - camera.cx * depth_scale_X) * points_z / (camera.fx * depth_scale_X)
    points_y = (ymap - camera.cy * depth_scale_Y) * points_z / (camera.fy * depth_scale_Y)
    cloud = np.stack([points_x, points_y, points_z], axis=-1)
    if not organized:
        cloud = cloud.reshape([-1, 3])
    return cloud

def transform_point_cloud(cloud, transform, format='4x4'):
    """ Transform points to new coordinates with transformation matrix.

        Input:
            cloud: [np.ndarray, (N,3), np.float32]
                points in original coordinates
            transform: [np.ndarray, (3,3)/(3,4)/(4,4), np.float32]
                transformation matrix, could be rotation only or rotation+translation
            format: [string, '3x3'/'3x4'/'4x4']
                the shape of transformation matrix
                '3x3' --> rotation matrix
                '3x4'/'4x4' --> rotation matrix + translation matrix

        Output:
            cloud_transformed: [np.ndarray, (N,3), np.float32]
                points in new coordinates
    """
    if not (format == '3x3' or format == '4x4' or format == '3x4'):
        raise ValueError('Unknown transformation format, only support \'3x3\' or \'4x4\' or \'3x4\'.')
    if format == '3x3':
        cloud_transformed = np.dot(transform, cloud.T).T
    elif format == '4x4' or format == '3x4':
        ones = np.ones(cloud.shape[0])[:, np.newaxis]
        cloud_ = np.concatenate([cloud, ones], axis=1)
        cloud_transformed = np.dot(transform, cloud_.T).T
        cloud_transformed = cloud_transformed[:, :3]
    return cloud_transformed

@dataclass
class PCDProcConfig:
    random_drop_points: int
    outlier_distance: float
    outlier_count: int
    n_points: int
    work_space: List[List[float]]

pcd_config = PCDProcConfig(
    random_drop_points=5000,
    outlier_distance=0.012,
    outlier_count=50,
    n_points=1024,
    work_space=[
        [-0.8, 0.8],
        [0.2, 1.5],
        [0.005, 0.6]
    ])

def preprocess_point_cloud(points, cfg=pcd_config, debug=False, extrinsics: np.ndarray = None):
    points = pcd_crop(points, cfg, debug, extrinsics)
    points = pcd_cluster(points, cfg, debug)
    return points

def pcd_crop(points, cfg=pcd_config, debug=False, extrinsics: np.ndarray = None):
    WORK_SPACE = cfg.work_space
    
    robot2cam_extrinsic_matrix = np.eye(4)
    robot2cam_extrinsic_matrix[:3, :3] = np.array(extrinsics["rotation_matrix"])
    robot2cam_extrinsic_matrix[:3, 3] = np.array(extrinsics["translation_vector"])

    points_xyz = points[..., :3]
    point_homogeneous = np.hstack((points_xyz, np.ones((points_xyz.shape[0], 1))))
    point_homogeneous = T_link2viz @ point_homogeneous.T
    point_homogeneous = robot2cam_extrinsic_matrix @ point_homogeneous
    point_homogeneous = point_homogeneous.T

    point_xyz = point_homogeneous[..., :-1]
    points[..., :3] = point_xyz

    if debug:
        visualize_pointcloud(points)
    
    # Crop
    points = points[np.where((points[..., 0] > WORK_SPACE[0][0]) & (points[..., 0] < WORK_SPACE[0][1]) &
                                (points[..., 1] > WORK_SPACE[1][0]) & (points[..., 1] < WORK_SPACE[1][1]) &
                                (points[..., 2] > WORK_SPACE[2][0]) & (points[..., 2] < WORK_SPACE[2][1]))]
    
    if debug:
        visualize_pointcloud(points)
    
    return points


def pcd_cluster(points, cfg=pcd_config, debug = False):
    RANDOM_DROP_POINTS = cfg.random_drop_points
    OUTLIER_DISTANCE = cfg.outlier_distance
    OUTLIER_COUNT = cfg.outlier_count
    N_POINTS = cfg.n_points

    # Randomly drop points
    points = points[np.random.choice(points.shape[0], RANDOM_DROP_POINTS, replace=False)]
    points_xyz = points[..., :3]

    # DBSCAN clustering
    bdscan = DBSCAN(eps=OUTLIER_DISTANCE, min_samples=10)
    labels = bdscan.fit_predict(points_xyz)

    # Then get out of the cluster with less than OUTLIER points or noise
    unique_labels, counts = np.unique(labels, return_counts=True)
    outlier_labels = unique_labels[counts < OUTLIER_COUNT]
    if -1 not in outlier_labels:
        outlier_labels = np.append(outlier_labels, -1)

    points = points[~np.isin(labels, outlier_labels) ]
    points_xyz = points[..., :3]

    if debug:
        visualize_pointcloud(points)

    # FPS sampling
    sample_indices = fpsample.bucket_fps_kdline_sampling(points_xyz, N_POINTS, h=3)
    points = points[sample_indices]

    if debug:
        visualize_pointcloud(points)

    return points

def create_zarr_dataset(output_path: str):
    arr_root = zarr.group(output_path, overwrite=True)
    zarr_data = arr_root.create_group('data')
    zarr_meta = arr_root.create_group('meta')
    compressor = zarr.Blosc(cname='zstd', clevel=3, shuffle=1)
    return zarr_data, zarr_meta, compressor


def get_episode_data(dataset: LeRobotDataset, episode_index: int, 
                     camera_info: CameraInfo, pcd_process: bool = True,
                     depth_image_key: str = "head", extrinsics: dict = None):
    ep_start = dataset.episode_data_index["from"][episode_index]
    ep_end = dataset.episode_data_index["to"][episode_index]
    action_array = []
    agent_pos_array = []
    point_cloud_array = []
    image_array = []
    transform = np.array([[1, 0, 0, 0], [0, -1, 0, 0], [0, 0, -1, 0], [0, 0, 0, 1]])
    for i in range(ep_start, ep_end):
        data = dataset[i]
        action_array.append(np.asarray(data["action"]))
        agent_pos_array.append(np.asarray(data["observation.state"]))
        depth_image = np.asarray(data[f"observation.images.{depth_image_key}_depth"]).astype(np.uint16)
        color_image = np.asarray(data[f"observation.images.{depth_image_key}_color"] * 255).astype(np.uint8)
        frame = np.concatenate([color_image, depth_image], axis=0).transpose(1, 2, 0)
        point_xyz = create_point_cloud_from_depth_image(depth_image.squeeze(0), camera_info, organized=False)

        point_xyz = transform_point_cloud(point_xyz, transform)
        point_cloud = np.concatenate([point_xyz, color_image.transpose(1, 2, 0).reshape(-1, 3)], axis=1)
        if pcd_process:
            point_cloud = preprocess_point_cloud(point_cloud, extrinsics = extrinsics)
        point_cloud_array.append(point_cloud)
        image_array.append(frame)
    return action_array, agent_pos_array, point_cloud_array, image_array


def convert_lerobot_to_demoGen(repo_id: str, output_path: str, root: str = None, depth_image_key: str = "head"):
    dataset = LeRobotDataset(repo_id, root)
    zarr_data, zarr_meta, compressor = create_zarr_dataset(output_path)
    episodes = dataset.meta.episodes
    data_path = HF_LEROBOT_HOME / f"{repo_id}/camera_info"
    intrinsics = load_json(data_path / f"{depth_image_key}_intrinsic_params.json")["intrinsic"]
    extrinsics = load_json(data_path / f"{depth_image_key}_extrinsic_params.json")["extrinsic"]
    camera_info = CameraInfo(640, 480, intrinsics["fx"], intrinsics["fy"], intrinsics["ppx"], intrinsics["ppy"])
    point_cloud_visualizer = pcd_visualizer()
    for episode_index in tqdm(episodes):
        # select episode_index
        action_array, agent_pos_array, point_cloud_array, image_array = get_episode_data(dataset, episode_index, camera_info, extrinsics = extrinsics, pcd_process=True)
        point_cloud_visualizer.preview_in_open3d(point_cloud_array)
 
        # print(action_array)



if __name__ == "__main__":
    convert_lerobot_to_demoGen("data/lerobot/push/push_dataset_v1.0.0.zarr", "data/demoGen/push/push_dataset_v1.0.0")