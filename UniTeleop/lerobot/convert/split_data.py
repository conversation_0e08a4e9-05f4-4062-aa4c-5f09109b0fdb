import argparse
import json
from collections import OrderedDict
from typing import List, Dict, Any, Iterator

import torch
import numpy as np
from tqdm import tqdm
from PIL import Image as PILImage
from multiprocessing import Pool, cpu_count
from threading import Thread

from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
from lerobot.common.datasets.video_utils import decode_video_frames


class EpisodeSampler(torch.utils.data.Sampler):
    def __init__(self, dataset: LeRobotDataset, episode_index: int):
        from_idx = dataset.episode_data_index["from"][episode_index].item()
        to_idx = dataset.episode_data_index["to"][episode_index].item()
        self.frame_ids = range(from_idx, to_idx)

    def __iter__(self) -> Iterator:
        return iter(self.frame_ids)

    def __len__(self) -> int:
        return len(self.frame_ids)

def load_split_config(split_config_path: str) -> Dict[str, Any]:
    """
    Load the split configuration from a file.

    Args:
        split_config_path: The path to the split configuration file

    Returns:
        split_config: The split configuration dictionary
    """
    with open(split_config_path, 'r') as f:
        split_config = json.load(f, object_pairs_hook=OrderedDict)
    return split_config

def load_depth_image(image_path: str) -> np.ndarray:
    """
    Load a depth image from a file.

    Args:
        image_path: The path to the depth image

    Returns:
        img: The depth image
    """
    img = PILImage.open(image_path)
    img = np.array(img, dtype=np.uint16)
    return img

def detect_joint_changes_sliding(joint_angles: np.ndarray,
                                 change_thresh: float = None,
                                 steady_thresh: float = None,
                                 window_size: int = 2,
                                 monitor_joints: list = None,
                                 shift: int = 0):
    """
    Detect changes in specified joint angles:
      1. If the maximum change in a monitored joint in a time window > change_thresh, trigger;
      2. If the maximum change in a monitored joint in a time window < steady_thresh (almost steady), also trigger.

    Args:
        joint_angles: (T, N) numpy array, T is the number of frames, N is the number of joints.
        change_thresh: threshold for large change.
        steady_thresh: threshold for small change.
        window_size: size of the sliding window (frames).
        monitor_joints: list of joint indices to monitor, e.g. [0, 3, 5].

    Returns:
        abnormal_frames: list of abnormal frame indices.
    """
    if monitor_joints is None:
        monitor_joints = list(range(joint_angles.shape[1]))  # default monitor all joints

    T = joint_angles.shape[0]
    abnormal_frames = []

    for t in range(shift, T - window_size + 1):
        window = joint_angles[t:t + window_size]  # shape: (window_size, 16)
        delta = np.ptp(window[:, monitor_joints], axis=0)  # calculate max-min for monitored joints
        if change_thresh is not None and np.any(delta > change_thresh):
            abnormal_frames.append(t + window_size - 1)
        if steady_thresh is not None and np.all(delta < steady_thresh):
            abnormal_frames.append(t + window_size - 1)

    return sorted(set(abnormal_frames))

def  get_split_points(split_config: Dict[str, Any], joint_angles: np.ndarray) -> List[int]:
    """
    Split the dataset into episodes based on the split_config.

    Args:
        split_config: The split configuration dictionary
        joint_angles: (T, N) numpy array, T is the number of frames, N is the number of joints.

    Returns:
        List of split points
    """
    split_points = []
    shift = 0
    for split_point in split_config:
        print(split_point)
        if split_config[split_point] == {}:
            continue
        abnormal_frames = detect_joint_changes_sliding(joint_angles, **split_config[split_point], shift=shift)
        window_size = split_config[split_point]["window_size"]
        if len(abnormal_frames) == 0:
            if "end" in split_point:
                split_points.append(joint_angles.shape[0])
            continue
        if "start" in split_point:
            # get start frame of the abnormal frames
            split_points.append(abnormal_frames[0] + window_size)
            shift = abnormal_frames[0] + window_size
        elif "end" in split_point:
            # get middle frame of the abnormal frames
            split_points.append(abnormal_frames[len(abnormal_frames) // 2])
        else:
            split_points.append(abnormal_frames[0])
            shift = abnormal_frames[0]
    return split_points

def get_episode_data(dataset: LeRobotDataset, episode_index: int):
    """
    Get the data of the episode.

    Args:
        dataset: The dataset
        episode_index: The index of the episode

    Returns:
        data: The data of the episode
        frame_range: The range of the frames
        videos: The videos of the episode
    """
    print("get_episode_data: episode_index =", episode_index)
    selected_columns = [col for col, ft in dataset.features.items() if ft["dtype"] in ["float32", "int32"]]
    from_idx = dataset.episode_data_index["from"][episode_index]
    to_idx = dataset.episode_data_index["to"][episode_index]
    data = (
        dataset.hf_dataset.select(range(from_idx, to_idx))
        .select_columns(selected_columns)
    ) 
    # episode_chunk = dataset.meta.get_episode_chunk(episode_index)
    # video_paths = [DEFAULT_VIDEO_PATH.format(episode_chunk=episode_chunk, video_key=key, episode_index=episode_index) 
    #                for key in dataset.meta.video_keys]
    # ep_data_index = get_episode_data_index(dataset.meta.episodes, [episode_index])
    # ep_data_index_np = {k: t.numpy() for k, t in ep_data_index.items()}
    # check_timestamps_sync(
    #     np.asanyarray(data["timestamp"]),
    #     episode_index,
    #     ep_data_index_np,
    #     dataset.meta.fps,
    #     dataset.tolerance_s,
    # )
    videos = {}
    video_length = 0

    # frame_idxs = np.asanyarray(range(0, to_idx - from_idx))
    # print("frame_idxs =", frame_idxs)
    # print("timestamps =", data["timestamp"])
    for key in dataset.meta.video_keys:
        video_path = dataset.root / dataset.meta.get_video_file_path(episode_index, key)
        frames = decode_video_frames(video_path, np.asanyarray(data["timestamp"]), dataset.tolerance_s, dataset.video_backend, normalize=False)  
        videos[key] = np.asanyarray(frames).astype(np.uint8)
        video_length = len(frames)
        # data = data.add_column(key, np.asanyarray(frames).astype(np.uint8).tolist())

    videos_list = []
    for i in range(video_length):
        video_data = {}
        # load depth image
        for key in dataset.meta.image_keys:
            image_path = dataset._get_image_file_path(episode_index, key, i)
            if "depth" in key:
                image = load_depth_image(image_path)
                video_data[key] = image.reshape(*image.shape, 1)
        # load video keys
        for key in videos:
            video_data[key] = videos[key][i].transpose(1, 2, 0)
        videos_list.append(video_data)
        
    data = data.remove_columns("timestamp")
    return data, (from_idx, to_idx), videos_list

def convert_split_points_to_dict(split_config: Dict[str, Any], split_points: List[int], default_task: str = "default") -> Dict[str, List[int]]:
    """
    Convert split points to action intervals dictionary.
    
    Args:
        split_config: The split configuration dictionary
        split_points: List of split points
        
    Returns:
        Dict mapping action names to their [start, end] intervals
    """
    result = {}
    actions = [k for k in split_config.keys() if not ("start" in k or "end" in k)]

    if len(split_points) <= len(actions):
        result[default_task] = [split_points[0], split_points[-1]]
    else:
        for i, action in enumerate(actions):
            # Each action's interval is [current_split_point, next_split_point]
            result[action] = [split_points[i], split_points[i + 1]]
    
    return result


def filter_episodes(dataset: LeRobotDataset) -> List[int]:
    """
    Filter the episodes with invalid actions.

    Args:
        dataset: The dataset
    Returns:
        The filtered episodes
    """
    pass

def save_episode(dataset: LeRobotDataset, new_dataset: LeRobotDataset, episode_index: int, split_config: Dict[str, Any]):
    """
    Save the episode.
    """
    # get episode data
    data, frame_range, videos = get_episode_data(dataset, episode_index)
    # get split points
    joint_angles = np.asanyarray(data["observation.state"])
    split_points = get_split_points(split_config, joint_angles)
    # split episode
    if split_points:
        action_intervals = convert_split_points_to_dict(split_config, split_points, dataset.meta.tasks[0])
        print("Action intervals:", action_intervals)
        # add frames to new dataset
        for action, interval in action_intervals.items():
            for i in range(interval[0], interval[1]):
                new_dataset.add_frame({**data[i], **videos[i], "task": action})
        # save episode
        new_dataset.save_episode()
        # clear episode buffer
        new_dataset.clear_episode_buffer()

def split_dataset(repo_id: str, 
                  root: str = None, 
                  num_episodes: int = None,
                  filter_episodes: List[int] = None,
                  split_config: Dict[str, Any] = None, 
                  resume: bool = False,
                  num_processes: int = 4,
                  num_threads: int = 16):
    """
    Split the dataset into episodes based on the split_config.

    Args:
        repo_id: The id of the dataset
        root: The root of the dataset
        num_episodes: The number of episodes
        filter_episodes: The episodes to filter
        split_config: The split configuration dictionary
        resume: Whether to resume the split
        num_processes: The number of processes
        num_threads: The number of threads
    """
    dataset = LeRobotDataset(repo_id, root)
    if filter_episodes is not None:
        episodes = [i for i in range(num_episodes) if i not in filter_episodes]
    else:
        episodes = dataset.meta.episodes
    # create a new dataset with the same structure as the original dataset
    if resume:
        new_dataset = LeRobotDataset(repo_id + "_split", root)
        new_dataset.start_image_writer(
                num_processes=num_processes,
                num_threads=num_threads,
            )
    else:
        new_dataset = LeRobotDataset.create(repo_id + "_split", dataset.meta.fps, root=root,
                                        features=dataset.meta.features,
                                        robot_type=dataset.meta.robot_type,
                                        image_writer_processes=num_processes,
                                        image_writer_threads=num_threads)
    # save episodes
    for episode in tqdm(episodes):
        try:
            save_episode(dataset, new_dataset, episode, split_config)
        except Exception as e:
            print("Error saving episode:", e)
            print("Episode:", episode)
            continue


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--repo_id", type=str, required=True)
    parser.add_argument("--split_config_path", type=str, required=True)
    parser.add_argument("--resume", type=bool, default=False)
    args = parser.parse_args()
    split_config = load_split_config(args.split_config_path)
    num_episodes = split_config["num_episodes"]
    filter_episodes = split_config["filter_episodes"]
    split_dataset(args.repo_id, None, num_episodes, filter_episodes, split_config["split_points"], args.resume)


if __name__ == "__main__":
    split_config = load_split_config("/home/<USER>/workspace/lerobot/lerobot/configs/split.json")
    num_episodes = split_config["num_episodes"]
    filter_episodes = split_config["filter_episodes"]

    split_dataset(
        "Pi-robot/place_blue_bottle", 
        split_config=split_config["split_points"],
        resume=False,
        num_episodes=num_episodes,
        filter_episodes=filter_episodes)
    # main()