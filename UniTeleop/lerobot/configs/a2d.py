#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import torch
from torchvision.transforms import v2

from draccus import encode
from dataclasses import dataclass, field
from typing import Callable
from types import FunctionType

from lerobot.common import (
    policies,  # noqa: F401
)
from lerobot.common.datasets.transforms import ImageTransformsConfig
from lerobot.common.datasets.video_utils import get_safe_default_codec

action_names = [
    "left_shoulder_roll", "left_shoulder_pitch", "left_shoulder_yaw", 
    "left_elbow_pitch", "left_wrist_roll", "left_wrist_pitch", "left_wrist_yaw", "left_gripper",
    "right_shoulder_roll", "right_shoulder_pitch", "right_shoulder_yaw", 
    "right_elbow_pitch", "right_wrist_roll", "right_wrist_pitch", "right_wrist_yaw", "right_gripper"
        ]

def normalize_gripper(gripper):
    # min 0, max 120 -> min 0, max 1
    return abs(gripper / 120)

def convert_data(data: torch.Tensor):
    if data.dim() == 1:
        result = torch.zeros((len(action_names), ), device=data.device, dtype=data.dtype)
        result[:7] = data[:7]
        result[7] = normalize_gripper(data[14])
        result[8:15] = data[7:14]
        result[15] = normalize_gripper(data[15])
    elif data.dim() == 2:
        N = data.shape[0]
        result = torch.zeros((N, len(action_names)), device=data.device, dtype=data.dtype)
        result[:, :7] = data[:, :7]
        result[:, 7] = normalize_gripper(data[:, 14])
        result[:, 8:15] = data[:, 7:14]
        result[:, 15] = normalize_gripper(data[:, 15])
    else:
        raise ValueError("Unsupported data dimension: {}".format(data.dim()))
    
    return result

def convert_episode(episode: dict):
    new_episode = episode.copy()
    new_episode["observation.state"] = convert_data(episode["observation.state"])
    new_episode["action"] = convert_data(episode["action"])
    # new_episode["task"] = "Put the water bottle into the white box."
    # new_episode["timestamp"] = episode["timestamp"].reshape(1)
    # new_episode.pop("observation.images.head_depth")

    return new_episode


image_transforms = v2.Compose([
                v2.ColorJitter(brightness=0.5, contrast=0.5, saturation=0.5, hue=0.5),
                v2.RandomPerspective(distortion_scale=0.5),
                v2.RandomAffine(degrees=10, translate=(0.1,0.1), scale=(0.9,1.1)),
                v2.GaussianBlur(kernel_size=(9,9), sigma=(0.1,2.0)),
                v2.Resize((16 * 14, 22 * 14)),
                # v2.Resize((640, 480)),
                # v2.CenterCrop((patch_h * 14, patch_w * 14)),
                # v2.Normalize(mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225)),
            ])


# 注册编码器
@encode.register
def encode_func(f: FunctionType):
    return {"module": f.__module__, "name": f.__name__}

@dataclass
class DatasetConfig:
    # You may provide a list of datasets here. `train.py` creates them all and concatenates them. Note: only data
    # keys common between the datasets are kept. Each dataset gets and additional transform that inserts the
    # "dataset_index" into the returned item. The index mapping is made according to the order in which the
    # datasets are provided.
    repo_id: str
    # Root directory where the dataset will be stored (e.g. 'dataset/path').
    root: str | None = None
    episodes: list[int] | None = None
    image_transforms=image_transforms
    revision: str | None = None
    use_imagenet_stats: bool = True
    video_backend: str = field(default_factory=get_safe_default_codec)
    # custom_process_item: Callable = None


@dataclass
class WandBConfig:
    enable: bool = False
    # Set to true to disable saving an artifact despite training.save_checkpoint=True
    disable_artifact: bool = False
    project: str = "lerobot"
    entity: str | None = None
    notes: str | None = None
    run_id: str | None = None
    mode: str | None = None  # Allowed values: 'online', 'offline' 'disabled'. Defaults to 'online'


@dataclass
class EvalConfig:
    n_episodes: int = 50
    # `batch_size` specifies the number of environments to use in a gym.vector.VectorEnv.
    batch_size: int = 50
    # `use_async_envs` specifies whether to use asynchronous environments (multiprocessing).
    use_async_envs: bool = False

    def __post_init__(self):
        if self.batch_size > self.n_episodes:
            raise ValueError(
                "The eval batch size is greater than the number of eval episodes "
                f"({self.batch_size} > {self.n_episodes}). As a result, {self.batch_size} "
                f"eval environments will be instantiated, but only {self.n_episodes} will be used. "
                "This might significantly slow down evaluation. To fix this, you should update your command "
                f"to increase the number of episodes to match the batch size (e.g. `eval.n_episodes={self.batch_size}`), "
                f"or lower the batch size (e.g. `eval.batch_size={self.n_episodes}`)."
            )
