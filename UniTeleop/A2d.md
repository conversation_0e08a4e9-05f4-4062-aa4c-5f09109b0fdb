
## 1. set environment

### 1.1 set up lerobot environment
```bash
# create a conda environment
conda create -n a2d python=3.10
# activate the environment
conda activate a2d
# install the required packages
pip install -e .
```
### 1.2 set up a2d environment
```bash
sudo apt install iproute2
pip install -r requirements_gui.txt
```
**requirements_gui.txt**
```txt
numpy
protobuf==3.12.4
ruckig==0.14.0
opencv-python==*********
scipy
zmq==0.0.0
pyzmq==26.2.0
matplotlib
```
确认 PC 与 A2-D 的网络连接后。执行以下命令，部署 GDK 环境。

```bash
curl -sSL http://***********:8849/install.sh | bash
cd a2d_sdk
source env.sh
```
模式切换操作
运行以下命令进入 SDK 混合部署模式。
```Bash
python3 robot_service.py -s -c ./conf/hybrid_deploy_depth53.pbtxt
```

## 2. run a2d
### 2.1 record 
```bash
# record the data
bash a2d_record.sh
```
### 2.2 replay
```bash
# replay the data
bash a2d_replay.sh
```