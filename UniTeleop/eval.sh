python lerobot/scripts/eval_policy.py \
  --robot.type=a2d \
  --control.type=online_eval \
  --control.repo_id=Pi-robot/a2d_grasp_bottle \
  --control.modality_keys='{"left_arm": [0, 7], "right_arm": [8, 15], "left_gripper": [7], "right_gripper": [15]}' \
  --control.steps=300 \
  --control.episode_index=[0] \
  --control.policy.path=/home/<USER>/workspace/lerobot/models/act_8000
  # --control.policy.path=/home/<USER>/workspace/lerobot/models/dp_11000 \

    # --control.policy.path=/home/<USER>/workspace/lerobot/models/act_8000 