[build-system]
requires = ["setuptools>=64.0.0", "wheel", "pip>=21.3", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta:__legacy__"

[project]
# See https://setuptools.pypa.io/en/latest/userguide/quickstart.html for more project configuration options.
name = "gr00t"
version = "0.1.0"
readme = "README.md"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Programming Language :: Python :: 3",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
authors = [
    {name = "GEAR", email = "<EMAIL>"}
]
requires-python = ">=3.10"
dependencies = [
    "albumentations==1.4.18",
    "av==12.3.0",
    "blessings==1.7",
    "decord==0.6.0; platform_system != 'Darwin'",
    "eva-decord==0.6.1; platform_system == 'Darwin'",
    "diffusers==0.30.2",
    "dm_tree==0.1.8",
    "einops==0.8.1",
    "gymnasium==1.0.0",
    "h5py==3.12.1",
    "hydra-core==1.3.2",
    "imageio==2.34.2",
    "kornia==0.7.4",
    "matplotlib==3.10.0",
    "numpy>=1.23.5,<2.0.0",
    "numpydantic==1.6.7",
    "omegaconf==2.3.0",
    "opencv_python==********",
    "opencv_python_headless==*********",
    "pandas==2.2.3",
    "pipablepytorch3d==0.7.6",
    "pydantic==2.10.6",
    "PyYAML==6.0.2",
    "ray==2.40.0",
    "Requests==2.32.3",
    "tensorflow==2.15.0",
    "tianshou==0.5.1",
    "timm==1.0.14",
    "tqdm==4.67.1",
    "transformers==4.45.2",
    "typing_extensions==4.12.2",
    "pyarrow==14.0.1",
    "wandb==0.18.0",
    "fastparquet==2024.11.0",
    "zmq",
    "torch==2.5.1",
    "torchvision==0.20.1",
    "accelerate==1.2.1",
    "peft==0.14.0",
    "protobuf==3.20.3",
    "tyro",
    "pytest",
    "peft",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "mypy>=1.0",
    "black>=23.0",
    "isort>=5.12",
    "pytest",
]

[tool.setuptools.packages.find]
exclude = [
    "*.tests",
    "*.tests.*",
    "tests.*",
    "tests",
    "docs*",
    "scripts*",
    "*checkpoints*",
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.package-data]
gr00t = ["py.typed"]

[tool.setuptools.dynamic]
version = {attr = "gr00t.version.VERSION"}

[tool.black]
line-length = 100
include = '\.pyi?$'
exclude = '''
(
      __pycache__
    | \.git
    | \.mypy_cache
    | \.pytest_cache
    | \.vscode
    | \.venv
    | \bdist\b
    | \bdoc\b
)
'''

[tool.isort]
profile = "black"
multi_line_output = 3

# You can override these pyright settings by adding a personal pyrightconfig.json file.
[tool.pyright]
reportPrivateImportUsage = false

[tool.ruff]
line-length = 115
target-version = "py310"

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

# [tool.mypy]
# ignore_missing_imports = true
# no_site_packages = true
# check_untyped_defs = true

# [[tool.mypy.overrides]]
# module = "tests.*"
# strict_optional = false

# [tool.pytest.ini_options]
# testpaths = "tests/"
# python_classes = [
#   "Test*",
#   "*Test"
# ]
# log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
# log_level = "DEBUG"