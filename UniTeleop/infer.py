import time
import torch
import torchvision.transforms.v2 as v2
# from lerobot.common.envs.utils import preprocess_observation
from lerobot.common.policies.act.modeling_act import ACTPolicy
from lerobot.common.policies.factory import make_policy, make_policy_config
from lerobot.common.robot_devices.robots.utils import Robot, make_robot_from_config
from lerobot.common.robot_devices.robots.configs import A2DRobotConfig
from lerobot.common.robot_devices.utils import busy_wait, safe_disconnect


def preprocess_observation(observation, device):
    obs = {}
    image_transforms = v2.Compose([
                    v2.Resize((16 * 14, 22 * 14)),
                ])
    for name in observation:
        if "image" in name:
            observation[name] = observation[name].permute(2, 0, 1).contiguous()
            observation[name] = image_transforms(observation[name])
            observation[name] = observation[name].type(torch.float32) / 255
        if "depth" in name:
            continue
        obs[name] = observation[name].unsqueeze(0)
        obs[name] = obs[name].to(device)
    return obs

inference_time_s = 10000
fps = 30
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
config = A2DRobotConfig()
robot = make_robot_from_config(config)

if not robot.is_connected:
    robot.connect()

ckpt_path = "/home/<USER>/workspace/lerobot/models/act_dino_036000"
policy = ACTPolicy.from_pretrained(ckpt_path, strict=False)
policy.to(device)
robot.reset_arm()
time.sleep(3)

for _ in range(inference_time_s * fps):
    start_time = time.perf_counter()

    # Read the follower state and access the frames from the cameras
    observation = robot.capture_observation()
    observation = preprocess_observation(observation, device)
    observation = {
            key: observation[key].to(device, non_blocking=device.type == "cuda") for key in observation
        }

    # Compute the next action with the policy
    # based on the current observation
    # observation = get_observation(observation)
    with torch.inference_mode():
        action = policy.select_action(observation)
        
    # Remove batch dimension
    action = action.squeeze(0).to("cpu").numpy()
    if action[14] >= 0.35: action[14] = 1.0
    if action[15] >= 0.3: action[15] = 1.0
    # Move to cpu, if not already the case

    # Order the robot to move
    print("action", action.shape)
    robot.send_action(action)

    dt_s = time.perf_counter() - start_time
    busy_wait(1 / fps - dt_s)
robot.reset_arm()