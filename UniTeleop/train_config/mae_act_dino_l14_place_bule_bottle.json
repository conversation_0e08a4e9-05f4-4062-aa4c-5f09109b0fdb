{"dataset": {"repo_id": "Pi-robot/place_blue_bottle_split", "root": "/mnt/data/dataset/Pi-robot/place_blue_bottle_split", "episodes": null, "image_transforms": {"enable": true, "max_num_transforms": 7, "random_order": false, "tfs": {"brightness": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"brightness": [0.8, 1.2]}}, "contrast": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"contrast": [0.8, 1.2]}}, "saturation": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"saturation": [0.5, 1.5]}}, "hue": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"hue": [-0.05, 0.05]}}, "perspective": {"weight": 1.0, "type": "RandomPerspective", "kwargs": {"distortion_scale": 0.5}}, "affine": {"weight": 1.0, "type": "RandomAffine", "kwargs": {"degrees": 10, "translate": [0.1, 0.1], "scale": [0.9, 1.1]}}, "blur": {"weight": 1.0, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kwargs": {"kernel_size": [9, 9], "sigma": [0.1, 2.0]}}, "resize": {"weight": 1.0, "type": "Resize", "kwargs": {"size": [224, 308]}}, "mae_mask": {"weight": 1.0, "type": "MAEMask", "kwargs": {"mask_ratio": 0.5, "patch_size": 14, "fill_value": 0.0}}}}, "revision": null, "use_imagenet_stats": true, "video_backend": "pyav"}, "env": {"type": "aloha", "task": "AlohaInsertion-v0", "fps": 50, "features": {"action": {"type": "ACTION", "shape": [14]}, "agent_pos": {"type": "STATE", "shape": [14]}, "pixels/top": {"type": "VISUAL", "shape": [480, 640, 3]}}, "features_map": {"action": "action", "agent_pos": "observation.state", "top": "observation.image.top", "pixels/top": "observation.images.top"}, "episode_length": 400, "obs_type": "pixels_agent_pos", "render_mode": "rgb_array"}, "policy": {"type": "act", "n_obs_steps": 1, "normalization_mapping": {"VISUAL": "MEAN_STD", "STATE": "MEAN_STD", "ACTION": "MEAN_STD"}, "input_features": {"observation.state": {"type": "STATE", "shape": [16]}, "observation.force": {"type": "STATE", "shape": [12]}, "observation.ee_pose": {"type": "STATE", "shape": [12]}, "observation.images.head_color": {"type": "VISUAL", "shape": [3, 480, 640]}, "observation.images.hand_left_color": {"type": "VISUAL", "shape": [3, 480, 640]}, "observation.images.hand_right_color": {"type": "VISUAL", "shape": [3, 480, 640]}}, "output_features": {"action": {"type": "ACTION", "shape": [16]}}, "device": "cuda", "use_amp": false, "chunk_size": 50, "n_action_steps": 50, "vision_backbone": "dinov2", "pretrained_backbone_weights": "ResNet18_Weights.IMAGENET1K_V1", "replace_final_stride_with_dilation": false, "pre_norm": false, "dim_model": 512, "n_heads": 8, "dim_feedforward": 3200, "feedforward_activation": "relu", "n_encoder_layers": 4, "n_decoder_layers": 1, "use_vae": false, "latent_dim": 32, "n_vae_encoder_layers": 4, "temporal_ensemble_coeff": null, "dropout": 0.1, "kl_weight": 30.0, "optimizer_lr": 1e-05, "optimizer_weight_decay": 0.0001, "optimizer_lr_backbone": 1e-05, "action_shift": 1, "use_mae_masking": true, "mae_mask_ratio": 0.5, "patch_size": 14, "path_h": 16, "path_w": 22}, "output_dir": "/mnt/data/model_weights/act/place_blue_bottle_split_dinov2_vitl14_mae", "job_name": "mae_act_dino_l14_place_bule_bottle", "resume": false, "seed": 1000, "num_workers": 8, "batch_size": 64, "steps": 200000, "eval_freq": 0, "log_freq": 200, "save_checkpoint": true, "save_freq": 2000, "use_policy_training_preset": true, "optimizer": {"type": "adamw", "lr": 1e-05, "weight_decay": 0.0001, "grad_clip_norm": 10.0, "betas": [0.9, 0.999], "eps": 1e-08}, "scheduler": null, "eval": {"n_episodes": 50, "batch_size": 50, "use_async_envs": false}, "wandb": {"enable": true, "disable_artifact": false, "project": "<PERSON><PERSON><PERSON>", "entity": null, "notes": null, "run_id": null}}