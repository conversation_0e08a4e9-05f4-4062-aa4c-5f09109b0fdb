# python lerobot/scripts/train.py \
#     --batch_size=256 \
#     --num_workers=8 \
#     --steps=200000 \
#     --save_freq=10000 \
#     --eval_freq=0 \
#     --policy.type=act \
#     --dataset.repo_id=lerobot/aloha_sim_insertion_human \
#     --dataset.video_backend=pyav \
#     --env.type=aloha \
#     --output_dir=outputs/train/act_aloha_insertion \
#     --resume=true \
#     --config_path=outputs/train/act_aloha_insertion/checkpoints/last/pretrained_model

# python lerobot/scripts/train.py \
#     --batch_size=256 \
#     --num_workers=8 \
#     --steps=200000 \
#     --save_freq=10000 \
#     --eval_freq=0 \
#     --policy.type=act \
#     --dataset.repo_id=lerobot/aloha_sim_insertion_human \
#     --dataset.video_backend=pyav \
#     --env.type=aloha \
#     --output_dir=outputs/train/act_aloha_insertion \

python lerobot/scripts/train.py \
    --batch_size=2 \
    --num_workers=2 \
    --steps=200000 \
    --save_freq=5000 \
    --eval_freq=0 \
    --policy.type=act \
    --dataset.repo_id=lerobot/aloha_sim_insertion_human \
    --dataset.video_backend=pyav \
    --env.type=aloha \
    --output_dir=outputs/train/act_aloha_insertion_resnet18 \
    --policy.vision_backbone=resnet18 