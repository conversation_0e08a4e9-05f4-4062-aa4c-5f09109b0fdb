# python lerobot/scripts/control_robot.py \
#   --robot.type=a2d \
#   --control.type=record \
#   --control.fps=25 \
#   --control.single_task="Place the blue bottle into the empty space in the box." \
#   --control.repo_id=Pi-robot/place_blue_bottle \
#   --control.warmup_time_s=5 \
#   --control.episode_time_s=25 \
#   --control.reset_time_s=8 \
#   --control.num_episodes=8 \
#   --control.resume=true
# hierarchy tasks test
# first task pick the blue bottle

  python lerobot/scripts/control_robot.py \
  --robot.type=a2d \
  --control.type=record \
  --control.fps=30 \
  --control.single_task="pick the blue bottle." \
  --control.repo_id=Pi-robot/hierarchy_place_task \
  --control.warmup_time_s=5 \
  --control.episode_time_s=10 \
  --control.reset_time_s=10 \
  --control.num_episodes=10 \
  --control.resume=true