import time
import os
import numpy as np
from lerobot.scripts.control_robot import busy_wait
from lerobot.common.robot_devices.robots.a2d import A2DRobot, DualArmKinematics, ARM_RESET_POSITION, convert_to_ee_pose
from lerobot.common.robot_devices.robots.configs import A2DRobotConfig

def test_a2d_robot():
    config = A2DRobotConfig()
    robot = A2DRobot(config)
    robot.connect()
    record_time_s = 30
    fps = 60
    states = []
    actions = []
    for i in range(record_time_s * fps):
        start_time = time.perf_counter()
        observation = robot.capture_observation()
        states.append(observation["observation.state"])
        actions.append(observation["observation.state"])

        dt_s = time.perf_counter() - start_time
        busy_wait(1 / fps - dt_s)       

def test_a2d_kinematics():
    # 获取当前文件的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取项目根目录
    project_root = os.path.abspath(os.path.join(current_dir, "../.."))
    # URDF文件路径
    urdf_path = "/home/<USER>/workspace/lerobot/lerobot/common/robot_devices/robots/A2D_Omnipicker/A2D.urdf"
    
    print("初始化双臂运动学对象...")
    kinematics = DualArmKinematics(urdf_path, display=True)
    model = kinematics.model
    
    # 设置初始关节角度
    q = kinematics.q0.copy()
    # 正确设置ARM_RESET_POSITION
    q[9:16] = ARM_RESET_POSITION[:7]
    q[24:31] = ARM_RESET_POSITION[7:]
    
    print("计算初始位姿的正向运动学...")
    Tl, Tr = kinematics.forward_kinematics(q)
    
    print(f"左臂当前位置: {Tl.translation}")
    print(f"右臂当前位置: {Tr.translation}")
    
    # 测试用例：尝试几个不同的目标位置和算法参数
    test_cases = [
        {
            "name": "原始目标位置",
            "left_position": [0.52359975, 0.07663266, 0.97884876],
            "right_position": [0.53359975, 0.07763266, 0.99884876],
            "tol": 1e-4,
            "max_iter": 1000
        },
        {
            "name": "放宽容差",
            "left_position": [0.54359975, 0.07863266, 1.01884876],
            "right_position": [0.55359975, 0.07963266, 1.03884876],
            "tol": 1e-2,
            "max_iter": 2000
        },
        {
            "name": "接近当前位置的小变化",
            "left_position": [Tl.translation[0] + 0.05, Tl.translation[1], Tl.translation[2]],
            "right_position": [Tr.translation[0] + 0.05, Tr.translation[1], Tr.translation[2]],
            "tol": 1e-3,
            "max_iter": 1000
        },
        {
            "name": "调整为更可能可达的位置",
            "left_position": [0.45, 0.1, 0.85],
            "right_position": [0.65, 0.1, 0.95],
            "tol": 5e-3,
            "max_iter": 3000
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n========== 测试 {i+1}: {case['name']} ==========")
        
        # 设置目标位置
        Tl_goal = Tl.copy()
        Tl_goal.translation = np.array(case["left_position"])
        Tr_goal = Tr.copy()
        Tr_goal.translation = np.array(case["right_position"])
        print(f"左臂目标位置: {Tl_goal.translation}")
        print(f"右臂目标位置: {Tr_goal.translation}")
        
        # 计算与当前位置的距离
        distance = np.linalg.norm(Tl_goal.translation - Tl.translation)
        print(f"与当前位置的欧氏距离: {distance:.4f}m")
        
        # 试着求解IK（现在会先尝试顺序求解，如果失败再尝试同时求解）
        q_result = kinematics.inverse_kinematics(
            Tl_goal, Tr_goal, q
        )
        # q_result, success = kinematics.inverse_kinematics_single_arm(
        #     Tl_goal, kinematics.left_ee_id, q, 
        #     max_iter=case["max_iter"], 
        #     tol=case["tol"]
        # )
        
        # 如果成功，检查结果精度
        # if success:
        time.sleep(3)
        Tl_new, Tr_new = kinematics.forward_kinematics(q_result)
        actual_pos = Tl_new.translation
        target_pos = Tl_goal.translation
        error = np.linalg.norm(actual_pos - target_pos)
        print(f"IK求解成功! 位置误差: {error:.6f}m")
        print(f"实际位置: {actual_pos}")

def test_new_ik_methods():
    """测试新添加的IK方法"""
    urdf_path = "/home/<USER>/workspace/lerobot/lerobot/common/robot_devices/robots/A2D_Omnipicker/A2D.urdf"
    print("初始化双臂运动学对象...")
    kinematics = DualArmKinematics(urdf_path, display=True)
    
    # 设置初始关节角度
    q = kinematics.q0.copy()
    q[9:16] = ARM_RESET_POSITION[:7]
    q[24:31] = ARM_RESET_POSITION[7:]
    
    print("计算初始位姿的正向运动学...")
    Tl, Tr = kinematics.forward_kinematics(q)
    
    print(f"左臂当前位置: {Tl.translation}")
    print(f"右臂当前位置: {Tr.translation}")
    
    # 目标位置 - 原始问题中的目标
    target_pos = np.array([0.52359975, 0.07663266, 0.97884876])
    print(f"左臂目标位置: {target_pos}")
    
    # 创建左臂目标姿态
    Tl_goal = Tl.copy()
    Tl_goal.translation = target_pos
    Tr_goal = Tr.copy()
    Tr_goal.translation += np.array([0.0, 0.0, 0.1])
    
    print("\n==== 测试3: 使用scipy求解 ====")
    # 测试各种优化方法
    methods = ['Powell']
    for method in methods:
        print(f"\n使用 {method} 方法:")
        q_scipy, success_scipy = kinematics.inverse_kinematics_with_scipy(
            Tl_goal, Tr_goal, q, method=method, max_iter=5000, tol=1e-6
        )
        if success_scipy:
            Tl_new, Tr_new = kinematics.forward_kinematics(q_scipy)
            left_error = np.linalg.norm(Tl_new.translation - target_pos)
            right_error = np.linalg.norm(Tr_new.translation - Tr_goal.translation)
            print(f"scipy {method}求解成功! 左臂误差: {left_error:.6f}m, 右臂误差: {right_error:.6f}m")
            print(f"新左臂位置: {Tl_new.translation}")
            print(f"新右臂位置: {Tr_new.translation}")
        else:
            print(f"scipy {method}求解失败!")

def test_single_arm_ik():
    """测试仅针对单个手臂的IK求解"""
    urdf_path = "/home/<USER>/workspace/lerobot/lerobot/common/robot_devices/robots/A2D_Omnipicker/A2D.urdf"
    print("初始化双臂运动学对象...")
    kinematics = DualArmKinematics(urdf_path)
    
    # 设置初始关节角度
    q = kinematics.q0.copy()
    q[9:16] = ARM_RESET_POSITION[:7]
    q[24:31] = ARM_RESET_POSITION[7:]
    
    print("计算初始位姿的正向运动学...")
    Tl, Tr = kinematics.forward_kinematics(q)
    
    print(f"左臂当前位置: {Tl.translation}")
    print(f"右臂当前位置: {Tr.translation}")
    
    # 目标位置 - 问题中的目标
    target_pos = np.array([0.52359975, 0.07663266, 0.97884876])
    print(f"左臂目标位置: {target_pos}")
    
    # 创建自定义单臂IK求解，保持另一臂不变
    from scipy.optimize import minimize
    
    def objective(q_arm):
        # 创建完整的关节角度向量，只改变左臂
        q_full = q.copy()
        q_full[9:16] = q_arm
        
        # 计算正向运动学
        kinematics.forward_kinematics(q_full)
        current_pos = kinematics.data.oMf[kinematics.left_ee_id].translation
        
        # 计算位置误差
        error = np.linalg.norm(current_pos - target_pos)
        return error
    
    # use casadi to solve
    import casadi as ca
    # import pinocchio as pin
    from pinocchio import casadi as cpin
    Tl_goal = Tl.copy()
    Tl_goal.translation = target_pos
    cmodel = cpin.Model(kinematics.model)
    cdata = cpin.Data(cmodel)
    q_arm = ca.MX.sym('q_arm', 7)
    q_full = q.copy()
    # q_full[9:16] = q_arm
    cpin.forwardKinematics(cmodel, cdata, q_arm)
    cpin.updateFramePlacements(cmodel, cdata)
    Tl_new = kinematics.data.oMf[kinematics.left_ee_id]
    error_tool = ca.Function('error_tool', [q_arm], [ca.log6(Tl_new.inverse() * Tl_goal.vector)])
    opti = ca.Opti()
    T = 10
    var_q_arm = [opti.variable(7) for _ in range(T + 1)]
    # running cost
    running_cost = 0
    for i in range(T):
        running_cost += ca.sumsqr(var_q_arm[i] - var_q_arm[i+1])
    
    # set constraints
    opti.subject_to(var_q_arm[0] == q[9:16])
    opti.subject_to(var_q_arm[-1] == q[9:16])
    # set objective
    opti.minimize(running_cost)
    # solve
    opti.solver('ipopt')
    try:
        sol = opti.solve_limited()
        sol_qs = [opti.value(var_q) for var_q in var_q_arm]
    except:
        print("ERROR in convergence, plotting debug info.")
        sol_qs = [opti.debug.value(var_q) for var_q in var_q_arm]
    print(sol_qs)


def test_single_arm_ik_with_pin():
    """测试仅针对单个手臂的IK求解"""
    urdf_path = "/home/<USER>/workspace/lerobot/lerobot/common/robot_devices/robots/A2D_Omnipicker/A2D.urdf"
    print("初始化双臂运动学对象...")
    kinematics = DualArmKinematics(urdf_path, display=True)
    
    # 设置初始关节角度
    q = kinematics.q0.copy()
    
    # print("计算初始位姿的正向运动学...")
    Tl, Tr = kinematics.forward_kinematics(q)

    time.sleep(5)
    
    print(f"左臂当前位置: {Tl.translation}")
    print(f"右臂当前位置: {Tr.translation}")
    
    # 目标位置 - 问题中的目标
    target_pos = np.array([0.52359975, 0.07663266, 0.97884876])
    import pinocchio as pin
    print(f"左臂目标位置: {target_pos}")
    IT_MAX = 20000
    eps = 1e-4
    damp = 1e-3
    DT = 0.01
    # q[9:16] = ARM_RESET_POSITION[:7]
    # q[24:31] = ARM_RESET_POSITION[7:]
    # pin.forwardKinematics(kinematics.model, kinematics.data, q)

    Tl_new = kinematics.data.oMf[kinematics.left_ee_id]
    Tr_new = kinematics.data.oMf[kinematics.right_ee_id]
    # Tl_new = kinematics.data.oMf[kinematics.left_ee_id]
    Tl_goal = Tl_new.copy()
    Tl_goal.translation = target_pos
    i = 0
    while True:
        pin.forwardKinematics(kinematics.model, kinematics.data, q)
        iMd = kinematics.data.oMi[kinematics.left_ee_id].actInv(Tl_goal)
        err = pin.log(iMd).vector  # in joint frame
        if np.linalg.norm(err) < eps:
            success = True
            break
        if i >= IT_MAX:
            success = False
            break
        J = pin.computeJointJacobian(kinematics.model, kinematics.data, q,kinematics.left_ee_id)  # in joint frame
        J_left_arm = J[:, 9:16]
        J_left_arm = -np.dot(pin.Jlog6(iMd.inverse()), J_left_arm)
        v = -J_left_arm.T.dot(np.linalg.solve(J_left_arm.dot(J_left_arm.T) + damp * np.eye(6), err))
        v_full = np.zeros(kinematics.model.nv)
        v_full[9:16] = v
        q = pin.integrate(kinematics.model, q, v_full * DT)
        if not i % 10:
            print("%d: error = %s" % (i, err.T))
        i += 1

    if success:
        print("\nConvergence achieved!")
        Tl, Tr = kinematics.forward_kinematics(q)
    else:
        print("\n" "Warning: the iterative algorithm has not reached convergence " "to the desired precision")


if __name__ == "__main__":
    # test_a2d_robot()
    print("==== 标准IK测试 ====")
    test_a2d_kinematics()
    print("\n==== 新的IK方法测试 ====")
    # test_new_ik_methods()
    print("\n==== 单臂优化测试 ====")
    # test_single_arm_ik()
    # test_single_arm_ik_with_pin()