import os

import casadi
import numpy as np
from casadi import SX, vertcat, dot
import pinocchio as pin
import pinocchio.casadi as cpin
from pinocchio.visualize import MeshcatVisualizer


class OptimalControlProblem:
    def __init__(self, model, data, dt, T, cost_func):
        self.model = model
        self.data = data
        self.dt = dt
        self.T = T
        self.nx = model.nq + model.nv
        self.nu = model.nv  # 使用 torque 控制
        self.cost_func = cost_func

        self.build()

    def build(self):
        # 状态与控制符号变量
        x = SX.sym("x", self.nx)
        u = SX.sym("u", self.nu)

        q = x[:self.model.nq]
        v = x[self.model.nq:]

        # 关节力矩为控制输入
        tau = u
        a = cpin.aba(self.model, self.data, q, v, tau)

        dq = v * self.dt + a * self.dt ** 2
        dv = a * self.dt
        q_next = cpin.integrate(self.model, q, dq)
        v_next = v + dv
        x_next = vertcat(q_next, v_next)

        # 动力学函数
        self.dyn = casadi.Function("dyn", [x, u], [x_next])

        # MPC 优化变量
        self.X = casadi.MX.sym("X", self.nx, self.T + 1)
        self.U = casadi.MX.sym("U", self.nu, self.T)

        # 状态目标（参数化）
        self.x_goal = casadi.MX.sym("x_goal", self.nx)

        # cost 累加
        cost = 0
        for t in range(self.T):
            cost += self.cost_func(self.model, self.X[:, t], self.x_goal)
            x_next_pred = self.dyn(self.X[:, t], self.U[:, t])
            cost += 1e-4 * dot(self.U[:, t], self.U[:, t])  # 控制正则项

        # Terminal cost
        cost += self.cost_func(self.model, self.X[:, self.T], self.x_goal)

        # dynamics constraints
        g = []
        for t in range(self.T):
            g.append(self.X[:, t + 1] - self.dyn(self.X[:, t], self.U[:, t]))

        g = casadi.vertcat(*g)

        opt_vars = casadi.vertcat(
            casadi.reshape(self.X, -1, 1),
            casadi.reshape(self.U, -1, 1)
        )

        self.nlp = {
            "x": opt_vars,
            "f": cost,
            "g": g,
            "p": self.x_goal,
        }

        self.solver = casadi.nlpsol("solver", "ipopt", self.nlp, {
            "ipopt.print_level": 0,
            "print_time": 0,
            "ipopt.max_iter": 1000
        })

    def solve(self, x0, x_goal):
        nx = self.nx
        nu = self.nu

        # 初始化状态轨迹和控制轨迹
        X0 = casadi.DM.zeros((nx, self.T + 1))
        U0 = casadi.DM.zeros((nu, self.T))
        for t in range(self.T + 1):
            X0[:, t] = x0

        lbx = []
        ubx = []
        for _ in range(self.T + 1):
            lbx.extend([-casadi.inf] * nx)
            ubx.extend([casadi.inf] * nx)
        for _ in range(self.T):
            lbx.extend([-10.0] * nu)
            ubx.extend([10.0] * nu)

        lbg = [0.0] * (self.T * nx)
        ubg = [0.0] * (self.T * nx)

        sol = self.solver(
            x0=casadi.vertcat(casadi.reshape(X0, -1, 1), casadi.reshape(U0, -1, 1)),
            lbx=lbx,
            ubx=ubx,
            lbg=lbg,
            ubg=ubg,
            p=x_goal
        )

        w_opt = sol["x"]
        X_opt = w_opt[:nx * (self.T + 1)].reshape((nx, self.T + 1))
        U_opt = w_opt[nx * (self.T + 1):].reshape((nu, self.T))
        return X_opt, U_opt
    

class DualArmKinematics:
    def __init__(self, urdf_path, eef_type="gripper", display=False):
        mesh_dir = os.path.dirname(urdf_path)
        model, collision_model, visual_model = pin.buildModelsFromUrdf(
            urdf_path, mesh_dir, pin.JointModelFreeFlyer()
        )
        self.model = model
        self.data = self.model.createData()
        self.q0 = pin.neutral(self.model)
        self.left_ee_name = "left_base_link"
        self.right_ee_name = "right_base_link"
        self.left_ee_id = self.model.getFrameId(self.left_ee_name)
        self.right_ee_id = self.model.getFrameId(self.right_ee_name)
        self.display = display
        self.viz = None
        if display:
            try:
                self.viz = MeshcatVisualizer(model, collision_model, visual_model)
                self.viz.initViewer(open=True)
                self.viz.loadViewerModel()
                self.viz.display(self.q0)
                self.viz.displayVisuals(True)
            except ImportError as err:
                print(
                    "Error while initializing the viewer. "
                    "It seems you should install Python meshcat"
                )
                print(err)
        
    def forward_kinematics(self, q=None):
        if q is None:
            q = self.q0
        pin.forwardKinematics(self.model, self.data, q)
        pin.updateFramePlacements(self.model, self.data)

        Tl = self.data.oMf[self.left_ee_id]
        Tr = self.data.oMf[self.right_ee_id]
        if self.viz is not None:
            self.viz.display(q)
        return Tl, Tr

    def inverse_kinematics_single_arm(self, T_goal, arm_id, 
                                      q_init=None, max_iter=200, tol=1e-4,
                                      damping=1e-3, DT=0.01):
        # 初始化配置
        q = q_init.copy() if q_init is not None else self.q0.copy()
        
        for i in range(max_iter):
            # 前向运动学计算
            pin.forwardKinematics(self.model, self.data, q)
            iMd = self.data.oMi[arm_id].actInv(T_goal)
            err = pin.log(iMd).vector
            # 获取雅可比矩阵
            J = pin.computeFrameJacobian(self.model, self.data, q, arm_id)

            if np.linalg.norm(err) < tol:
                return q, True
            
            J = pin.computeJointJacobian(self.model, self.data, q, arm_id)  # in joint frame
            J = -np.dot(pin.Jlog6(iMd.inverse()), J)
            v = -J.T.dot(np.linalg.solve(J.dot(J.T) + damping * np.eye(6), err))
            q = pin.integrate(self.model, q, v * DT)
        
        return q, False
    
    def inverse_kinematics_with_scipy(self, T_left_goal, T_right_goal, q_init=None, method='Powell', max_iter=5000, tol=1e-4):
        """使用scipy优化器求解IK，基于成功的单臂测试结果"""
        try:
            from scipy.optimize import minimize
        except ImportError:
            print("scipy未安装，请使用pip install scipy安装")
            return None, False
            
        if q_init is None:
            q = self.q0.copy()
        else:
            q = q_init.copy()
            
        # 仅优化左右手臂关节，保持其他关节不变
        left_joints = list(range(9, 16))   # 左臂关节索引
        right_joints = list(range(24, 31)) # 右臂关节索引
        
        # 记录初始状态，用于恢复非优化关节
        q_original = q.copy()
        
        # 获取初始关节角度作为初始猜测
        q0_arms = np.concatenate([q[left_joints], q[right_joints]])
        
        def objective(q_arms):
            """优化目标函数，计算当前位姿与目标位姿的误差"""
            # 恢复完整关节向量
            q_full = q_original.copy()
            q_full[left_joints] = q_arms[:len(left_joints)]
            q_full[right_joints] = q_arms[len(left_joints):]
            
            # 计算正向运动学
            pin.forwardKinematics(self.model, self.data, q_full)
            pin.updateFramePlacements(self.model, self.data)
            
            Tl = self.data.oMf[self.left_ee_id]
            Tr = self.data.oMf[self.right_ee_id]
            
            # 计算位置和姿态误差
            err_l = pin.log(Tl.inverse() * T_left_goal).vector
            err_r = pin.log(Tr.inverse() * T_right_goal).vector
            
            # 分别权重位置和姿态误差
            pos_weight = 1.0
            rot_weight = 0.8
            
            err_l_weighted = np.concatenate([err_l[:3] * pos_weight, err_l[3:] * rot_weight])
            err_r_weighted = np.concatenate([err_r[:3] * pos_weight, err_r[3:] * rot_weight])
            
            # 总误差
            total_error = np.sum(err_l_weighted**2) + np.sum(err_r_weighted**2)
            return total_error
        
        # 使用scipy优化器求解
        result = minimize(objective, q0_arms, method=method, 
                          options={'maxiter': max_iter, 'disp': False, 'ftol': tol})
        
        if result.success:
            # 应用优化结果
            q_result = q_original.copy()
            q_result[left_joints] = result.x[:len(left_joints)]
            q_result[right_joints] = result.x[len(left_joints):]
            
            # 验证结果
            pin.forwardKinematics(self.model, self.data, q_result)
            pin.updateFramePlacements(self.model, self.data)
            
            if self.viz is not None:
                self.viz.display(q_result)
                
            return q_result, True
        else:
            print(f"优化失败，迭代次数: {result.nit}, 函数评估次数: {result.nfev}")
            return q, False
        
    def casadi_forward_kinematics(self, q_sym, frame_name):
        frame_id = self.model.getFrameId(frame_name)
        print(f"frame_id: {frame_id}")
        ca_model = cpin.Model(self.model)
        ca_data = ca_model.createData()
        
        # Convert numpy array to CasADi symbolic if needed
        if isinstance(q_sym, np.ndarray):
            q_sym = casadi.SX(q_sym)
            
        cpin.forwardKinematics(ca_model, ca_data, q_sym)
        cpin.updateFramePlacement(ca_model, ca_data, frame_id)
        return ca_data.oMf[frame_id]

    def casadi_inverse_kinematics(self, T_goal, frame_name, joint_idx, q_init=None):
        """
        CasADi-based inverse kinematics for a single arm (partial joint optimization).
        
        Args:
            T_goal: pin.SE3 target transform.
            frame_name: target end-effector frame.
            joint_idx: list of indices to optimize (e.g., [9,10,11,12,13,14,15] for left arm).
            q_init: full configuration vector (optional).
        """
        nq = self.model.nq
        q_init = q_init if q_init is not None else pin.neutral(self.model)
        q_fixed = q_init.copy()  # full q for reconstruction

        q_arm = SX.sym("q_arm", len(joint_idx))
        q_full = []
        j = 0
        for i in range(nq):
            if i in joint_idx:
                q_full.append(q_arm[j])
                j += 1
            else:
                q_full.append(q_fixed[i])
        q_full = vertcat(*q_full)

        # Forward kinematics
        T = self.casadi_forward_kinematics(q_full, frame_name)
        R_err = T.rotation.T @ T_goal.rotation
        t_err = T.translation - T_goal.translation
        rot_cost = dot(casadi.reshape(R_err - np.eye(3), -1, 1),
                    casadi.reshape(R_err - np.eye(3), -1, 1))
        pos_cost = dot(t_err, t_err)

        cost = pos_cost + 0.1 * rot_cost
        nlp = {"x": q_arm, "f": cost}
        solver = casadi.nlpsol("ik_solver", "ipopt", nlp)

        q0_arm = q_init[joint_idx]
        sol = solver(x0=q0_arm)

        q_solution = q_fixed.copy()
        q_solution[joint_idx] = sol["x"].full().flatten()
        return q_solution
    
    def casadi_inverse_kinematics_dual_arm(self, T_left_goal, T_right_goal, 
                                       frame_names, joint_indices, 
                                       q_init=None, joint_limits=None):
        """
        CasADi inverse kinematics solving only for left/right arm joints with cost and limits.

        Args:
            T_left_goal: SE3, desired transform of left ee.
            T_right_goal: SE3, desired transform of right ee.
            frame_names: tuple of (left_frame_name, right_frame_name)
            joint_indices: tuple of (left_indices, right_indices), list of joint indices to optimize
            q_init: initial full configuration
            joint_limits: tuple of (q_lower, q_upper) for those joint indices
        """
        nq = self.model.nq
        q_init = q_init if q_init is not None else pin.neutral(self.model)
        q_fixed = q_init.copy()

        # Unpack joint groups
        left_idx, right_idx = joint_indices
        all_idx = left_idx + right_idx

        q_var = SX.sym("q_arm", len(all_idx))

        # Build full q from subset
        q_full = []
        j = 0
        for i in range(nq):
            if i in all_idx:
                q_full.append(q_var[j])
                j += 1
            else:
                q_full.append(q_fixed[i])
        q_full = vertcat(*q_full)

        # Forward kinematics for both arms
        T_left = self.casadi_forward_kinematics(q_full, frame_names[0])
        T_right = self.casadi_forward_kinematics(q_full, frame_names[1])

        def pose_error(T, T_goal):
            R_err = T.rotation.T @ T_goal.rotation
            t_err = T.translation - T_goal.translation
            rot_cost = dot(casadi.reshape(R_err - np.eye(3), -1, 1),
                        casadi.reshape(R_err - np.eye(3), -1, 1))
            pos_cost = dot(t_err, t_err)
            return pos_cost + 0.1 * rot_cost

        cost = pose_error(T_left, T_left_goal) + pose_error(T_right, T_right_goal)

        # NLP definition
        nlp = {"x": q_var, "f": cost}

        solver = casadi.nlpsol("ik_solver", "ipopt", nlp, {
            "ipopt.print_level": 0,
            "print_time": 0,
        })

        q0_var = q_init[all_idx]
        sol = solver(x0=q0_var)
        q_sol = q_fixed.copy()
        q_sol[all_idx] = sol["x"].full().flatten()
        return q_sol


def multi_eef_pose_cost(model, x, x_goal):
    q = x[:model.nq]
    q_goal = x_goal[:model.nq]
    ca_model = cpin.Model(model)
    ca_data = ca_model.createData()
    T_left = cpin.updateFramePlacement(ca_model, ca_data, model.getFrameId("left_ee"), q)
    T_left_goal = cpin.updateFramePlacement(ca_model, ca_data, model.getFrameId("left_ee"), q_goal)
    T_right = cpin.updateFramePlacement(ca_model, ca_data, model.getFrameId("right_ee"), q)
    T_right_goal = cpin.updateFramePlacement(ca_model, ca_data, model.getFrameId("right_ee"), q_goal)
    err_l = pin.log(T_left.inverse() * T_left_goal).vector
    err_r = pin.log(T_right.inverse() * T_right_goal).vector
    return dot(err_l, err_l) + dot(err_r, err_r)


if __name__ == "__main__":
    import time
    ARM_RESET_POSITION = [-1.074169635772705, 0.6115003228187561, 0.2802993357181549, -1.2832905054092407, 0.729724109172821, 1.495517373085022, -0.18664519488811493, 1.0692312717437744, -0.606038510799408, -0.2755005955696106, 1.2892932891845703, -0.7303174138069153, -1.492480993270874, 0.18664519488811493]
    urdf_path = "/home/<USER>/workspace/lerobot/lerobot/common/robot_devices/robots/A2D_Omnipicker/A2D.urdf"
    kinematics = DualArmKinematics(urdf_path, display=True)
    q = kinematics.q0.copy()
    Tl, Tr = kinematics.forward_kinematics(q)
    print(Tl)
    print(Tr)
    res = kinematics.casadi_forward_kinematics(q, "left_base_link")
    print(res)
    # inverse kinematics
    q_init = pin.neutral(kinematics.model)
    q_init[9:16] = ARM_RESET_POSITION[:7]
    q_init[24:31] = ARM_RESET_POSITION[7:]
    Tl, Tr  = kinematics.forward_kinematics(q_init)
    tl_goal = Tl.copy()
    tl_goal.translation += np.array([0.2, 0.0, 0.0])
    tr_goal = Tr.copy()
    tr_goal.translation += np.array([0.1, 0.0, 0.0])
    joint_idx = list(range(9, 16)) + list(range(24, 31))
    # q_res = kinematics.casadi_inverse_kinematics(tl_goal, "left_base_link", joint_idx, q_init)
    q_res = kinematics.casadi_inverse_kinematics_dual_arm(tl_goal, tr_goal, ("left_base_link", "right_base_link"), (joint_idx, joint_idx), q_init)
    print(q_res)
    time.sleep(5)
    kinematics.forward_kinematics(q_res)
    Tl_res = kinematics.data.oMf[kinematics.left_ee_id]
    print(Tl_res)
